package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户当日测试提醒统计表
 */
@Getter
@Setter
@TableName("sys_notification_testing_statistics")
public class SysNotificationTestingStatisticsEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private Long e3gFirstReminderTime;

    private String e3gReminderTimes;

    private Long pdgFirstReminderTime;

    private String pdgReminderTimes;

    private Long hcgFirstReminderTime;

    private String hcgReminderTimes;

    private Long product12FirstReminderTime;

    private String product12ReminderTimes;

    private Long product14FirstReminderTime;

    private String product14ReminderTimes;

    private Long product16FirstReminderTime;

    private String product16ReminderTimes;

    private String extraInfo;
}
