package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserBindEntity;
import com.mira.user.dal.mapper.AppUserBindMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user_bind DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserBindDAO extends ServiceImpl<AppUserBindMapper, AppUserBindEntity> {
    public AppUserBindEntity getByUserIdAndBindDevice(Long userId, String bindDevice) {
        return getOne(Wrappers.<AppUserBindEntity>lambdaQuery()
                .eq(AppUserBindEntity::getUserId, userId)
                .eq(AppUserBindEntity::getBindDevice, bindDevice)
                .last("limit 1"));
    }
}
