package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户拥有哪些试剂，以及数量
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-12-12
 **/
@Getter
@Setter
@TableName("app_user_wand_count")
public class AppUserWandCountEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * Mira Fertility Plus Wands
     */
    private Integer plus;

    /**
     * Mira Fertility Confirm Wands
     */
    private Integer confirm;

    /**
     * Mira Max Wands
     */
    private Integer max;

    /**
     * Mira Ovum Wands
     */
    private Integer ovum;
}
