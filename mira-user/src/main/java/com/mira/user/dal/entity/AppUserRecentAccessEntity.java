package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户最近access记录表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_recent_access")
public class AppUserRecentAccessEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * ip
     */
    private String ip;

    /**
     * iOS/android
     */
    private String os;

    /**
     * 手机系统版本
     */
    private String version;

    /**
     * 手机机型
     */
    private String device;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 网络类型
     */
    private String networkType;
}
