package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * app和shopify绑定状况
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_shopify_bind")
public class AppShopifyBindEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户绑定的email
     */
    private String bindEmail;

    /**
     * Main store是否绑定账号:0否,1是
     */
    private Integer usStore;

    /**
     * Australia store是否绑定账号:0否,1是
     */
    private Integer auStore;

    /**
     * Canada store是否绑定账号:0否,1是
     */
    private Integer caStore;

    /**
     * European store是否绑定账号:0否,1是
     */
    private Integer euStore;

    /**
     * UK store是否绑定账号:0否,1是
     */
    private Integer ukStore;

    /**
     * Test store是否绑定账号:0否,1是
     */
    private Integer testStore;
}
