package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppAgreementEntity;
import com.mira.user.dal.mapper.AppAgreementMapper;
import org.springframework.stereotype.Repository;

/**
 * app_agreement DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppAgreementDAO extends ServiceImpl<AppAgreementMapper, AppAgreementEntity> {
    public AppAgreementEntity getByTypeAndLocalLanaguage(Integer type, String localLanaguage) {
        return getOne(Wrappers.<AppAgreementEntity>lambdaQuery()
                .eq(AppAgreementEntity::getType, type)
                .eq(AppAgreementEntity::getLocalLanguage, localLanaguage));
    }
}
