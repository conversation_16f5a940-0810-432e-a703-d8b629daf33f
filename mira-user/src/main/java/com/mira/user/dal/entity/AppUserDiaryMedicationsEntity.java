package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户每日用药记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_diary_medications")
public class AppUserDiaryMedicationsEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 日记时间戳,年月日（不带时分秒）
     */
    private Long diaryDay;

    /**
     * 日记时间（年月日）
     */
    private String diaryDayStr;

    /**
     * 用药记录jsonArray
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String medications;
}
