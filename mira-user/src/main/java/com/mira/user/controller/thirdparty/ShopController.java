package com.mira.user.controller.thirdparty;

import com.mira.user.service.shop.IShopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Shop商店
 *
 * <AUTHOR>
 */
@Api(tags = "23.Shop商店")
@RestController
@RequestMapping("/app/v4/shop")
public class ShopController {
    @Resource
    private IShopService shopService;

    @ApiOperation("获取用户当前货币")
    @GetMapping("/currency")
    public String currency() {
        return shopService.currency();
    }

    @ApiOperation("修改用户当前货币")
    @PostMapping("/currency/change")
    public void changeCurrency(@RequestParam String currency) {
        shopService.changeCurrency(currency);
    }
}
