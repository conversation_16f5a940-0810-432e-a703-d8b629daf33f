package com.mira.user.controller.survey;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.mongo.dto.SurveyAnswerDTO;
import com.mira.api.mongo.dto.SurveyBaseDTO;
import com.mira.api.mongo.dto.SurveyCondition;
import com.mira.api.mongo.dto.SurveyDTO;
import com.mira.api.mongo.provider.ISurveyProvider;
import com.mira.core.annotation.Idempotent;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.user.dto.survey.SurveyAnswerSaveDTO;
import com.mira.user.service.survey.ISurveyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-03-25
 **/
@Api(tags = "17.survey")
@RestController
@RequestMapping("/app/v5/survey")
public class SurveyController {
    @Resource
    private ISurveyProvider surveyProvider;
    @Resource
    private ISurveyService surveyService;

    @ApiOperation("1.获取survey id")
    @GetMapping("/getId")
    public SurveyBaseDTO getId() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        //1. 获取所有可能出现的survey, 是否激活，时间是否在范围内，是否已经回答过，是否view了3次
        //2. conditions在user服务中判断。
        //3. 选取最近的一个，(或者随便选一个)
        List<SurveyDTO> surveyDTOS = surveyProvider.listSurveyByUserId(userId, timeZone).getData();

        for (SurveyDTO surveyDTO : surveyDTOS) {
            Object conditions = surveyDTO.getConditions();
            String conditionsStr = JsonUtil.toJson(conditions);
            SurveyCondition surveyCondition = JsonUtil.toObject(conditionsStr, SurveyCondition.class);

            boolean checkConditions = surveyService.checkSurveyConditions(userId, surveyCondition);
            if (!checkConditions) {
                continue;
            }

            //如果conditions都满足，那么返回这个。
            SurveyBaseDTO surveyBaseDTO = new SurveyBaseDTO();
            surveyBaseDTO.setId(surveyDTO.getId());
            surveyBaseDTO.setName(surveyDTO.getName());
            surveyBaseDTO.setBanner(surveyDTO.getBanner());
            return surveyBaseDTO;
        }

        return null;
    }

    @ApiOperation("2.通过id获取survey")
    @GetMapping("/{id}")
    public SurveyDTO getSurveyById(@PathVariable String id) {
        return surveyProvider.getSurveyById(id).getData();
    }

    @ApiOperation("3.保存survey答案")
    @Idempotent
    @PostMapping("/save-answer")
    public void saveSurveyAnswer(@RequestBody SurveyAnswerSaveDTO surveyAnswerSaveDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        SurveyAnswerDTO surveyAnswerDTO = new SurveyAnswerDTO();
        BeanUtil.copyProperties(surveyAnswerSaveDTO, surveyAnswerDTO);
        surveyAnswerDTO.setUserId(userId);
        surveyAnswerDTO.setTimeZone(timeZone);
        surveyProvider.saveSurveyAnswer(surveyAnswerDTO);
    }

    @ApiOperation("4.点击查看survey")
    @GetMapping("/view/{id}")
    public void viewSurvey(@PathVariable String id) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        surveyProvider.viewSurvey(id, userId, timeZone);
    }
}
