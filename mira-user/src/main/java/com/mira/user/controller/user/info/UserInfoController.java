package com.mira.user.controller.user.info;

import com.mira.api.message.dto.PushTokenDTO;
import com.mira.core.annotation.Anonymous;
import com.mira.user.controller.vo.user.UserAvgParamVO;
import com.mira.user.controller.vo.user.UserGoalScheduleOptionVO;
import com.mira.user.dto.info.*;
import com.mira.user.dto.schedule.GoalTestingScheduleDTO;
import com.mira.user.enums.user.EditGoalFlagEnum;
import com.mira.user.service.user.IUserInfoService;
import com.mira.user.service.user.IUserOnboardingPageViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户信息控制器
 *
 * <AUTHOR>
 */
@Api(tags = "03.用户信息")
@RestController
@RequestMapping("/app/v4/info")
public class UserInfoController {
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IUserOnboardingPageViewService userOnboardingPageViewService;

    @ApiOperation("step1: 保存用户info信息中的birthday")
    @PostMapping("/save/birthday")
    public void saveBirthday(@RequestBody UserBirthdayDTO userBirthdayDTO) {
        userInfoService.saveBirthday(userBirthdayDTO);
    }

    @ApiOperation("step2: 保存用户平均经期长度")
    @PostMapping("/save/period-length")
    public void savePeriodLength(@RequestBody UserPeriodLengthDTO userPeriodLengthDTO) {
        userInfoService.savePeriodLength(userPeriodLengthDTO);
    }

    @ApiOperation("step3: 编辑用户最近经期")
    @PostMapping("/save/period")
    public void savePeriod(@RequestBody List<String> dates) {
        userInfoService.savePeriod(dates);
    }

    @ApiOperation("step4: 保存用户平均周期长度")
    @PostMapping("/save/cycle-length")
    public void saveCycleLength(@RequestBody UserCycleLengthDTO userCycleLengthDTO) {
        userInfoService.saveCycleLength(userCycleLengthDTO);
    }

    @ApiOperation("step5：保存用户info信息中的goal")
    @PostMapping("/save/goal")
    public void saveGoal(@RequestBody UserGoalDTO userGoalDTO) {
        userInfoService.saveGoal(userGoalDTO);
    }

    @ApiOperation(value = "step6：保存用户info信息中的conditions", notes = "该接口只会在注册时调一次")
    @PostMapping("/save/conditions")
    public void saveConditions(@RequestBody UserConditionsDTO userConditionsDTO) {
        userInfoService.saveConditions(userConditionsDTO);
    }

    @ApiOperation(value = "step7：保存用户info信息中的节育方式", notes = "该接口只会在注册时调一次")
    @PostMapping("/save/hormonal-birth-control")
    public void saveHormonalBirthControl(@RequestBody UserHormonalBirthControlDTO userHormonalBirthControlDTO) {
        userInfoService.saveHormonalBirthControl(userHormonalBirthControlDTO);
    }

    @ApiOperation("获取goal和testing schedule的备选项")
    @GetMapping("/goal-schedule/option")
    public UserGoalScheduleOptionVO getGoalScheduleOption() {
        return userInfoService.getGoalScheduleOption();
    }

    @ApiOperation("修改用户info信息中的name")
    @PostMapping("/edit/name")
    public void editName(@RequestBody UserNameDTO userNameDTO) {
        userInfoService.editName(userNameDTO);
    }

    /**
     * @param goalTestingScheduleDTO
     * @param removePregnantInfo     移除怀孕信息标识，1表示移除，0或者null表示不处理
     */
    @ApiOperation("修改用户info信息中的goal")
    @PostMapping("/edit/goal/v2")
    public void editGoal(@RequestBody GoalTestingScheduleDTO goalTestingScheduleDTO, @RequestParam(
            value = "removePregnantInfo", required = false) Integer removePregnantInfo) {
        userInfoService.editGoal(goalTestingScheduleDTO, removePregnantInfo, EditGoalFlagEnum.EDIT_GOAL_FROM_CLIENT);
    }

    @ApiOperation("修改用户info信息中的birthday")
    @PostMapping("/edit/birthday")
    public void editBirthday(@RequestBody UserBirthdayDTO userBirthdayDTO) {
        userInfoService.editBirthday(userBirthdayDTO);
    }

    @ApiOperation("修改用户info信息中的conditions")
    @PostMapping("/edit/conditions")
    public void editConditions(@RequestBody UserConditionsDTO userConditionsDTO) {
        userInfoService.editConditions(userConditionsDTO);
    }

    @ApiOperation("修改用户info信息中的节育方式")
    @PostMapping("/edit/hormonal-birth-control")
    public void editHormonalBirthControl(@RequestBody UserHormonalBirthControlDTO userHormonalBirthControlDTO) {
        userInfoService.editHormonalBirthControl(userHormonalBirthControlDTO);
    }

    @ApiOperation("修改用户info信息中的pushToken")
    @PostMapping("/edit/push-token")
    public void editPushToken(@RequestBody PushTokenDTO pushTokenDTO) {
        userInfoService.editPushToken(pushTokenDTO);
    }

    @ApiOperation("修改用户头像")
    @PostMapping("/edit/avatar")
    public void editAvatar(@RequestBody UserAvatarDTO userAvatarDTO) {
        userInfoService.editAvatar(userAvatarDTO);
    }

    @Anonymous
    @ApiOperation("获取注册用户的部分参数均值")
    @GetMapping("/avg-param")
    public UserAvgParamVO getAvgParam() {
        UserAvgParamVO userAvgParamVO = new UserAvgParamVO();
        userAvgParamVO.setAvgSysBirthYear(1998);
        userAvgParamVO.setAvgSysLenCycle(29);
        userAvgParamVO.setAvgSysLenPeriod(6);
        return userAvgParamVO;
    }

    /**
     * 是否怀孕弹窗选择positive，congratulations!popup to Confirm change mode。后台切换到怀孕模式
     */
    @ApiOperation("Confirm change mode并切换到怀孕模式")
    @PostMapping("/change-mode/pregnant")
    public void changeModePregnant() {
        userInfoService.changeModePregnant(EditGoalFlagEnum.CHANGE_MODE_TO_PREGNANT);
    }


    @ApiOperation("Confirm change mode并切换到上一个模式")
    @PostMapping("/change-mode/last-mode")
    public void changeModeLastMode() {
        userInfoService.changeModeLastMode(EditGoalFlagEnum.REMOVE_PREGNANT_FROM_DAILY_LOG, null);
    }

    @Deprecated
    @ApiOperation("获取onboarding-page-view")
    @GetMapping("/onboarding-page-view")
    public String getOnboardingPageView() {
        return userOnboardingPageViewService.getOnboardingPageView();
    }

    @ApiOperation("设置onboarding-page-view")
    @PostMapping("/onboarding-page-view")
    public void setOnboardingPageView(@RequestParam String pageView) {
        userOnboardingPageViewService.setOnboardingPageView(pageView);
    }
}
