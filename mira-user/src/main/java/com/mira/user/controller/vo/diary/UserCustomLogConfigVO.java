package com.mira.user.controller.vo.diary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户的日记选项配置")
public class UserCustomLogConfigVO {
    @ApiModelProperty("爱爱，默认true")
    private Boolean sex = true;

    @ApiModelProperty("日记，默认true")
    private Boolean notes = true;

    @ApiModelProperty("体温，默认true")
    private Boolean temperature = true;

    @ApiModelProperty("温度默认单位，默认值与国际化对应")
    private String tempUnit;

    @ApiModelProperty("体重，默认true")
    private Boolean weight = true;

    @ApiModelProperty("体重默认单位，默认值与国际化对应")
    private String weightUnit;

    @ApiModelProperty("心情，默认true")
    private Boolean mood = true;

    @ApiModelProperty("药物，默认true")
    private Boolean medications = true;

    @ApiModelProperty("怀孕测试，默认true")
    private Boolean pregnantTest = true;

    @ApiModelProperty("排卵测试，默认true")
    private Boolean ovulationTest = true;

    @ApiModelProperty("症状，默认true")
    private Boolean symptoms = true;

    @ApiModelProperty("Cervical Mucus，默认true")
    private Boolean cervicalMucus = true;

    @ApiModelProperty("Cervical Position，默认true")
    private Boolean cervicalPosition = true;

    @ApiModelProperty("Flow & Spotting，默认true")
    private Boolean spotting = true;

    @ApiModelProperty("Spotting，默认true")
    private Boolean singleSpotting = true;

    @ApiModelProperty("Period Other，默认true")
    private Boolean periodOther = true;

    @ApiModelProperty("Challenge with glucose control，默认true")
    private Boolean glucoseControl = true;
}
