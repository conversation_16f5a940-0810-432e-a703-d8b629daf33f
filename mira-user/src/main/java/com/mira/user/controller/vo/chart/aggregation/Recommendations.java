package com.mira.user.controller.vo.chart.aggregation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("Recommendations")
public class Recommendations {
    @ApiModelProperty("Title")
    private String title;

    @ApiModelProperty("Body")
    private String body;

    @ApiModelProperty("Picture")
    private String picture;

    @ApiModelProperty("Link")
    private String link;
}
