package com.mira.user.controller.vo.cycle;

import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("测试数据")
public class TestDataVO extends TestDataDTO {

    @ApiModelProperty("待审:0:无须审核；1:审核中")
    private Integer pending = 0;
}
