package com.mira.user.dto.schedule;

import com.mira.api.user.dto.user.TestingScheduleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("GoalTestingScheduleDTO")
public class GoalTestingScheduleDTO extends TestingScheduleDTO {
    @ApiModelProperty("用户目标，参考UserGoalEnum")
    private Integer goalStatus;
}
