package com.mira.user.dto.info;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-10-10
 **/
@Getter
@Setter
@ApiModel("编辑怀孕模式信息")
public class UserEditPregnantModeInfoDTO {
    @ApiModelProperty("手动选择的最后一个实周期的开始日")
    private String lastPeriodDate;

    @ApiModelProperty("用户自己输入的受孕第一天")
    private String conceptionDate;

    @ApiModelProperty("医生给的预测生产的日（预产期要大于受孕日）")
    private String dueDate;

    @ApiModelProperty("当前肚子里的小孩数")
    private Integer numberOfChildren;
}
