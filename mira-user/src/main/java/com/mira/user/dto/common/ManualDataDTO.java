package com.mira.user.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("手动添加测试数据")
public class ManualDataDTO {
    @ApiModelProperty("试剂完成时间")
    private String completeTime;

    @ApiModelProperty("试剂类型")
    private String testWandType;

    @ApiModelProperty("t1浓度值")
    private BigDecimal t1ConValue;

    @ApiModelProperty("t2浓度值")
    private BigDecimal t2ConValue;

    @ApiModelProperty("t3浓度值")
    private BigDecimal t3ConValue;

    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;
}
