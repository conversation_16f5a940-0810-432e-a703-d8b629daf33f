package com.mira.user.dto.common;

import com.mira.core.consts.enums.WandTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-08-19
 **/
@Getter
@Setter
@ApiModel("手动添加测试数据转化成hormone")
public class ManualHormoneDTO {
    @ApiModelProperty("hormone 测试结果数据时间戳")
    private String test_time;
    /**
     * @see WandTypeEnum
     */
    @ApiModelProperty("试剂类型")
    private Integer wand_type;

    @ApiModelProperty("测量值1")
    private Float value1;

    @ApiModelProperty("测量值2")
    private Float value2;

    @ApiModelProperty("测量值3")
    private Float value3;

    @ApiModelProperty("pending数据状态，0:未处理，1:正在处理，2:已处理，3:已同步，4:未添加")
    private Integer pendingStatus;
}
