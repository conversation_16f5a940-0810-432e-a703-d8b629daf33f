package com.mira.user.dto.diary;

import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Getter
@Setter
@ApiModel("编辑用户日记Symptoms请求参数")
public class UserDiarySymptomsDTO {
    @ApiModelProperty("时间，e.g: 2021-05-20")
    @NotBlank(message = "dateStr can not be empty.")
    private String dateStr;

    @ApiModelProperty("note内容")
    private List<UserSymptomDTO> symptoms;
}
