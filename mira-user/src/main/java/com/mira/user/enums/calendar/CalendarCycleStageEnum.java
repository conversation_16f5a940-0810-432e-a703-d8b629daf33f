package com.mira.user.enums.calendar;

import lombok.Getter;

/**
 * 日历页每日状态枚举
 */
@Getter
public enum CalendarCycleStageEnum {
    PERIOD_START("period_start", "经期第一天"),
    PERIOD("period", "经期"),
    PERIOD_END("period_end", "经期结束那一天"),
    PERIOD_ONE("period_one", "经期只有一天"),

    FW("fw", "易孕期"),
    FW_START("fw_start", "易孕期第一天"),
    FW_END("fw_end", "易孕期结束那一天（指当天）"),
    FW_ONE("fw_one", "易孕期只有一天"),

    OVU("ovu", "排卵日;Ovulation"),

    P_PERIOD_START("p_period_start", "预测经期第一天"),
    P_PERIOD("p_period", "预测经期"),
    P_PERIOD_END("p_period_end", "预测经期结束那一天"),
    P_PERIOD_ONE("p_period_one", "预测经期只有一天"),

    P_FW("p_fw", "预测易孕期"),
    P_FW_START("p_fw_start", "预测易孕期第一天"),
    P_FW_END("p_fw_end", "预测易孕期结束那一天（指当天）"),
    P_FW_ONE("p_fw_one", "预测易孕期只有一天"),
    P_OVU("p_ovu", "预测排卵日;预测Ovulation"),

    OTHERS(null, "其他;"),

    TTA_HIGH_START("tta_high_start", "TTA高风险"),
    TTA_HIGH("tta_high", "TTA高风险"),
    TTA_HIGH_END("tta_high_end", "TTA高风险"),
    TTA_HIGH_ONE("tta_high_one", "TTA高风险"),

    TTA_MEDIUM_START("tta_medium_start", "TTA中风险"),
    TTA_MEDIUM("tta_medium", "TTA中风险"),
    TTA_MEDIUM_END("tta_medium_end", "TTA中风险"),
    TTA_MEDIUM_ONE("tta_medium_one", "TTA中风险"),

    TTA_LOW_START("tta_low_start", "TTA低风险"),
    TTA_LOW("tta_low", "TTA低风险"),
    TTA_LOW_END("tta_low_end", "TTA低风险"),
    TTA_LOW_ONE("tta_low_one", "TTA低风险"),
    ;

    private final String value;
    private final String description;

    CalendarCycleStageEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}

