package com.mira.user.enums.user.onboarding;

import lombok.Getter;

/**
 * activity level
 *
 * <AUTHOR>
 */
@Getter
public enum OnboardingActivityLevelEnum {
    NOT_AT_ALL(0, "Not at all"),
    LESS_THAN_75_MINUTES_PER_WEEK(1, "Less than 75 minutes per week"),
    PER_WEEK_75_MINUTES(2, "75 minutes per week"),
    PER_WEEK_150_MINUTES(3, "150 minutes per week"),
    MORE_THAN_150_MINUTES_PER_WEEK(4, "More than 150 minutes per week"),
    NOT_SURE(5, "I’m not sure")
    ;

    private final Integer code;
    private final String description;

    OnboardingActivityLevelEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
