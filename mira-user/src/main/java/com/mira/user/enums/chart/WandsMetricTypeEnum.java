package com.mira.user.enums.chart;

import lombok.Getter;

/**
 * wands metric 统计枚举
 *
 * <AUTHOR>
 */
@Getter
public enum WandsMetricTypeEnum {
    AVG(0, "均值"),
    LOWER_5(1, "下5%"),
    UPPER_95(2, "上95%"),
    RATIO(3, "比例Ratio"),
    LOWER_15(4, "下15%"),
    UPPER_85(5, "上85%"),
    LOWER_3(6, "下3%"),
    UPPER_97(7, "上97%"),
    MIDDLE(8, "中位数")
    ;

    private final int type;
    private final String desc;

    WandsMetricTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
