package com.mira.user.enums.user;

import lombok.Getter;

/**
 * @program: mira_server_microservices
 * @description: 修改user mode的标记
 * @author: xizhao.dai
 * @create: 2024-04-25 10:07
 **/
@Getter
public enum EditGoalFlagEnum {
    EDIT_GOAL_FROM_CLIENT(0, "客户端选择修改用户模式（非怀孕模式相关)"),
    CHANGE_MODE_TO_PREGNANT(1, "将模式修改为怀孕模式"),
    REMOVE_PREGNANT_FROM_DAILY_LOG(2, "退出怀孕模式(从客户端daily log)"),
    REMOVE_PREGNANT_FROM_PREGNANT_SETTING(3, "退出怀孕模式(从客户端 pregnant setting)"),
    REMOVE_PREGNANT_FROM_EDIT_GOAL_REMOVE_INFO(4, "修改用户模式，退出怀孕模式，选择移除怀孕信息");

    private final Integer value;
    private final String description;

    EditGoalFlagEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
