package com.mira.user.enums.user;

import lombok.Getter;

/**
 * articles cycle type enum
 *
 * <AUTHOR>
 */
@Getter
public enum ArticlesCycleTypeEnum {
    FIRST_CYCLE(1, "第一个周期"),
    SECOND_CYCLE(2, "第二个周期"),
    OTHER_CYCLE(0, "其他周期")
    ;

    private final int type;
    private final String desc;

    ArticlesCycleTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
