package com.mira.user.enums.home;

import com.mira.user.enums.user.PregnantCodeEnum;
import lombok.Getter;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2023-12-27 13:40
 **/
@Getter
public enum HomeActionButtonCodeEnum {

    ONE(1, "Take a test with [X] wand today", "Take a test", "Testing flow"),
    TWO(2, "Did you start your period?", "Yes/No", "Yes: Calendar\n" +
            "No: Change to “How are you feeling today?” B: Log your symptoms"),
    THREE(3, "Time for a pregnancy test", "Log result", "Log symptoms"),
    //  deprecated 4
    FIVE(5, "How are you feeling today?", "Log your symptoms", "Log symptoms"),
    SIX(6, "How are you feeling today?", "Log your symptoms", "Log symptoms"),
    SEVEN(7, "What’s your fertility status?", "Start testing with Mira", "https://shop.miracare.com/products/fertility-max-starter-kit?utm_source=mira-app&utm_medium=action-button&utm_campaign=your-fertility-status-ab&utm_content=starter-kit-promotion"),
    EIGHT(8, "Understand your body with our Hormone Monitor \uD83D\uDC9B", "Grab yours", "https://shop.miracare.com/products/fertility-max-starter-kit?utm_source=mira-app&utm_medium=action-button&utm_campaign=non-analyzer-symptoms-logged-ab&utm_content=starter-kit-promotion"),
    NINE(9, "Great news! We've detected your LH peak \\uD83C\\uDF1F \"", "Why it’s important! -  link to LH article", "https://www.miracare.com/blog/lh-surge-before-period/?utm_source=mira-app&utm_medium=action-button&utm_campaign=lh-pea-ab&utm_content=lh-article"),
    TEN(10, "The more you test, the more our AI learns \uD83E\uDDE0", "How it works - link to article", "https://www.miracare.com/blog/mira-cycle-tracking-features/?utm_source=mira-app&utm_medium=action-button&utm_campaign=21-cd-ab&utm_content=tracking-article"),
    ELEVEN(11, "Test regularly with subscription and reach goals! \uD83E\uDE84", "Subscribe today& save 10%!", "https://shop.miracare.com/en-int/products/mira-max-test-wands?utm_source=mira-app&utm_medium=action-button&utm_campaign=15-test-performed-1-cycle-ab&utm_content=wands-subscription"),
    TWELVE(12, "Stay on schedule, subscribe to wands \uD83D\uDCC6", "Subscribe today", "https://shop.miracare.com/en-int/products/mira-max-test-wands?utm_source=mira-app&utm_medium=action-button&utm_campaign=7-days-before-period-1-cycle-ab&utm_content=wands-subscription"),
    THIRTEEN(13, "Test on schedule every time \uD83D\uDCC6", "Restock your wands", "https://shop.miracare.com/en-int/products/mira-max-test-wands?utm_source=mira-app&utm_medium=action-button&utm_campaign=7-days-before-period-ab&utm_content=wands-restock"),
    FOURTEEN(14, "Great job testing! Keep up & restock your wands", "Restock now", "https://shop.miracare.com/en-int/products/mira-max-test-wands?utm_source=mira-app&utm_medium=action-button&utm_campaign=10-test-performed-ab&utm_content=wands-restock"),
    FIFTEEN(15, "Congrats! Would you like to switch to pregnancy mode?", "", "Pregnancy mode"),
    SIXTEEN(16, "Stay consistent with wands \uD83D\uDCAB", "Order now", "https://shop.miracare.com/en-int/products/mira-max-test-wands?utm_source=mira-app&utm_medium=action-button&utm_campaign=analyzer-symptoms-logged-ab&utm_content=wands-subscription"),
    SEVENTEEN(17, "You’re gorgeous! Enjoy your day \uD83D\uDCAB", "", "Mira kit shop page"),
    EIGHTEEN(18, "How are you feeling today?", "Log your symptoms", "Log symptoms");

    private final Integer code;
    private final String content;
    private final String button;
    private final String open;

    HomeActionButtonCodeEnum(Integer code, String content, String button, String open) {
        this.code = code;
        this.content = content;
        this.button = button;
        this.open = open;
    }

    public static HomeActionButtonCodeEnum getEnumByCode(Integer code) {
        for (HomeActionButtonCodeEnum codeEnum : values()) {
            if (codeEnum.getCode().equals(code)) {
                return codeEnum;
            }
        }
        return null;
    }
}
