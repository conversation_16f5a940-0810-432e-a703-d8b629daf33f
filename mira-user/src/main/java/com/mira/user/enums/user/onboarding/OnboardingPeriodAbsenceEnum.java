package com.mira.user.enums.user.onboarding;

import lombok.Getter;

/**
 * period absence
 *
 * <AUTHOR>
 */
@Getter
public enum OnboardingPeriodAbsenceEnum {
    MENOPAUSE(0, "Menopause"),
    HORMONAL_IMBALANCE(1, "Hormonal imbalance"),
    PCOS(2, "PCOS"),
    PRIMARY_AMENORRHEA(3, "Primary amenorrhea (never had a period)"),
    SECONDARY_AMENORRHEA(4, "Secondary amenorrhea (stopped having periods)"),
    THYROID_DISORDERS(5, "Thyroid disorders"),
    STRESS_OR_EMOTIONAL_FACTORS(6, "Stress or emotional factors"),
    MEDICATIONS(7, "Medications"),
    HORMONAL_CONTRACEPTION(8, "Hormonal contraception"),
    LOW_BODY_FAT_PERCENTAGE(9, "Low body fat percentage"),
    POSTPARTUM_BREASTFEEDING(10, "Postpartum breastfeeding "),
    POST_HYSTERECTOMY(11, "Post-hysterectomy (with retained ovaries)"),
    UTERINE_ABLATION(12, "Uterine ablation"),
    IUD(13, "IUD"),
    PERIMENOPAUSE(14, "Perimenopause"),
    OTHERS(15, "Others")
    ;

    private final Integer code;
    private final String description;

    OnboardingPeriodAbsenceEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
