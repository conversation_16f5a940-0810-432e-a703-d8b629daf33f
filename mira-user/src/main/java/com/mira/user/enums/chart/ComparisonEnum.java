package com.mira.user.enums.chart;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * chat comparison type
 *
 * <AUTHOR>
 */
@Getter
public enum ComparisonEnum {
    // 1 healthy users
    HEALTHY_USERS_PDG(1, "pdg", 200, "Healthy Users"),
    HEALTHY_USERS_LH(1, "lh", 202, "Healthy Users"),
    HEALTHY_USERS_E3G(1, "e3g", 205, "Healthy Users"),

    // 2 pregnancy
    PREGNANCY_PDG(2, "pdg", 201, "Pregnant"),
    PREGNANCY_LH(2, "lh", 203, "Pregnant"),
    PREGNANCY_E3G(2, "e3g", 206, "Pregnant"),

    // 3 Hormone Imbalance

    // 4 Regular cycle
    REGULAR_CYCLE_FSH(4, "fsh", 208, "Regular cycle"),

    // 5 After miscarriage

    // 6 pcos
    PCOS_LH(6, "lh", 204, "PCOS"),
    PCOS_E3G(6, "e3g", 207, "PCOS"),

    // 7 Other conditions
    // 8 Previous cycle
    ;

    private final int groupCode;
    private final String wandType;
    private final int marker;
    private final String name;

    ComparisonEnum(int groupCode, String wandType, int marker, String name) {
        this.groupCode = groupCode;
        this.wandType = wandType;
        this.marker = marker;
        this.name = name;
    }

    public static List<ComparisonEnum> getByWandType(String wandType) {
        wandType = wandType.toLowerCase(Locale.ROOT);
        List<ComparisonEnum> result = new ArrayList<>();
        for (ComparisonEnum comparisonEnum : ComparisonEnum.values()) {
            if (comparisonEnum.getWandType().equals(wandType)) {
                result.add(comparisonEnum);
            }
        }
        return result;
    }

    public static List<ComparisonEnum> getByGroupCode(int groupCode) {
        List<ComparisonEnum> result = new ArrayList<>();
        for (ComparisonEnum comparisonEnum : ComparisonEnum.values()) {
            if (comparisonEnum.getGroupCode() == groupCode) {
                result.add(comparisonEnum);
            }
        }
        return result;
    }

    public static ComparisonEnum get(int groupCode, String wandType) {
        for (ComparisonEnum comparisonEnum : ComparisonEnum.values()) {
            if (comparisonEnum.getWandType().equals(wandType) && comparisonEnum.getGroupCode() == groupCode) {
                return comparisonEnum;
            }
        }
        return null;
    }
}
