package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.core.consts.enums.BiomarkerNameEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.NumberFormatUtil;
import com.mira.user.dal.dao.AppDataManualDAO;
import com.mira.user.dal.entity.AppDataManualEntity;
import com.mira.user.dto.common.ManualHormoneDTO;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 手动添加数据
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-08-19
 **/
@Slf4j
@Component
public class ManualDataManager {
    @Resource
    private AppDataManualDAO appDataManualDAO;
    @Resource
    private SysDictProperties sysDictProperties;

    //1. 查询所有未处理的手动添加数据，或者查询单个用户的未处理的手动添加数据
    //2. 转化成前端需要的数据结构
    //3. 组装到返回结果中
    public List<ManualHormoneDTO> getPendingDataByUserIdAndDate(Long userId, String date) {
        List<AppDataManualEntity> appDataManualEntities = appDataManualDAO.listPendingDataByUserId(userId);
        List<ManualHormoneDTO> manualHormoneDTOS = new ArrayList<>();
        for (AppDataManualEntity appDataManualEntity : appDataManualEntities) {
            String completeTime = appDataManualEntity.getCompleteTime();
            if (LocalDateUtil.minusToDay(completeTime, date) != 0) {
                continue;
            }
            ManualHormoneDTO manualHormoneDTO = getManualHormoneDTO(appDataManualEntity);
            manualHormoneDTOS.add(manualHormoneDTO);
        }
        return manualHormoneDTOS;
    }

    public List<ManualHormoneDTO> getPendingDatas(Long userId) {
        List<AppDataManualEntity> appDataManualEntities = appDataManualDAO.listPendingDataByUserId(userId);
        List<ManualHormoneDTO> manualHormoneDTOS = new ArrayList<>();
        for (AppDataManualEntity appDataManualEntity : appDataManualEntities) {
            ManualHormoneDTO manualHormoneDTO = getManualHormoneDTO(appDataManualEntity);
            manualHormoneDTOS.add(manualHormoneDTO);
        }
        return manualHormoneDTOS;
    }

    @NotNull
    private static ManualHormoneDTO getManualHormoneDTO(AppDataManualEntity appDataManualEntity) {
        ManualHormoneDTO manualHormoneDTO = new ManualHormoneDTO();
        // AppDataManualEntity --> ManualHormoneDTO
        manualHormoneDTO.setTest_time(appDataManualEntity.getCompleteTime());
        manualHormoneDTO.setWand_type(WandTypeEnum.get(appDataManualEntity.getTestWandType()).getInteger());
        manualHormoneDTO.setValue1(appDataManualEntity.getT1ConValue() == null ? null :
                appDataManualEntity.getT1ConValue().floatValue());
        manualHormoneDTO.setValue2(appDataManualEntity.getT2ConValue() == null ? null :
                appDataManualEntity.getT2ConValue().floatValue());
        manualHormoneDTO.setValue3(appDataManualEntity.getT3ConValue() == null ? null :
                appDataManualEntity.getT3ConValue().floatValue());
        manualHormoneDTO.setPendingStatus(appDataManualEntity.getStatus());
        return manualHormoneDTO;
    }

    public List<WandTestBiomarkerDTO> getPendingWandTestBiomarkerDTOS(Long userId, String date) {
        List<ManualHormoneDTO> manualHormoneDTOS = this.getPendingDataByUserIdAndDate(userId, date);
        if (CollectionUtils.isEmpty(manualHormoneDTOS)) {
            return new ArrayList<>();
        }
        List<WandTestBiomarkerDTO> wandTestDataList = new ArrayList<>();

        // 同一试剂棒标记
        Integer sameMark = ContextHolder.<Integer>get("sameWandMark");
        if (Objects.isNull(sameMark)) {
            sameMark = 0;
        }

        for (ManualHormoneDTO manualHormoneDTO : manualHormoneDTOS) {
            Integer wandType = manualHormoneDTO.getWand_type();

            switch (WandTypeEnum.get(wandType)) {
                case LH:
                    buildLh(manualHormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case E3G_LH:
                    buildE3gLh(manualHormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case PDG:
                    buildPdg(manualHormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case E3G_HIGH_RANGE:
                    buildE3gHighRange(manualHormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case LH_E3G_PDG:
                    buildLhE3gPdg(manualHormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case HCG:
                    buildHcg(manualHormoneDTO, wandTestDataList, userId, ++sameMark);
                    break;
                case HCG_QUALITATIVE:
                    buildHcgQualitative(manualHormoneDTO, wandTestDataList, ++sameMark);
                    break;
                case FSH:
                    buildFsh(manualHormoneDTO, wandTestDataList,  ++sameMark);
            }
        }

        return wandTestDataList;
    }

    private void buildLh(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int lhMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.LH.getName(), testTime, 1, pendingStatus);
        wandTestData.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ");
        wandTestData.setSameWandMark(lhMark);
        wandTestData.setProductCode("0".concat(WandTypeEnum.LH.getString()));
        wandTestDataList.add(wandTestData);
    }

    private void buildE3gLh(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int e3gLhMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        // 显示两条
        WandTestBiomarkerDTO wandTestData1 = new WandTestBiomarkerDTO(BiomarkerNameEnum.LH.getName(), testTime, 1, pendingStatus);
        wandTestData1.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue2()) + " ");
        wandTestData1.setSameWandMark(e3gLhMark);
        wandTestData1.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
        wandTestDataList.add(wandTestData1);

        WandTestBiomarkerDTO wandTestData2 = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, 1, pendingStatus);
        wandTestData2.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ");
        wandTestData2.setSameWandMark(e3gLhMark);
        wandTestData2.setProductCode("0".concat(WandTypeEnum.E3G_LH.getString()));
        wandTestDataList.add(wandTestData2);
    }

    private void buildPdg(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int pdgMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.PDG.getName(), testTime, 1, pendingStatus);
        wandTestData.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ");
        wandTestData.setSameWandMark(pdgMark);
        wandTestData.setProductCode("0".concat(WandTypeEnum.PDG.getString()));
        wandTestDataList.add(wandTestData);
    }

    private void buildE3gHighRange(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int e3gHighRangeMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, 1, pendingStatus);
        wandTestData.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ");
        wandTestData.setSameWandMark(e3gHighRangeMark);
        wandTestData.setProductCode(WandTypeEnum.E3G_HIGH_RANGE.getString());
        wandTestDataList.add(wandTestData);
    }

    private void buildLhE3gPdg(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int lhE3gPdgMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        //显示三条
        WandTestBiomarkerDTO wandTestData1 = new WandTestBiomarkerDTO(BiomarkerNameEnum.LH.getName(), testTime, 1, pendingStatus);
        wandTestData1.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ");
        wandTestData1.setSameWandMark(lhE3gPdgMark);
        wandTestData1.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
        wandTestDataList.add(wandTestData1);

        WandTestBiomarkerDTO wandTestData2 = new WandTestBiomarkerDTO(BiomarkerNameEnum.PDG.getName(), testTime, 1, pendingStatus);
        wandTestData2.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue2()) + " ");
        wandTestData2.setSameWandMark(lhE3gPdgMark);
        wandTestData2.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
        wandTestDataList.add(wandTestData2);

        WandTestBiomarkerDTO wandTestData3 = new WandTestBiomarkerDTO(BiomarkerNameEnum.E3G.getName(), testTime, 1, pendingStatus);
        wandTestData3.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue3()) + " ");
        wandTestData3.setSameWandMark(lhE3gPdgMark);
        wandTestData3.setProductCode(WandTypeEnum.LH_E3G_PDG.getString());
        wandTestDataList.add(wandTestData3);
    }

    private void buildHcg(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList,
                          Long userId, int hcgMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.HCG.getName(), testTime, 1, pendingStatus);
        String valueResult = NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ";
        List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
        if (userId != null && hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId)) {
            wandTestData.setTestValue("**");
        } else {
            wandTestData.setTestValue(valueResult);
        }
        wandTestData.setSameWandMark(hcgMark);
        wandTestData.setProductCode("0".concat(WandTypeEnum.HCG.getString()));
        wandTestDataList.add(wandTestData);
    }

    private void buildHcgQualitative(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int hcgQualitativeMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        Float value1 = manualHormoneDTO.getValue1();
        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.HCG2.getName(), testTime, 1, pendingStatus);
        String valueResult = "Pregnant";
        if (value1 < 10) {
            valueResult = "Not Pregnant";
        }
        wandTestData.setTestValue(valueResult);
        wandTestData.setSameWandMark(hcgQualitativeMark);
        wandTestData.setProductCode(WandTypeEnum.HCG_QUALITATIVE.getString());
        wandTestDataList.add(wandTestData);
    }

    private void buildFsh(ManualHormoneDTO manualHormoneDTO, List<WandTestBiomarkerDTO> wandTestDataList, int fshMark) {
        String testTime = manualHormoneDTO.getTest_time();
        Integer pendingStatus = manualHormoneDTO.getPendingStatus();

        WandTestBiomarkerDTO wandTestData = new WandTestBiomarkerDTO(BiomarkerNameEnum.FSH.getName(), testTime, 1, pendingStatus);
        wandTestData.setTestValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()) + " ");
        wandTestData.setSameWandMark(fshMark);
        wandTestData.setProductCode(WandTypeEnum.FSH.getString());
        wandTestDataList.add(wandTestData);
    }
}
