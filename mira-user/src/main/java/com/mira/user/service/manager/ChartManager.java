package com.mira.user.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.cycle.*;
import com.mira.api.bluetooth.dto.example.AlgorithmExampleDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.CopyOptionsUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.NumberFormatUtil;
import com.mira.core.util.UnitConvertUtil;
import com.mira.user.controller.vo.chart.ChartLineVO;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.cycle.CycleDataVO;
import com.mira.user.controller.vo.cycle.DayTestWandCount;
import com.mira.user.controller.vo.cycle.TestDataVO;
import com.mira.user.dto.chart.TemperatureChartDTO;
import com.mira.user.dto.common.ManualHormoneDTO;
import com.mira.user.enums.chart.ChartLegendEnum;
import com.mira.user.handler.chart.IChartHormoneHandler;
import com.mira.user.handler.chart.hormone.ChartHormoneHandler;
import com.mira.user.handler.chart.testday.ChartTestingDayHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 图表接口通用层
 *
 * <AUTHOR>
 */
@Component
public class ChartManager {
    @Resource
    private IAlgorithmProvider algorithmProvider;
    @Resource
    private ManualDataManager manualDataManager;

    private final static String LH_VIEW = "LH_VIEW";
    private final static String E3G_VIEW = "E3G_VIEW";
    private final static String PDG_VIEW = "PDG_VIEW";
    private final static String FSH_VIEW = "FSH_VIEW";
    private final static String HCG_VIEW = "HCG_VIEW";
    private final static String HCG_QUALITATIVE = "HCG_QUALITATIVE";
    private final static String BBT_VIEW = "BBT_VIEW";

    /**
     * 构造图表的数据展示方式（展示哪些项，以及哪些example数据）
     *
     * @param hormoneDataList     激素数据
     * @param temperatureDataList 温度数据
     * @return 曲线类型
     */
    public List<ChartLineVO.Legend> buildLegend(List<HormoneDTO> hormoneDataList,
                                                List<TemperatureChartDTO> temperatureDataList) {
        return buildLegend(hormoneDataList, temperatureDataList, new ArrayList<>());
    }

    /**
     * 构造图表的数据展示方式（展示哪些项，以及哪些example数据）
     *
     * @param hormoneDataList     激素数据
     * @param temperatureDataList 温度数据
     * @return 曲线类型
     */
    public List<ChartLineVO.Legend> buildLegend(List<HormoneDTO> hormoneDataList,
                                                List<TemperatureChartDTO> temperatureDataList,
                                                List<ManualHormoneDTO> pendingDatas) {
        List<ChartLineVO.Legend> legend = new ArrayList<>();

        // 遍历激素数据列表，获取试剂类型
        Set<Integer> wandTypeSet = new HashSet<>();
        for (HormoneDTO hormoneDTO : hormoneDataList) {
            Integer wandType = hormoneDTO.getTest_results().getWand_type();
            wandTypeSet.add(wandType);
        }

        for (ManualHormoneDTO manualHormoneDTO : pendingDatas) {
            wandTypeSet.add(manualHormoneDTO.getWand_type());
        }

        // 对应试剂是否显示，0不显示，1显示
        Map<String, Boolean> wandViewMap = getWandTypeView(wandTypeSet, temperatureDataList);
        legend.add(new ChartLineVO.Legend(ChartLegendEnum.LH.name(),
                Optional.ofNullable(wandViewMap.get(LH_VIEW)).orElse(false) ? 1 : 0));
        legend.add(new ChartLineVO.Legend(ChartLegendEnum.E3G.name(),
                Optional.ofNullable(wandViewMap.get(E3G_VIEW)).orElse(false) ? 1 : 0));
        legend.add(new ChartLineVO.Legend(ChartLegendEnum.PDG.name(),
                Optional.ofNullable(wandViewMap.get(PDG_VIEW)).orElse(false) ? 1 : 0));
        legend.add(new ChartLineVO.Legend(ChartLegendEnum.FSH.name(),
                Optional.ofNullable(wandViewMap.get(FSH_VIEW)).orElse(false) ? 1 : 0));

        // 有hcg数据才显示实际数据和example数据
        if (wandTypeSet.contains(WandTypeEnum.HCG.getInteger())) {
            legend.add(new ChartLineVO.Legend(ChartLegendEnum.HCG.name(),
                    Optional.ofNullable(wandViewMap.get(HCG_VIEW)).orElse(false) ? 1 : 0));
        }

        String userType = ContextHolder.get(HeaderConst.USER_TYPE);
        UserTypeEnum userTypeEnum = UserTypeEnum.get(userType);
        // 诊所显示 hcg 数据
        if (userTypeEnum == UserTypeEnum.CLINIC_USER) {
            if (wandTypeSet.contains(WandTypeEnum.HCG.getInteger())
                    || wandTypeSet.contains(WandTypeEnum.HCG_QUALITATIVE.getInteger())) {
                legend.add(new ChartLineVO.Legend(ChartLegendEnum.HCG.name(), 1));
            }
        }
        legend.add(new ChartLineVO.Legend(ChartLegendEnum.BBT.name(),
                Optional.ofNullable(wandViewMap.get(BBT_VIEW)).orElse(false) ? 1 : 0));
        return legend;
    }

    private Map<String, Boolean> getWandTypeView(Set<Integer> wandTypeSet, List<TemperatureChartDTO> temperatureDataList) {
        Map<String, Boolean> wandViewMap = new HashMap<>();

        for (Integer wandType : wandTypeSet) {
            if (Objects.equals(wandType, WandTypeEnum.LH.getInteger())) {
                wandViewMap.putIfAbsent(LH_VIEW, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.PDG.getInteger())) {
                wandViewMap.putIfAbsent(PDG_VIEW, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.FSH.getInteger())) {
                wandViewMap.putIfAbsent(FSH_VIEW, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.HCG.getInteger())) {
                wandViewMap.putIfAbsent(HCG_VIEW, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.HCG_QUALITATIVE.getInteger())) {
                wandViewMap.putIfAbsent(HCG_QUALITATIVE, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.E3G_HIGH_RANGE.getInteger())) {
                wandViewMap.putIfAbsent(E3G_VIEW, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.E3G_LH.getInteger())) {
                wandViewMap.putIfAbsent(LH_VIEW, true);
                wandViewMap.putIfAbsent(E3G_VIEW, true);
            }
            if (Objects.equals(wandType, WandTypeEnum.LH_E3G_PDG.getInteger())) {
                wandViewMap.putIfAbsent(LH_VIEW, true);
                wandViewMap.putIfAbsent(E3G_VIEW, true);
                wandViewMap.putIfAbsent(PDG_VIEW, true);
            }
        }

        if (CollectionUtils.isNotEmpty(temperatureDataList)) {
            wandViewMap.putIfAbsent(BBT_VIEW, true);
        }

        return wandViewMap;
    }

    /**
     * 构建图表页的example数据
     *
     * @param tempUnit           温度单位
     * @param exampleLegendsList example数据的曲线类型
     * @return example数据
     */
    public Map<String, List<TestDataDTO>> buildExampleDatasMap(String tempUnit, List<String> exampleLegendsList) {
        Map<String, List<TestDataDTO>> resultMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(exampleLegendsList)) {
            AlgorithmExampleDTO algorithmExampleData = algorithmProvider.algorithmExampleData().getData();
            Map<String, List<TestDataDTO>> legendTestDataMap = convertToLegendTestDataMap(tempUnit, algorithmExampleData);
            for (ChartLegendEnum chartLegendEnum : ChartLegendEnum.values()) {
                if (exampleLegendsList.contains(chartLegendEnum.name())) {
                    resultMap.put(chartLegendEnum.name(), legendTestDataMap.get(chartLegendEnum.name()));
                }
            }
        }

        return resultMap;
    }

    private Map<String, List<TestDataDTO>> convertToLegendTestDataMap(String tempUnit, AlgorithmExampleDTO algorithmExampleDataDTO) {
        Map<String, List<TestDataDTO>> resultMap = new HashMap<>();
        resultMap.put(ChartLegendEnum.LH.name(), algorithmExampleDataDTO.getLH());
        resultMap.put(ChartLegendEnum.E3G.name(), algorithmExampleDataDTO.getE3G());
        resultMap.put(ChartLegendEnum.HCG.name(), algorithmExampleDataDTO.getHCG());
        resultMap.put(ChartLegendEnum.PDG.name(), algorithmExampleDataDTO.getPDG());

        // bbt
        List<TestDataDTO> bbtDTOList = new ArrayList<>();
        List<TestDataDTO> exampleDataBBT = algorithmExampleDataDTO.getBBT();
        if (TempUnitEnum.F.getValue().equals(tempUnit)) {
            for (TestDataDTO testDataDTO : exampleDataBBT) {
                // ℃ to ℉: temp_value = temp_value * 9.0/5.0 + 32.0
                BigDecimal tempValue = UnitConvertUtil.getTemp(tempUnit, BigDecimal.valueOf(testDataDTO.getValue()));
                testDataDTO.setValue(tempValue.floatValue());
                bbtDTOList.add(testDataDTO);
            }
        } else {
            bbtDTOList.addAll(exampleDataBBT);
        }
        resultMap.put(ChartLegendEnum.BBT.name(), bbtDTOList);

        return resultMap;
    }

    /**
     * 获取chart中每个cycle的数据
     */
    public List<CycleDataVO> buildDataInCycle(String nowDate,
                                              List<CycleDataDTO> cycleDataDTOList,
                                              List<HormoneDTO> hormoneDataList,
                                              List<TemperatureChartDTO> temperatureDataList,
                                              List<String> exampleLegendList,
                                              Map<String, List<TestDataDTO>> exampleDatasMap) {
        return buildDataInCycle(nowDate, cycleDataDTOList, hormoneDataList, temperatureDataList, exampleLegendList,
                exampleDatasMap, new ArrayList<>());
    }

    public List<CycleDataVO> buildDataInCycle(String nowDate,
                                              List<CycleDataDTO> cycleDataDTOList,
                                              List<HormoneDTO> hormoneDataList,
                                              List<TemperatureChartDTO> temperatureDataList,
                                              List<String> exampleLegendList,
                                              Map<String, List<TestDataDTO>> exampleDatasMap,
                                              List<ManualHormoneDTO> pendingDatas) {
        List<CycleDataVO> dataInCycle = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOList) {
            CycleDataVO cycleDataVO = new CycleDataVO();
            BeanUtil.copyProperties(cycleDataDTO, cycleDataVO, CopyOptionsUtil.getCopyOptions(CycleDataDTO.class));
            // 怀孕风险预测
            PregnantRiskVO pregnantRiskVO = new PregnantRiskVO();
            PregnantRiskDTO pregnantRiskDTO = cycleDataDTO.getPregnant_risk();
            if (pregnantRiskDTO != null) {
                pregnantRiskVO.setHighRiskCDs(pregnantRiskDTO.getHigh_risks());
                pregnantRiskVO.setMediumRiskCDs(pregnantRiskDTO.getMedium_risks());
                pregnantRiskVO.setLowRiskCDs(pregnantRiskDTO.getLow_risks());
            }
            cycleDataVO.setPregnantRisk(pregnantRiskVO);

            if (CollectionUtils.isNotEmpty(cycleDataDTO.getDate_PDG_rise())) {
                cycleDataDTO.getDate_PDG_rise().sort(String::compareTo);
                cycleDataVO.setDatePdgRises(List.of(cycleDataDTO.getDate_PDG_rise().get(0)));
            } else {
                cycleDataVO.setDatePdgRises(cycleDataDTO.getDate_PDG_rise());
            }

            // 处理温度数据
            List<TemperatureChartDTO> temperatureDatas = handleTemperatureData(cycleDataDTO, temperatureDataList, exampleLegendList, exampleDatasMap);
            cycleDataVO.setTemperatureDatas(temperatureDatas);
            // 处理激素数据
            handleHormoneData(cycleDataDTO, hormoneDataList, exampleDatasMap, cycleDataVO, pendingDatas);

            if (CycleStatusEnum.PREGNANCY_CYCLE.getStatus() == cycleDataDTO.getCycle_status() ||
                    CycleStatusEnum.ABNORMAL_PREGNANCY_CYCLE.getStatus() == cycleDataDTO.getCycle_status()
            ) {
                List<String> cycleCdIndex = cycleDataDTO.getCycle_cd_index();
                List<Map<String, String>> weeks = new ArrayList<>();
                for (int i = 0; i < cycleCdIndex.size(); i++) {
                    if (StringUtils.isBlank(cycleCdIndex.get(i))) {
                        continue;
                    }
                    Map<String, String> map = new HashMap<>();
                    map.put(LocalDateUtil.plusDay(cycleDataDTO.getDate_period_start(), i, DatePatternConst.DATE_PATTERN),
                            cycleCdIndex.get(i));
                    weeks.add(map);
                }
                cycleDataVO.setWeeks(weeks);
            }
            dataInCycle.add(cycleDataVO);
            // 计算每天推荐测试的试剂数量和已测试的试剂数量
            buildDayTestWandCounts(nowDate, cycleDataVO, cycleDataDTO, hormoneDataList);
        }
        return dataInCycle;
    }

    private void buildDayTestWandCounts(String nowDate, CycleDataVO cycleDataVO, CycleDataDTO cycleDataDTO,
                                        List<HormoneDTO> hormoneDataList) {
        List<DayTestWandCount> dayTestWandCounts = cycleDataVO.getDayTestWandCounts();

        TestingProductDayDTO testingProductDayDTO = cycleDataDTO.getTesting_day_list();
        if (testingProductDayDTO == null) {
            return;
        }
        List<String> product03TestingDayList = testingProductDayDTO.getProduct03();
        List<String> product02TestingDayList = testingProductDayDTO.getProduct02();
        List<String> product09TestingDayList = testingProductDayDTO.getProduct09();
        List<String> product12TestingDayList = testingProductDayDTO.getProduct12();
        List<String> product14TestingDayList = testingProductDayDTO.getProduct14();
        List<String> product16TestingDayList = testingProductDayDTO.getProduct16();

        Integer lenCycle = cycleDataDTO.getLen_cycle();
        String datePeriodStart = cycleDataDTO.getDate_period_start();
        for (int i = 0; i < lenCycle; i++) {
            // 计算周期内的每一天
            String date;
            if (i == 0) {
                date = datePeriodStart;
            } else {
                date = LocalDateUtil.plusDay(datePeriodStart, i, DatePatternConst.DATE_PATTERN);
            }
            // nowDate及之后
            if (date.compareTo(nowDate) < 0) {
                continue;
            }
            // 计算date在哪几个推荐测试的试剂的TestDayList里面
            int recommendedNumber = 0;
            if (product03TestingDayList != null && product03TestingDayList.contains(date)) {
                recommendedNumber++;
            }
            if (product02TestingDayList != null && product02TestingDayList.contains(date)) {
                recommendedNumber++;
            }
            if (product09TestingDayList != null && product09TestingDayList.contains(date)) {
                recommendedNumber++;
            }
            if (product12TestingDayList != null && product12TestingDayList.contains(date)) {
                recommendedNumber++;
            }
            if (product14TestingDayList != null && product14TestingDayList.contains(date)) {
                recommendedNumber++;
            }
            if (product16TestingDayList != null && product16TestingDayList.contains(date)) {
                recommendedNumber++;
            }
            // 计算date这天测试了多少种试剂（有效数据）
            int wandCountDate = CycleDataUtil.wandCountByHormone(date, hormoneDataList);

            DayTestWandCount dayTestWandCount = new DayTestWandCount();
            dayTestWandCount.setDate(date);
            dayTestWandCount.setTestedNumber(wandCountDate);
            dayTestWandCount.setRecommendedNumber(recommendedNumber);
            dayTestWandCounts.add(dayTestWandCount);
        }
        dayTestWandCounts.sort(Comparator.comparing(DayTestWandCount::getDate).reversed());
    }

    public void appendExtraTemperatureCycle(List<TemperatureChartDTO> temperatureDataList, List<CycleDataVO> dataInCycle, CycleDataDTO lastCycleData) {
        String newStartDate = LocalDateUtil.plusDay(lastCycleData.getDate_period_start(), lastCycleData.getLen_cycle(),
                DatePatternConst.DATE_PATTERN);
        List<TemperatureChartDTO> extraTemperatureList = temperatureDataList
                .stream()
                .filter(temperatureData -> temperatureData.getTestTime().compareTo(newStartDate) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(extraTemperatureList)) {
            CycleDataVO cycleDataVO = new CycleDataVO();
            cycleDataVO.setCycleIndex(lastCycleData.getCycle_index() + 1);
            cycleDataVO.setDatePeriodStart(newStartDate);
            cycleDataVO.setLenCycle(LocalDateUtil.minusToDay(LocalDate.now().toString(), newStartDate) + 1);
            cycleDataVO.setCycleStatus(CycleStatusEnum.EXTRA_CYCLE.getStatus());
            cycleDataVO.setTemperatureDatas(extraTemperatureList);
            dataInCycle.add(cycleDataVO);
        }
    }

    private List<TemperatureChartDTO> handleTemperatureData(CycleDataDTO cycleDataDTO,
                                                            List<TemperatureChartDTO> temperatureDataList,
                                                            List<String> exampleLegendList,
                                                            Map<String, List<TestDataDTO>> exampleDatasMap) {
        List<TemperatureChartDTO> resultList = new ArrayList<>();
        if (!exampleLegendList.isEmpty() && exampleLegendList.contains(ChartLegendEnum.BBT.name())) {
            List<TestDataDTO> exampleTemperatureDatas = exampleDatasMap.get(ChartLegendEnum.BBT.name());
            if (exampleTemperatureDatas != null) {
                for (TestDataDTO temperatureData : exampleTemperatureDatas) {
                    int subtract = LocalDateUtil.minusToDay(temperatureData.getTestTime(), cycleDataDTO.getDate_period_start());
                    if (subtract >= 0 && subtract < cycleDataDTO.getLen_cycle()) {
                        TemperatureChartDTO temperatureChartDTO = new TemperatureChartDTO();
                        temperatureChartDTO.setType(0);
                        temperatureChartDTO.setTestTime(temperatureData.getTestTime());
                        temperatureChartDTO.setValue(temperatureData.getValue());
                        resultList.add(temperatureChartDTO);
                    }
                }
            }
        } else {
            for (TemperatureChartDTO temperatureData : temperatureDataList) {
                int subtract = LocalDateUtil.minusToDay(temperatureData.getTestTime(), cycleDataDTO.getDate_period_start());
                if (subtract >= 0 && subtract < cycleDataDTO.getLen_cycle()) {
                    resultList.add(temperatureData);
                }
            }
        }
        return resultList;
    }

    private void handleHormoneData(CycleDataDTO cycleDataDTO,
                                   List<HormoneDTO> hormoneDataList,
                                   Map<String, List<TestDataDTO>> exampleDatasMap,
                                   CycleDataVO cycleDataVO,
                                   List<ManualHormoneDTO> pendingDatas) {
        // 实际数据
        for (HormoneDTO hormoneDTO : hormoneDataList) {
            String testTime = hormoneDTO.getTest_time();
            int subtract = LocalDateUtil.minusToDay(testTime, cycleDataDTO.getDate_period_start());
            if (subtract >= 0 && subtract < cycleDataDTO.getLen_cycle()) {
                HormoneDTO.TestResult testResults = hormoneDTO.getTest_results();
                IChartHormoneHandler chartHormoneHandler = ChartHormoneHandler.get(testResults.getWand_type());
                if (Objects.nonNull(chartHormoneHandler)) {
                    chartHormoneHandler.handle(hormoneDTO, cycleDataVO, null);
                }
            }
        }

        for (ManualHormoneDTO manualHormoneDTO : pendingDatas) {
            String testTime = manualHormoneDTO.getTest_time();
            int subtract = LocalDateUtil.minusToDay(testTime, cycleDataDTO.getDate_period_start());
            if (subtract >= 0 && subtract < cycleDataDTO.getLen_cycle()) {
                IChartHormoneHandler chartHormoneHandler = ChartHormoneHandler.get(manualHormoneDTO.getWand_type());
                if (Objects.nonNull(chartHormoneHandler)) {
                    chartHormoneHandler.handle(null, cycleDataVO, manualHormoneDTO);
                }
            }
        }
        //对结果时间排序
        Comparator<TestDataVO> byTestTime = Comparator.comparing(TestDataVO::getTestTime);
        cycleDataVO.getLhDatas().sort(byTestTime);
        cycleDataVO.getE3gDatas().sort(byTestTime);
        cycleDataVO.getHcgDatas().sort(byTestTime);
        cycleDataVO.getPdgDatas().sort(byTestTime);
        cycleDataVO.getHcg2Datas().sort(byTestTime);
        cycleDataVO.getFshDatas().sort(byTestTime);

        // 示例数据
        for (Map.Entry<String, List<TestDataDTO>> entry : exampleDatasMap.entrySet()) {
            String legend = entry.getKey();
            if (Objects.nonNull(entry.getValue())) {
                for (TestDataDTO testDataDTO : entry.getValue()) {
                    String testTime = testDataDTO.getTestTime();
                    int subtract = LocalDateUtil.minusToDay(testTime, cycleDataDTO.getDate_period_start());
                    if (subtract >= 0 && subtract < cycleDataDTO.getLen_cycle()) {
                        if (ChartLegendEnum.LH.name().equals(legend)) {
                            TestDataVO testDataVO = new TestDataVO();
                            testDataVO.setTestTime(testDataDTO.getTestTime());
                            testDataVO.setValue(testDataDTO.getValue());
                            cycleDataVO.getLhDatas().add(testDataVO);
                        } else if (ChartLegendEnum.E3G.name().equals(legend)) {
                            TestDataVO testDataVO = new TestDataVO();
                            testDataVO.setTestTime(testDataDTO.getTestTime());
                            testDataVO.setValue(testDataDTO.getValue());
                            cycleDataVO.getE3gDatas().add(testDataVO);
                        } else if (ChartLegendEnum.HCG.name().equals(legend)) {
                            TestDataVO testDataVO = new TestDataVO();
                            testDataVO.setTestTime(testDataDTO.getTestTime());
                            testDataVO.setValue(testDataDTO.getValue());
                            cycleDataVO.getHcgDatas().add(testDataVO);
                        } else if (ChartLegendEnum.PDG.name().equals(legend)) {
                            TestDataVO testDataVO = new TestDataVO();
                            testDataVO.setTestTime(testDataDTO.getTestTime());
                            testDataVO.setValue(testDataDTO.getValue());
                            cycleDataVO.getPdgDatas().add(testDataVO);
                        }
                    }
                }
            }
        }

        // 测试日
        TestingProductDayDTO testingProductDayDTO = cycleDataDTO.getTesting_day_list();
        if (Objects.nonNull(testingProductDayDTO)) {
            Map<String, List<String>> testingDayMap = new HashMap<>();
            testingDayMap.put(WandTypeEnum.E3G_LH.getString(), testingProductDayDTO.getProduct03());
            testingDayMap.put(WandTypeEnum.HCG.getString(), testingProductDayDTO.getProduct02());
            testingDayMap.put(WandTypeEnum.PDG.getString(), testingProductDayDTO.getProduct09());
            testingDayMap.put(WandTypeEnum.HCG_QUALITATIVE.getString(), testingProductDayDTO.getProduct14());
            testingDayMap.put(WandTypeEnum.FSH.getString(), testingProductDayDTO.getProduct16());
            // 构建该周期内测试日未测试的日期list，据此来补-1的数据
            for (String key : testingDayMap.keySet()) {
                ChartTestingDayHandler.get(key).handle(testingProductDayDTO, cycleDataVO);
            }
        }

        // pdg 动态均值
        Float pdgDynamic = null;
        if (CollectionUtils.isNotEmpty(cycleDataVO.getPdgDatas())
                && StringUtils.isNotBlank(cycleDataDTO.getDate_ovulation())) {
            pdgDynamic =
                    pdgDynamicValue(cycleDataVO.getPdgDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList()), cycleDataDTO.getDate_ovulation());
        }
        cycleDataVO.setPdgDynamic(pdgDynamic);
    }

    /**
     * PDG 动态均值
     */
    private Float pdgDynamicValue(List<TestDataDTO> pdgDataList, String dateOvulation) {
        List<TestDataDTO> dynamicDataList = pdgDataList.stream()
                                                       .filter(testDataDTO -> testDataDTO.getValue() < 30L)
                                                       .filter(testDataDTO -> testDataDTO.getValue() > 0L)
                                                       .filter(testDataDTO -> LocalDateUtil.minusToDay(testDataDTO.getTestTime(), dateOvulation) < 0)
                                                       .collect(Collectors.toList());
        if (dynamicDataList.size() < 4) {
            return null;
        }
        Float sum = 0F;
        for (TestDataDTO testDataDTO : dynamicDataList) {
            sum += testDataDTO.getValue();
        }
        return NumberFormatUtil.format(sum / dynamicDataList.size());
    }
}
