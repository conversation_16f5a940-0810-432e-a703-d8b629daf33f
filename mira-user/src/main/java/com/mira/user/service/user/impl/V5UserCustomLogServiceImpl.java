package com.mira.user.service.user.impl;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.SpringContextHolder;
import com.mira.core.util.StringListUtil;
import com.mira.user.controller.vo.diary.UserCustomLogConfigVO;
import com.mira.user.controller.vo.diary.UserCustomLogUpdateVO;
import com.mira.user.dal.dao.AppUserDiaryDAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppUserDiaryEntity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.dto.diary.UserCustomLogDTO;
import com.mira.user.enums.calendar.CalendarDiaryEnum;
import com.mira.user.handler.event.EditPeriodEvent;
import com.mira.user.service.manager.CacheManager;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.web.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * v5 user custom log service
 *
 * <AUTHOR>
 */
@Slf4j
@Service("v5UserCustomLogService")
public class V5UserCustomLogServiceImpl extends AbstractCustomLogService {
    @Resource
    private AppUserDiaryDAO appUserDiaryDAO;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private CacheManager cacheManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserCustomLogUpdateVO diaryUpdate(UserCustomLogDTO userCustomLogDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserCustomLogUpdateVO userCustomLogUpdateVO = diarySaveOrUpdate(loginInfo, userCustomLogDTO);
        editCustomLogUnitConfig(loginInfo, userCustomLogDTO.getTempUnit(), userCustomLogDTO.getWeightUnit());
        return userCustomLogUpdateVO;
    }

    private UserCustomLogUpdateVO diarySaveOrUpdate(BaseLoginInfo loginInfo, UserCustomLogDTO userCustomLogDTO) {
        UserCustomLogUpdateVO userCustomLogUpdateVO = new UserCustomLogUpdateVO();
        AppUserDiaryEntity appUserDiaryEntity = buildUserDiaryEntity(loginInfo, userCustomLogDTO, userCustomLogUpdateVO);
        // get request param key
        HttpServletRequest request = RequestUtil.getRequest();
        Set<String> fields = JsonUtil.toObject(RequestUtil.getParameters(request), Map.class).keySet();
        // interator key
        for (String paramKey : fields) {
            CalendarDiaryEnum calendarDiaryEnum = CalendarDiaryEnum.get(paramKey);
            if (Objects.nonNull(calendarDiaryEnum)) {
                // edit period
                if (CalendarDiaryEnum.EDIT_PERIOD == calendarDiaryEnum) {
                    updatePeriodDates(loginInfo.getId(), userCustomLogDTO.getDateStr(), userCustomLogDTO.getEditPeriod());
                    continue;
                }
                // medications
                if (CalendarDiaryEnum.MEDICATIONS == calendarDiaryEnum) {
                    UserCustomLogConfigVO userCustomLogConfigVO = configInfo();
                    if (userCustomLogConfigVO.getMedications()) {
                        medicationsSaveOrUpdate(loginInfo, userCustomLogDTO);
                    }
                    continue;
                }
                // temperatures
                if (CalendarDiaryEnum.TEMPERATURES == calendarDiaryEnum) {
                    temperatureSaveOrUpdate(loginInfo, userCustomLogDTO);
                    continue;
                }
                calendarDiaryEnum.execute(appUserDiaryEntity, userCustomLogDTO);
            }
        }
        // save/update
        if (Objects.isNull(appUserDiaryEntity.getId())) {
            appUserDiaryDAO.save(appUserDiaryEntity);
        } else {
            appUserDiaryDAO.updateById(appUserDiaryEntity);
        }
        return userCustomLogUpdateVO;
    }

    private void updatePeriodDates(Long userId, String date, Boolean editPeriod) {
        // period record
        AppUserPeriodEntity userPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        String periods = userPeriodEntity.getPeriods();
        List<Long> periodsLongList = StringListUtil.strToLongList(periods, ",");
        // handle editPeriod
        if (Objects.isNull(editPeriod)) {
            editPeriod = Boolean.FALSE;
        }

        // get period str list
        List<String> periodStrList = PeriodUtil.periodsLong2String(periodsLongList, userPeriodEntity.getTimeZone());
        // 'date' is period day, and editPeriod:true
        if (editPeriod && periodStrList.contains(date)) {
            return;
        }
        // 'date' is not period day, and editPeriod:false (remove period day)
        if (!editPeriod && !periodStrList.contains(date)) {
            return;
        }

        // algorithm
        AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
        // period length
        Integer periodLength = userPeriodEntity.getAvgLenPeriod();

        List<String> previousCheckDays = generatePeriodCheckDays(date, periodLength, -1);
        List<String> futureCheckDays = generatePeriodCheckDays(date, periodLength, 1);

        // conditoin-1, no period day in the previous {periodLength} days
        boolean hasPeriodInPreviousDays = checkPeriodOverlap(cycleDataDTOS, previousCheckDays);
        // condition-2, no period day in the future {periodLength} days
        boolean hasPeriodInFutureDays = checkPeriodOverlap(cycleDataDTOS, futureCheckDays);

        // add or remove
        if (editPeriod) {
            if (hasPeriodInPreviousDays || hasPeriodInFutureDays) {
                // add this day
                periodStrList.add(date);
            } else {
                // add {periodLength} days
                periodStrList.addAll(generateAddPeriodDay(date, periodLength));
            }
        } else {
            periodStrList.remove(date);
        }

        // execute edit period
        log.info("Update periods via the log page, user:{}, periods:{}", userId, JsonUtil.toJson(periodStrList));
        SpringContextHolder.publishEvent(new EditPeriodEvent(periodStrList));
    }

    private List<String> generatePeriodCheckDays(String periodDate, int periodLength, int direction) {
        List<String> days = new ArrayList<>();
        for (int i = 1; i <= periodLength; i++) {
            days.add(LocalDateUtil.plusDay(periodDate, i * direction, DatePatternConst.DATE_PATTERN));
        }
        return days;
    }

    private List<String> generateAddPeriodDay(String periodDate, int periodLength) {
        List<String> days = new ArrayList<>();
        days.add(periodDate);
        for (int i = 1; i < periodLength; i++) {
            days.add(LocalDateUtil.plusDay(periodDate, i, DatePatternConst.DATE_PATTERN));
        }
        return days;
    }

    private boolean checkPeriodOverlap(List<CycleDataDTO> cycleDataDTOS, List<String> days) {
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            // periods range per cycle
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String datePeriodEnd = cycleDataDTO.getDate_period_end();
            for (String day : days) {
                if (LocalDateUtil.isBetweenDateAndEqualLeft(day, datePeriodStart, datePeriodEnd)) {
                    return true;
                }
            }
        }
        return false;
    }
}
