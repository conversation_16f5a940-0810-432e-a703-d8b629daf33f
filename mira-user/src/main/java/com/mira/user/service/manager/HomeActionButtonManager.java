package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.dto.wand.DayTestProductsDTO;
import com.mira.api.bluetooth.dto.wand.TestRemindDTO;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.dto.wand.WandTestDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.LocalDateUtil;
import com.mira.user.controller.vo.home.HomeV5CycleDataVO;
import com.mira.user.enums.home.HomeActionButtonCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mira.user.enums.home.HomeActionButtonCodeEnum.*;

/**
 * <p>
 *
 * @author: xizhao.dai
 * @since: 2024-02-26
 * @see com.mira.user.enums.home.HomeActionButtonCodeEnum
 * </p>
 **/
@Slf4j
@Component
public class HomeActionButtonManager {
    @Resource
    private IBluetoothProvider bluetoothProvider;
    @Resource
    private UserDiaryLogManager userDiaryLogManager;


    /**
     * 1.User started testing (analyzer bind log)?
     *
     * @param bindLogFlag
     * @return true表示存在仪器绑定记录
     */
    private boolean decision1(Integer bindLogFlag) {
        return bindLogFlag == 1;
    }

    /**
     * 2.Do user has any recommended test today?
     *
     * @return
     */
    private boolean decision2(TestingProductDayDTO testingDayList,
                              String dateStr) {
        if (testingDayList == null) {
            return false;
        }
        List<String> product03TestingDayList = testingDayList.getProduct03();
        List<String> product02TestingDayList = testingDayList.getProduct02();
        List<String> product09TestingDayList = testingDayList.getProduct09();
        List<String> product12TestingDayList = testingDayList.getProduct12();
        List<String> product14TestingDayList = testingDayList.getProduct14();
        List<String> product16TestingDayList = testingDayList.getProduct16();
        if (product03TestingDayList != null && product03TestingDayList.contains(dateStr)) {
            return true;
        }
        if (product02TestingDayList != null && product02TestingDayList.contains(dateStr)) {
            return true;
        }
        if (product09TestingDayList != null && product09TestingDayList.contains(dateStr)) {
            return true;
        }
        if (product12TestingDayList != null && product12TestingDayList.contains(dateStr)) {
            return true;
        }
        if (product14TestingDayList != null && product14TestingDayList.contains(dateStr)) {
            return true;
        }
        if (product16TestingDayList != null && product16TestingDayList.contains(dateStr)) {
            return true;
        }
        return false;
    }

    /**
     * 3. Do user has a predicted period in <2 days?
     * (这里按照测试的逻辑，倒数2天也算)
     *
     * @return
     */
    private boolean decision3(CycleDataDTO cycleDataDTO,
                              String today) {
        String cycleEndDate = LocalDateUtil.plusDay(
                cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
        int gap = LocalDateUtil.minusToDay(today, cycleEndDate);
        if (gap == -1 || gap == -2) {
            return true;
        }
        return false;
    }

    /**
     * 4. 1)Has performed recommended test for today or 2)Has no recommended tests for today
     *
     * @return
     */
    private HomeV5CycleDataVO.ActionButton decision4(List<HormoneDTO> dailyHormoneDatas,
                                                     String today, Long userId,
                                                     List<CycleDataDTO> cycleDataDTOS) {
        HomeV5CycleDataVO.ActionButton actionButton = null;

        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setDate(today);
        wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
        DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
        dayTestProductsDTO.setDate(today);
        dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
        dayTestProductsDTO.setWandTestBiomarkerDTOS(wandTestBiomarkerDTOS);
        List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);
        if (!testingDTOS.isEmpty()) {//命中第一优先级
            actionButton = new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.ONE);
            actionButton.setContentAddition(testingDTOS.get(0).getProductCode());
        }
        return actionButton;
    }

    /**
     * 5. Is it a 1st users' test cycle?
     * 第一条测试数据所在的周期，并判断今天是否在这个周期 (运行到这个判断时，说明今天一定有测试数据存在)
     *
     * @return
     */
    private boolean decision5(CycleDataDTO firstTestCycleDataDTO, String today) {
        if (LocalDateUtil.minusToDay(today, firstTestCycleDataDTO.getDate_period_start()) >= 0 &&
                LocalDateUtil.minusToDay(today, LocalDateUtil.plusDay(
                        firstTestCycleDataDTO.getDate_period_start(), firstTestCycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN)
                ) < 0) {
            return true;
        }
        return false;
    }

    /**
     * 6. Has user performed 15 tests
     * 仅仅取当前周期
     *
     * @return
     */
    private boolean decision6(long firstTestCycleCount) {
        return firstTestCycleCount >= 15;
    }

    /**
     * 7. Is LH peak detected?
     *
     * @return
     */
    private boolean decision7(CycleDataDTO firstTestCycleDataDTO) {
        Float valueLhSurge = firstTestCycleDataDTO.getValue_LH_surge();
        return valueLhSurge != null;
    }

    /**
     * 8. Is user on cycle day 21?
     *
     * @return
     */
    private boolean decision8(CycleDataDTO currentCycleDataDTO, String today) {
        if (LocalDateUtil.minusToDay(today, currentCycleDataDTO.getDate_period_start()) == 20) {
            return true;
        }
        return false;
    }

    /**
     * 9. Does user has an active subscription
     *
     * @return default value is DON'T HAVE subscription.
     */
    private boolean decision9(Long userId) {
        return false;
    }

    /**
     * 10. right after 15th test?
     *
     * @return
     */
    private boolean decision10(long firstTestCycleCount) {
        return firstTestCycleCount == 15;
    }

    /**
     * 11. Is there 7 days up to the next predicted cycle?
     *
     * @return
     */
    private boolean decision11(CycleDataDTO currentCycleDataDTO, String today) {
        String cycleEndDate = LocalDateUtil.plusDay(
                currentCycleDataDTO.getDate_period_start(), currentCycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
        if (LocalDateUtil.minusToDay(today, cycleEndDate) == -7) {
            return true;
        }
        return false;
    }

    /**
     * 12. Has user performed 10 tests during the current cycle?
     *
     * @return
     */
    private boolean decision12(long currentCycleCount) {
        return currentCycleCount >= 10;
    }

    /**
     * 13. right after 10th test?
     *
     * @return
     */
    private boolean decision13(long currentCycleCount) {
        return currentCycleCount == 10;
    }

    /**
     * 14. Is there 7 days up to the next predicted cycle?
     * 正好倒数第7天
     *
     * @return
     */
    private boolean decision14(CycleDataDTO currentCycleDataDTO, String today) {
        return decision11(currentCycleDataDTO, today);
    }

    /**
     * 15. Do user has period late  >2 days?
     *
     * @return always no in current version
     */
    private boolean decision15() {
        return false;
    }

    /**
     * 16. Has user logged POSITIVE pregnancy test result?
     * todo 暂时没有
     *
     * @return
     */
    private boolean decision16() {
        return false;
    }

    /**
     * 17. Has period started (logged)?
     *
     * @return always yes in current version
     */
    private boolean decision17() {
        return true;
    }

    /**
     * 18. Has user logged symptoms?
     * triggered by symptom and moods
     *
     * @return
     */
    private boolean decision18(Long userId, String today) {
        List<UserSymptomDTO> userSymptomDTOS = userDiaryLogManager.listUserSymptomDTO(userId, today);
        if (!CollectionUtils.isEmpty(userSymptomDTOS)) {
            return true;
        }
        UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryLogManager.getUserDiaryMoodsDTO(userId, today);
        if (userDiaryMoodsDTO != null) {
            return true;
        }
        return false;
    }

    /**
     * 19. check again if the action button is null?
     *
     * @return true表示actionButton为空
     */
    private boolean decision19(HomeV5CycleDataVO.ActionButton actionButton) {
        return actionButton == null;
    }


    @NotNull
    public HomeV5CycleDataVO.ActionButton buildActionButton(Long userId,
                                                            List<HormoneDTO> hormoneDatas,
                                                            List<CycleDataDTO> cycleDataDTOS,
                                                            String today,
                                                            Integer bindLogFlag,
                                                            Integer userMode
    ) {
        HomeV5CycleDataVO.ActionButton actionButton;
        try {
            actionButton = buildGroup1ActionButton(userId, hormoneDatas, cycleDataDTOS, today,
                    userMode, bindLogFlag);
        } catch (Exception e) {
            actionButton = new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.SEVEN);
            log.error("buildGroup1ActionButton error", e);
        }
        return actionButton;
    }

    /**
     * 能被2整除，则返回true
     *
     * @param userId
     * @return
     */
    private boolean checkABTestUserId(Long userId) {
        // 检查 userId 是否能被 2 整除
        return userId % 2 == 0;
    }

    /**
     * @param userId
     * @param hormoneDatas
     * @param cycleDataDTOS
     * @param currentCycleDataDTO 今天所在周期，由于前面做了过滤，这里不会是空
     * @param today
     * @param bindLogFlag
     * @return
     */
    @NotNull
    private HomeV5CycleDataVO.ActionButton buildGroup1ActionButton(Long userId,
                                                                   List<HormoneDTO> hormoneDatas,
                                                                   List<CycleDataDTO> cycleDataDTOS,
                                                                   String today,
                                                                   Integer userMode,
                                                                   Integer bindLogFlag) {
        //获取当前周期
        CycleDataDTO currentCycleDataDTO = cycleDataDTOS
                .stream()
                .filter(cycleDataDTO ->
                        LocalDateUtil.minusToDay(today, cycleDataDTO.getDate_period_start()) >= 0 &&
                                LocalDateUtil.minusToDay(today, LocalDateUtil.plusDay(
                                        cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN)
                                ) < 0)
                .findFirst()
                .orElse(null);

        boolean decision1 = decision1(bindLogFlag);
        boolean decision9 = decision9(userId);
        boolean decision18 = decision18(userId, today);
        if (!decision1) {
            return checkDecision3(false, decision18, decision9, currentCycleDataDTO, today, userMode);
        }
        //当前周期的推荐测试日
        TestingProductDayDTO testingDayList = currentCycleDataDTO.getTesting_day_list();
        boolean decision2 = decision2(testingDayList, today);
        if (!decision2) {
            return checkDecision3(true, decision18, decision9, currentCycleDataDTO, today, userMode);
        }
        //获取今天的测试数据
        List<HormoneDTO> todayHormoneDatas = hormoneDatas
                .stream()
                .filter(hormoneData -> today.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .collect(Collectors.toList());
        HomeV5CycleDataVO.ActionButton decision4ActionButton = decision4(todayHormoneDatas, today, userId,
                cycleDataDTOS);
        if (decision4ActionButton != null) {
            //case1
            return decision4ActionButton;
        }
        String firstTestTime;
        CycleDataDTO firstTestCycleDataDTO = null;
        String firstTestCyclePeriodStart;
        long firstTestCycleCount = 0;
        //不包含
        String firstTestCycleCycleEnd;
        HormoneDTO firstHormoneDTO = todayHormoneDatas.get(0);
        if (firstHormoneDTO != null) {
            firstTestTime = firstHormoneDTO.getTest_time();
            firstTestCycleDataDTO = cycleDataDTOS
                    .stream()
                    .filter(cycleDataDTO ->
                            LocalDateUtil.minusToDay(firstTestTime, cycleDataDTO.getDate_period_start()) >= 0 &&
                                    LocalDateUtil.minusToDay(firstTestTime, LocalDateUtil.plusDay(
                                            cycleDataDTO.getDate_period_start(), cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN)
                                    ) < 0)
                    .findFirst()
                    .orElse(null);
            firstTestCyclePeriodStart = firstTestCycleDataDTO.getDate_period_start();
            firstTestCycleCycleEnd = LocalDateUtil.plusDay(firstTestCyclePeriodStart, firstTestCycleDataDTO.getLen_cycle(),
                    DatePatternConst.DATE_PATTERN);
            firstTestCycleCount =
                    todayHormoneDatas.stream()
                                .filter(hormoneData ->
                                        LocalDateUtil.minusToDay(hormoneData.getTest_time(), firstTestCyclePeriodStart) >= 0 &&
                                                LocalDateUtil.minusToDay(hormoneData.getTest_time(), firstTestCycleCycleEnd) < 0
                                )
                                //取有效数据
                                .filter(hormoneData -> hormoneData.getTest_results().getEcode() == null)
                                .count();
        }


        boolean decision5 = false;
        if (firstTestCycleDataDTO != null) {
            decision5 = decision5(firstTestCycleDataDTO, today);
        }
        if (decision5) {
            boolean decision6 = decision6(firstTestCycleCount);
            if (decision6) {
                if (decision9) {
                    //case 15需要额外的校验
                    return checkCase15(decision18);
                }
                boolean decision10 = decision10(firstTestCycleCount);
                if (decision10) {
                    //case11
                    return new HomeV5CycleDataVO.ActionButton(ELEVEN);
                }
                boolean decision11 = decision11(currentCycleDataDTO, today);
                if (decision11) {
                    //case12
                    return new HomeV5CycleDataVO.ActionButton(TWELVE);
                }
                //case15
                return checkCase15(decision18);
            }
            boolean decision7 = decision7(firstTestCycleDataDTO);
            if (decision7) {
                //case9
                return new HomeV5CycleDataVO.ActionButton(NINE);
            }
            boolean decision8 = decision8(currentCycleDataDTO, today);
            if (decision8) {
                //case10
                return new HomeV5CycleDataVO.ActionButton(TEN);
            }
            //case15
            return checkCase15(decision18);
        }

        String currentCyclePeriodStart = currentCycleDataDTO.getDate_period_start();
        //不包含
        String currentCycleCycleEnd = LocalDateUtil.plusDay(currentCyclePeriodStart, currentCycleDataDTO.getLen_cycle(),
                DatePatternConst.DATE_PATTERN);
        long currentCycleCount =
                hormoneDatas.stream()
                            .filter(hormoneData ->
                                    LocalDateUtil.minusToDay(hormoneData.getTest_time(), currentCyclePeriodStart) >= 0 &&
                                            LocalDateUtil.minusToDay(hormoneData.getTest_time(), currentCycleCycleEnd) < 0
                            )
                            //取有效数据
                            .filter(hormoneData -> hormoneData.getTest_results().getEcode() == null)
                            .count();

        boolean decision12 = decision12(currentCycleCount);
        if (decision12) {
            boolean decision13 = decision13(currentCycleCount);//同12
            if (decision13) {
                //case14
                return new HomeV5CycleDataVO.ActionButton(FOURTEEN);
            }
            return checkDecision14(decision18, currentCycleDataDTO, today, userId);
        } else {
            return checkDecision14(decision18, currentCycleDataDTO, today, userId);
        }
    }

    @NotNull
    private static HomeV5CycleDataVO.ActionButton checkCase15(boolean decision18) {
        HomeV5CycleDataVO.ActionButton actionButton;
        if (decision18) {
            actionButton = new HomeV5CycleDataVO.ActionButton(SEVENTEEN);
        } else {
            actionButton = new HomeV5CycleDataVO.ActionButton(SIX);
        }
        return actionButton;
    }

    private HomeV5CycleDataVO.ActionButton checkDecision14(boolean decision18, CycleDataDTO currentCycleDataDTO,
                                                           String today, Long userId) {
        boolean decision14 = decision14(currentCycleDataDTO, today);
        if (decision14) {
            boolean decision9 = decision9(userId);
            if (decision9) {
                //case15
                return checkCase15(decision18);
            } else {
                //case13
                return new HomeV5CycleDataVO.ActionButton(THIRTEEN);
            }
        } else {
            //case15
            return checkCase15(decision18);
        }
    }

    private HomeV5CycleDataVO.ActionButton checkDecision3(boolean decision1, boolean decision18, boolean decision9,
                                                          CycleDataDTO currentCycleDataDTO,
                                                          String today, Integer userMode) {
        boolean decision3 = decision3(currentCycleDataDTO, today);
        if (decision3) {
            if (decision18) {
                //case17
                return new HomeV5CycleDataVO.ActionButton(SEVENTEEN);
            }
            //case2
            return new HomeV5CycleDataVO.ActionButton(TWO);
        }
        boolean decision15 = decision15();
        if (decision15) {
            //case3
            return new HomeV5CycleDataVO.ActionButton(THREE);
        }
        boolean decision17 = decision17();
        if (!decision17) {
            //todo 暂时不会发生
            if (decision1) {
                //case15
            } else {
                //case16
            }
            return new HomeV5CycleDataVO.ActionButton(SEVEN);
        }
        if (!decision18) {
            //case6
            return new HomeV5CycleDataVO.ActionButton(FIVE);
        }
        if (UserGoalEnum.PREGNANCY_TRACKING.getValue().equals(userMode)) {
            //case17
            return new HomeV5CycleDataVO.ActionButton(SEVENTEEN);
        }
        if (decision1) {
            if (decision9) {
                //case17
                return new HomeV5CycleDataVO.ActionButton(SEVENTEEN);
            } else {
                //case7
                return new HomeV5CycleDataVO.ActionButton(SIXTEEN);
            }
        } else {
            //case8
            return new HomeV5CycleDataVO.ActionButton(EIGHT);
        }
    }


    @NotNull
    private HomeV5CycleDataVO.ActionButton buildDefaultActionButton(Integer bindLogFlag,
                                                                    List<HormoneDTO> hormoneDatas,
                                                                    String today,
                                                                    Long userId,
                                                                    Integer userMode,
                                                                    List<CycleDataDTO> cycleDataDTOS) {
        //按钮
        HomeV5CycleDataVO.ActionButton actionButton = null;
        if (bindLogFlag == 1) {//有仪器的用户
            //获取今天的测试数据
            List<HormoneDTO> dailyHormoneDatas = hormoneDatas
                    .stream()
                    .filter(hormoneData -> today.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                    .collect(Collectors.toList());
            WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
            wandDayTestDataDTO.setDate(today);
            wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
            List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
            DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
            dayTestProductsDTO.setDate(today);
            dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
            dayTestProductsDTO.setWandTestBiomarkerDTOS(wandTestBiomarkerDTOS);
            List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);
            if (!testingDTOS.isEmpty()) {//命中第一优先级
                actionButton = new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.ONE);
                actionButton.setContentAddition(testingDTOS.get(0).getProductCode());
            } else {
                //考虑第二优先级，跟经期，预测经期相关
                actionButton = getPriorityActionButton(today, cycleDataDTOS, userMode);
                //剩下的是第三优先级
                if (actionButton == null) {
                    actionButton = new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.SIX);
                }
            }
        } else {//没有仪器的用户
            //考虑第二优先级，跟经期，预测经期相关
            actionButton = getPriorityActionButton(today, cycleDataDTOS, userMode);
            //剩下的是第三优先级
            if (actionButton == null) {
                actionButton = new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.SEVEN);
            }
        }
        return actionButton;
    }

    private HomeV5CycleDataVO.ActionButton getPriorityActionButton(String date, List<CycleDataDTO> cycleDataDTOS, Integer userMode) {
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            Integer cycleStatus = cycleDataDTO.getCycle_status();
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
            String datePeriodEnd = cycleDataDTO.getDate_period_end();

            int subtractFromPeriodStart = LocalDateUtil.minusToDay(date, datePeriodStart);
            int subtractFromCycleEnd = LocalDateUtil.minusToDay(date, dateCycleEnd);
            Integer subtractFromPeriodEnd = null;
            if (StringUtils.isNotBlank(datePeriodEnd)) {
                subtractFromPeriodEnd = LocalDateUtil.minusToDay(date, datePeriodEnd);
            }
            if (subtractFromPeriodStart < 0 || subtractFromCycleEnd >= 0) {
                continue;
            }
            //非预测周期
            if (cycleStatus != CycleStatusEnum.FORECAST_CYCLE.getStatus()) {
                //非预测周期 && 今天为period第一天
                if (subtractFromPeriodStart == 0) {
                    return new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.FIVE);
                }
                //非预测周期 && 今天距离cycle结束日小于2天
                if (subtractFromCycleEnd >= -2) {
                    return new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.TWO);
                }
                //非预测周期 && 今天处于经期内，且距离period开始日比平均经期长度大
                //                if (subtractFromPeriodEnd != null
                //                        && subtractFromPeriodEnd < 0
                //                        && subtractFromPeriodStart > avgLenPeriod) {
                //                    return new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.FOUR);
                //                }
            } else {
                if (subtractFromPeriodStart < 2) {
                    //Predicted period in <2 days
                    return new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.TWO);
                } else if (Objects.equals(UserGoalEnum.TTC.getValue(), userMode)) {
                    //预测周期 && 今天距离period开始日大于等于2天
                    return new HomeV5CycleDataVO.ActionButton(HomeActionButtonCodeEnum.THREE);
                }
            }
        }
        return null;
    }
}
