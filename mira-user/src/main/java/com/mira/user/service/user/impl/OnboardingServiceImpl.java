package com.mira.user.service.user.impl;

import com.google.common.collect.Lists;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.enums.OnboardingStatusEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.OnboardingConditionDAO;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.OnboardingConditionEntity;
import com.mira.user.dto.info.UserBirthdayDTO;
import com.mira.user.dto.info.UserCycleLengthDTO;
import com.mira.user.dto.info.UserGoalDTO;
import com.mira.user.dto.info.UserPeriodLengthDTO;
import com.mira.user.dto.info.v5.MenopauseGoalDTO;
import com.mira.user.dto.info.v5.MenopauseHrtTypeDTO;
import com.mira.user.dto.info.v5.MenopauseSymptomDTO;
import com.mira.user.enums.user.onboarding.OnboardingEnum;
import com.mira.user.enums.user.onboarding.OnboardingHealthGoalEnum;
import com.mira.user.service.manager.AlgorithmCallParamManager;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.user.IOnboardingService;
import com.mira.user.service.user.IUserInfoService;
import com.mira.user.service.user.IUserInfoV5Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Onboarding service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OnboardingServiceImpl implements IOnboardingService {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private OnboardingConditionDAO onboardingConditionDAO;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IUserInfoV5Service userInfoV5Service;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private AlgorithmCallParamManager algorithmCallParamManager;
    @Resource
    private KlaviyoProducer klaviyoProducer;

    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public void discoverySource(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_1.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_1, options, onboardingContionEntity);
    }

    private <T> void insertOrUpdateOnboardingEntity(BaseLoginInfo loginInfo,
                                                    OnboardingEnum onboardingEnum,
                                                    List<T> options,
                                                    OnboardingConditionEntity onboardingContionEntity) {
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // current time
        long currentTimeMillis = System.currentTimeMillis();
        String today = ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_PATTERN);

        // determine object type
        Object o = options.get(0);
        String condition = "";
        if (o instanceof Integer) {
            condition = StringListUtil.listToString(options, ",");
        }
        if (o instanceof UserMedicineDTO) {
            condition = JsonUtil.toJson(options);
        }

        // insert
        if (onboardingContionEntity == null) {
            onboardingContionEntity = new OnboardingConditionEntity();
            onboardingContionEntity.setUserId(userId);
            onboardingContionEntity.setType(onboardingEnum.getCode());
            onboardingContionEntity.setConditions(condition);
            onboardingContionEntity.setCreateTime(currentTimeMillis);
            onboardingContionEntity.setCreateTimeStr(today);
            onboardingContionEntity.setModifyTime(currentTimeMillis);
            onboardingContionEntity.setModifyTimeStr(today);
            onboardingConditionDAO.save(onboardingContionEntity);
            return;
        }

        // update
        onboardingContionEntity.setConditions(condition);
        onboardingContionEntity.setModifyTime(currentTimeMillis);
        onboardingContionEntity.setModifyTimeStr(today);
        onboardingConditionDAO.updateById(onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBirthday(UserBirthdayDTO userBirthdayDTO) {
        userInfoService.saveBirthday(userBirthdayDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void healthGoals(List<Integer> options, Integer update) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_2.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_2, options, onboardingContionEntity);

        // ttc or tta
        Integer goalStatus = options.contains(OnboardingHealthGoalEnum.GET_PREGNANT.getCode()) ?
                UserGoalEnum.TTC.getValue() : UserGoalEnum.CYCLE_TRACKING.getValue();

        // save goal
        UserGoalDTO userGoalDTO = new UserGoalDTO();
        userGoalDTO.setGoalStatus(goalStatus);
        userGoalDTO.setMenopause(options.contains(OnboardingHealthGoalEnum.DEFINE_MENOPAUSE_STAGE.getCode()));
        userInfoService.saveGoal(userGoalDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void averagePeriodLength(UserPeriodLengthDTO userPeriodLengthDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // no period
        if (Objects.nonNull(userPeriodLengthDTO.getNoPeriodFlag()) && Objects.equals(1, userPeriodLengthDTO.getNoPeriodFlag())) {
            AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
            if (Objects.nonNull(appUserInfo)) {
                appUserInfo.setNoPeriod(appUserInfo.getGoalStatus());
                appUserInfoDAO.updateById(appUserInfo);
            }
            if (Objects.isNull(userPeriodLengthDTO.getAvgLenPeriod())) {
                userPeriodLengthDTO.setAvgLenPeriod(6);
            }
        }

        // save period length
        userInfoService.savePeriodLength(userPeriodLengthDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void averageCycleLength(UserCycleLengthDTO userCycleLengthDTO) {
        if (Objects.nonNull(userCycleLengthDTO.getIrregularCycle()) && Objects.equals(1, userCycleLengthDTO.getIrregularCycle())) {
            if (Objects.isNull(userCycleLengthDTO.getAvgLenCycle())) {
                userCycleLengthDTO.setAvgLenCycle(30);
            }
        }
        userInfoService.saveCycleLength(userCycleLengthDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void noPeriod(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_3.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_3, options, onboardingContionEntity);

        // save period entity
        userInfoService.savePeriod(Lists.newArrayList("default"));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lastPeriod(List<String> dates) {
        userInfoService.savePeriod(dates);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void otherTrackingTools(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_4.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_4, options, onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void otherConsiderations(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // app user info entity
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        String conditions = StringListUtil.listToString(options, ",");
        userInfoEntity.setConditions(conditions);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void medicalProcedures(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_5.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_5, options, onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void medicationsOption(Integer option) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_6_SELECT.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_6_SELECT, Lists.newArrayList(option), onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void medications(List<UserMedicineDTO> medications) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_6.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_6, medications, onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pregnancyHistory(Integer option) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_7.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_7, Lists.newArrayList(option), onboardingContionEntity);

        // check menopause
        OnboardingConditionEntity goalConditionsEntity = onboardingConditionDAO.getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_2.getCode());
        List<Integer> goalConditions = StringListUtil.strToIntegerList(goalConditionsEntity.getConditions(), ",");
        if (goalConditions.contains(OnboardingHealthGoalEnum.DEFINE_MENOPAUSE_STAGE.getCode())) {
            userInfoV5Service.checkTrackingMenopause(Boolean.TRUE, null);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pregnancyPlan(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_8.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_8, options, onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void menopauseMontherOnsetAge(Integer age, Integer update) {
        userInfoV5Service.menopauseMotherEnter(age, update);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void menopauseHrt(Integer option, Integer update) {
        userInfoV5Service.checkHrt(option, update);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void menopauseHrtStatus(MenopauseHrtTypeDTO menopauseHrtTypeDTO, Integer update) {
        userInfoV5Service.menopauseHrtType(menopauseHrtTypeDTO, update);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void menopauseYourGoals(MenopauseGoalDTO menopauseGoalDTO, Integer update) {
        userInfoV5Service.menopauseGoal(menopauseGoalDTO, update);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void menopauseSymptoms(List<MenopauseSymptomDTO> menopauseSymptoms, Integer update) {
        userInfoV5Service.menopauseSymptom(menopauseSymptoms, update);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lifestyleStressLevel(Integer option) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_9.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_9, Lists.newArrayList(option), onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lifestylePhysicalActivity(List<Integer> options) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_10.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_10, options, onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lifestyleActivityLevel(Integer option) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_11.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_11, Lists.newArrayList(option), onboardingContionEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lifestyleDietaryHabits(Integer option) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity
        OnboardingConditionEntity onboardingContionEntity = onboardingConditionDAO
                .getByUserIdAndType(userId, OnboardingEnum.ONBOARDING_12.getCode());

        // insert or update
        insertOrUpdateOnboardingEntity(loginInfo, OnboardingEnum.ONBOARDING_12, Lists.newArrayList(option), onboardingContionEntity);

        // login user info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        // update onboarding status
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
        if (!Objects.equals(appUserInfoEntity.getOnboardingStatus(), OnboardingStatusEnum.HOME_PAGE.getCode())) {
            appUserInfoEntity.setOnboardingStatus(OnboardingStatusEnum.PAIRED_DEVICE.getCode());
        }

        // call algorithm
        algorithmCallParamManager.callAlgorithm(userId, loginUserInfoDTO, AlgorithmRequestTypeEnum.REGISTER);

        // klavyio
        klaviyoProducer.onBoardingComplete(userId);
    }
}
