package com.mira.user.service.survey.condition;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.mongo.dto.SurveyCondition;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.LocalDateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <p>
 * Days before predicted period
 * <br/>
 * 预测月经前的天数，1,2,3...9,10
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-16
 **/
@Slf4j
public class BeforePrePeriodCondition {
    public static boolean checkBeforePredictedPeriod(Long userId, String today, List<CycleDataDTO> cycleDataDTOS, SurveyCondition surveyCondition) {
        List<Integer> beforePredictedPeriodList = surveyCondition.getBeforePredictedPeriod();
        if (beforePredictedPeriodList.isEmpty()) {
            return true;
        }
        if (cycleDataDTOS.isEmpty()) {
            return false;
        }
        try {
            CycleDataDTO lastCycleData = cycleDataDTOS.get(cycleDataDTOS.size() - 1);
            if (lastCycleData.getCycle_status() == CycleStatusEnum.FORECAST_CYCLE.getStatus()) {
                int minusToDay = LocalDateUtil.minusToDay(lastCycleData.getDate_period_start(), today);
                if (!beforePredictedPeriodList.contains(minusToDay)) {
                    return false;
                }
            } else if (lastCycleData.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus()) {
                String forecastPeriodStartDate = LocalDateUtil.plusDay(lastCycleData.getDate_period_start(), lastCycleData.getLen_cycle(),
                        DatePatternConst.DATE_PATTERN);
                int minusToDay = LocalDateUtil.minusToDay(forecastPeriodStartDate, today);
                if (!beforePredictedPeriodList.contains(minusToDay)) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("【SurveyCondition】user:{} BeforePrePeriod handler error.", userId, e);
            return false;
        }
        return true;
    }
}
