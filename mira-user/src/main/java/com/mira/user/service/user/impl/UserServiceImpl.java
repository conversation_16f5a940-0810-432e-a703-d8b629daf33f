package com.mira.user.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.BBTModeErrorCodeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.HcgResultEnum;
import com.mira.api.clinic.enums.PregnantFlagEnum;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.*;
import com.mira.api.user.dto.user.diary.*;
import com.mira.api.user.dto.user.diary.excel.ExportTemperatureDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserDiaryIntegrationDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserMedicineDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserSymptomDTO;
import com.mira.api.user.enums.UserBindTypeEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.daily.DailyStatusPregnantEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.*;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.async.PatientProducer;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.properties.PaywallProperties;
import com.mira.user.service.manager.AlgorithmCallManager;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.manager.UserDiaryLogManager;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.user.IUserService;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserServiceImpl implements IUserService {
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserDiaryDAO appUserDiaryDAO;
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final AppBlackSnDAO appBlackSnDAO;
    private final AppUserBindDAO appUserBindDAO;
    private final AppUserBindLogDAO appUserBindLogDAO;
    private final AppBindFirmwareLogDAO appBindFirmwareLogDAO;
    private final AppUserTemperatureDAO appUserTemperatureDAO;
    private final AppUserTestingScheduleDAO appUserTestingScheduleDAO;
    private final AppUserPartnerDAO appUserPartnerDAO;
    private final AppUserReminderDAO appUserReminderDAO;
    private final UserReminderComplaintDAO userReminderComplaintDAO;
    private final UserPaywallDAO userPaywallDAO;
    private final SysNotificationTestingStatisticsDAO sysNotificationTestingStatisticsDAO;

    private final UserDiaryLogManager userDiaryLogManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final CacheManager cacheManager;

    private final PatientProducer patientProducer;
    private final KlaviyoProducer klaviyoProducer;

    private final ISsoProvider ssoProvider;
    private final IMessageProvider messageProvider;

    private final PaywallProperties paywallProperties;

    @Override
    public AppUserDTO getUserById(Long userId) {
        AppUserEntity appUser = appUserDAO.getById(userId);
        //后台管理系统请求过来的userId可能查不到数据
        if (appUser == null) {
            return null;
        }
        return BeanUtil.toBean(appUser, AppUserDTO.class);
    }

    @Override
    public AppUserDTO getUserByEmail(String email) {
        AppUserEntity appUser = appUserDAO.getByEmail(email);
        if (appUser == null) {
            return null;
        }
        return BeanUtil.toBean(appUser, AppUserDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AppUserDTO saveUser(AppUserSaveDTO appUserSaveDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        AppUserEntity existUser = appUserDAO.getByEmail(appUserSaveDTO.getEmail());
        if (existUser != null) {
            return BeanUtil.toBean(existUser, AppUserDTO.class);
        }

        AppUserEntity appUser = new AppUserEntity();
        BeanUtil.copyProperties(appUserSaveDTO, appUser);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUser);
        appUserDAO.save(appUser);

        // klaviyo
        klaviyoProducer.accountCreated(appUser);

        AppUserInfoEntity appUserInfo = new AppUserInfoEntity();
        BeanUtil.copyProperties(appUserSaveDTO, appUserInfo);
        appUserInfo.setUserId(appUser.getId());
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserInfo);
        appUserInfoDAO.save(appUserInfo);

        return BeanUtil.toBean(appUser, AppUserDTO.class);
    }

    @Override
    public AppUserInfoDTO getUserInfoByUserId(Long userId) {
        return BeanUtil.toBean(appUserInfoDAO.getByUserId(userId), AppUserInfoDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserInfo(AppUserInfoDTO appUserInfoDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(appUserInfoDTO.getUserId());
        if (appUserInfoEntity == null) {
            return;
        }
        BeanUtil.copyProperties(appUserInfoDTO, appUserInfoEntity);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserInfoEntity);
        appUserInfoDAO.updateById(appUserInfoEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TemperatureResultDTO saveTemperature(Long userId, UserTemperatureDTO userTemperatureDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        AppUserTemperatureEntity appUserTemperatureEntity = new AppUserTemperatureEntity();
        appUserTemperatureEntity.setUserId(userId);
        appUserTemperatureEntity.setAutoFlag(0);
        appUserTemperatureEntity.setType(1);
        appUserTemperatureEntity.setModeError(userTemperatureDTO.getModeError());
        appUserTemperatureEntity.setTempTimestamp(userTemperatureDTO.getCompleteTimestamp());
        appUserTemperatureEntity.setTempTime(userTemperatureDTO.getCompleteTime());
        appUserTemperatureEntity.setTempDay(ZoneDateUtil.format(timeZone,
                userTemperatureDTO.getCompleteTimestamp(), DatePatternConst.DATE_PATTERN));

        Integer unit = userTemperatureDTO.getUnit();
        BigDecimal temperature = userTemperatureDTO.getTemperature();
        if (!BBTModeErrorCodeEnum.NORMAL.getCode().equals(userTemperatureDTO.getModeError())) {
            appUserTemperatureEntity.setTempC(BigDecimal.valueOf(0));
            appUserTemperatureEntity.setTempF(BigDecimal.valueOf(0));
        } else {
            BigDecimal tempC = null;
            BigDecimal tempF = null;
            if (unit == 1) {
                tempC = temperature;
                tempF = UnitConvertUtil.getUnitConvert("temp", TempUnitEnum.C.getValue(), tempC);
            } else if (unit == 2) {
                tempF = temperature;
                tempC = UnitConvertUtil.getUnitConvert("temp", TempUnitEnum.F.getValue(), tempF);
            }
            appUserTemperatureEntity.setTempC(tempC);
            appUserTemperatureEntity.setTempF(tempF);
        }
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserTemperatureEntity);
        appUserTemperatureDAO.save(appUserTemperatureEntity);

        // klaviyo
        klaviyoProducer.putTemperature(userId, appUserTemperatureEntity.getTempF(), false);

        TemperatureResultDTO temperatureResultDTO = new TemperatureResultDTO();
        temperatureResultDTO.setTempC(appUserTemperatureEntity.getTempC());
        temperatureResultDTO.setTempF(appUserTemperatureEntity.getTempF());
        return temperatureResultDTO;
    }

    @Override
    public TestingScheduleDTO getTestingSchedule(Long userId) {
        AppUserTestingScheduleEntity appUserTestingScheduleEntity = appUserTestingScheduleDAO.getByUserId(userId);
        if (appUserTestingScheduleEntity == null) {
            return new TestingScheduleDTO();
        }
        return BeanUtil.toBean(appUserTestingScheduleEntity, TestingScheduleDTO.class);
    }

    @Override
    public AppPartnerDTO getPartnerById(Long partnerId) {
        return BeanUtil.toBean(appUserPartnerDAO.getById(partnerId), AppPartnerDTO.class);
    }

    @Override
    public AppPartnerDTO getPartnerByEmail(String email) {
        return BeanUtil.toBean(appUserPartnerDAO.getByEmail(email), AppPartnerDTO.class);
    }

    @Override
    public List<UserDiaryDTO> listUserDiary(Long userId, String startDate, String endDate) {
        return appUserDiaryDAO.listByUserIdBetweenDate(userId, startDate, endDate).stream()
                .map(entity -> BeanUtil.toBean(entity, UserDiaryDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public UserDiaryIntegrationDTO getUserDiaryIntegration(Long userId, String date) {
        UserDiaryDTO userDiaryDTO = BeanUtil.toBean(appUserDiaryDAO.getByUserIdAndDayStr(userId, date), UserDiaryDTO.class);
        List<UserSymptomDTO> userSymptomDTOS = userDiaryLogManager.listUserSymptomDTO(userId, date);
        List<UserMedicineDTO> userMedicineDTOS = userDiaryLogManager.listUserMedicine(userId, date);
        UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryLogManager.getUserDiaryMoodsDTO(userId, date);
        List<TemperatureDTO> temperatureDTOS = userDiaryLogManager.listTemperatureDTO(userId, date);

        UserDiaryIntegrationDTO userDiaryIntegrationDTO = new UserDiaryIntegrationDTO();
        userDiaryIntegrationDTO.setUserDiaryDTO(userDiaryDTO);
        userDiaryIntegrationDTO.setUserSymptomDTOS(userSymptomDTOS);
        userDiaryIntegrationDTO.setUserMedicineDTOS(userMedicineDTOS);
        userDiaryIntegrationDTO.setUserDiaryMoodsDTO(userDiaryMoodsDTO);
        userDiaryIntegrationDTO.setTemperatureDTOS(temperatureDTOS);

        return userDiaryIntegrationDTO;
    }

    @Override
    public Map<String, UserDiaryIntegrationDTO> listUserDiaryIntegration(Long userId, List<String> dates) {
        List<UserDiaryDTO> userDiaryDTOS = appUserDiaryDAO.listByUserIdInDayStr(userId, dates).stream()
                .map(entity -> BeanUtil.toBean(entity, UserDiaryDTO.class))
                .collect(Collectors.toList());
        Map<String, List<UserSymptomDTO>> userSymptomDTOMap = userDiaryLogManager.listUserSymptomDTOMap(userId, dates);
        Map<String, List<UserMedicineDTO>> userMedicineDTOMap = userDiaryLogManager.listUserMedicineMap(userId, dates);
        Map<String, UserDiaryMoodsDTO> userDiaryMoodsDTOMap = userDiaryLogManager.getUserDiaryMoodsDTOMap(userId, dates);
        Map<String, List<TemperatureDTO>> temperatureDTOMap = userDiaryLogManager.listTemperatureDTOMap(userId, dates);

        Map<String, UserDiaryIntegrationDTO> userDiaryIntegrationDTOMap = new HashMap<>();
        for (String date : dates) {
            UserDiaryIntegrationDTO userDiaryIntegrationDTO = new UserDiaryIntegrationDTO();
            userDiaryIntegrationDTO.setUserDiaryDTO(userDiaryDTOS.stream().filter(dto -> date.equals(dto.getDiaryDayStr())).findFirst().orElse(null));
            userDiaryIntegrationDTO.setUserSymptomDTOS(userSymptomDTOMap.get(date));
            userDiaryIntegrationDTO.setUserMedicineDTOS(userMedicineDTOMap.get(date));
            userDiaryIntegrationDTO.setUserDiaryMoodsDTO(userDiaryMoodsDTOMap.get(date));
            userDiaryIntegrationDTO.setTemperatureDTOS(temperatureDTOMap.get(date));
            userDiaryIntegrationDTOMap.put(date, userDiaryIntegrationDTO);
        }

        return userDiaryIntegrationDTOMap;
    }

    @Override
    public Map<String, ExportUserDiaryIntegrationDTO> listUserAllDiaryIntegration(Long userId) {
        List<UserDiaryDTO> userDiaryDTOS = appUserDiaryDAO.listByUserId(userId).stream()
                .map(entity -> BeanUtil.toBean(entity, UserDiaryDTO.class))
                .collect(Collectors.toList());
        Map<String, ExportUserSymptomDTO> userSymptomDTOMap = userDiaryLogManager.listUserSymptomDTOMap(userId);
        Map<String, ExportUserMedicineDTO> userMedicineDTOMap = userDiaryLogManager.listUserMedicineMap(userId);
        Map<String, List<ExportTemperatureDTO>> temperatureDTOMap = userDiaryLogManager.listTemperatureDTOMap(userId);

        Map<String, UserDiaryMoodsDTO> userDiaryMoodsDTOMap = userDiaryLogManager.getUserDiaryMoodsDTOMap(userId);

        Set<String> allDates = new HashSet<>();
        allDates.addAll(userSymptomDTOMap.keySet());
        allDates.addAll(userMedicineDTOMap.keySet());
        allDates.addAll(userDiaryMoodsDTOMap.keySet());
        allDates.addAll(temperatureDTOMap.keySet());

        Map<String, ExportUserDiaryIntegrationDTO> exportUserDiaryIntegrationDTOMap = new HashMap<>();
        for (String date : allDates) {
            ExportUserDiaryIntegrationDTO exportUserDiaryIntegrationDTO = new ExportUserDiaryIntegrationDTO();
            exportUserDiaryIntegrationDTO.setUserDiaryDTO(userDiaryDTOS.stream().filter(dto -> date.equals(dto.getDiaryDayStr())).findFirst().orElse(null));
            exportUserDiaryIntegrationDTO.setSymptoms(userSymptomDTOMap.get(date));
            exportUserDiaryIntegrationDTO.setMedicines(userMedicineDTOMap.get(date));
            exportUserDiaryIntegrationDTO.setUserDiaryMoodsDTO(userDiaryMoodsDTOMap.get(date));
            exportUserDiaryIntegrationDTO.setTemperatureDTOS(temperatureDTOMap.get(date));
            exportUserDiaryIntegrationDTOMap.put(date, exportUserDiaryIntegrationDTO);
        }

        // 将 map 转换为 stream，然后排序并收集到 linked hashmap 中以保持排序
        Map<String, ExportUserDiaryIntegrationDTO> sortedMap =
                exportUserDiaryIntegrationDTOMap.entrySet()
                        .stream()
                        .sorted(Map.Entry.<String, ExportUserDiaryIntegrationDTO>comparingByKey().reversed()) // 按照键（日期）逆序排序
                        .collect(
                                Collectors.toMap(
                                        Map.Entry::getKey,
                                        Map.Entry::getValue,
                                        (oldValue, newValue) -> oldValue, // 合并函数，不过在这种情况下不会有重复键
                                        LinkedHashMap::new // 使用 LinkedHashMap 来保持顺序
                                ));

        return sortedMap;
    }

    @Override
    public UserPeriodDTO getUserPeriod(Long userId) {
        return BeanUtil.toBean(appUserPeriodDAO.getByUserId(userId), UserPeriodDTO.class);
    }

    @Override
    public AlgorithmEditPeriodDTO buildAlgorithmEditPeriodDTO(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            return null;
        }

        // 构建经期信息
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, null);
        // 获取AlgorithmEditPeriodDTO
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(loginUserInfoDTO.getTimeZone(),
                loginUserInfoDTO.getEmail(), appUserPeriodEntity.getPeriods(), userPeriodParamDTO);

        return algorithmCallManager.getAlgorithmEditPeriodDTO(prepareEditPeriodDTO, algorithmRequestTypeEnum);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void wandChange(Long userId, String wandType) {
        if (WandTypeEnum.LH_E3G_PDG.getString().equals(wandType)) {
            // 如果用户处于 user_reminder_complaint 数据表中，则自动打开 reminder setting 的 Max 开关
            long count = userReminderComplaintDAO.getCountByUserId(userId);
            if (count > 0) {
                AppUserReminderEntity userReminderEntity = appUserReminderDAO.getByUserId(userId);
                if (userReminderEntity == null) {
                    return;
                }
                userReminderEntity.setMaxWands(1);
                userReminderEntity.setFertilityPlusWands(0);
                userReminderEntity.setFertilityConfirmWands(0);
                appUserReminderDAO.updateById(userReminderEntity);
                cacheManager.deleteUserDetailCache(userId);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePregnantDiary(UpdatePregnantDiaryDTO updatePregnantDiaryDTO) {
        Long userId = updatePregnantDiaryDTO.getUserId();
        String timeZone = updatePregnantDiaryDTO.getTimeZone();
        HormoneDTO hcgHormoneDTO = JsonUtil.toObject(updatePregnantDiaryDTO.getHcgHormoneDTO(), HormoneDTO.class);
        Integer hcgResult = updatePregnantDiaryDTO.getHcgResult();

        // 获取每日信息
        Long diaryDay = ZoneDateUtil.timestamp(timeZone, hcgHormoneDTO.getTest_time(), DatePatternConst.DATE_PATTERN);
        String diaryDayStr = ZoneDateUtil.format(timeZone, diaryDay, DatePatternConst.DATE_PATTERN);
        AppUserDiaryEntity appUserDiary = appUserDiaryDAO.getByUserIdAndDayStr(userId, diaryDayStr);

        if (appUserDiary == null || !appUserDiary.getPregnant()) {
            // hCG test result above 10 ng/ml
            HormoneDTO.TestResult testResult = hcgHormoneDTO.getTest_results();
            Float value1 = testResult.getValue1();

            if (value1 >= 10) {
                if (WandTypeEnum.HCG.getInteger().equals(testResult.getWand_type())) {
                    patientProducer.pregnantFlag(userId, PregnantFlagEnum.HCG);
                } else if (WandTypeEnum.HCG_QUALITATIVE.getInteger().equals(testResult.getWand_type())) {
                    patientProducer.pregnantFlag(userId, PregnantFlagEnum.HCG_QUALITATIVE);
                }
            }
        }

        Boolean pregnant;
        if (HcgResultEnum.NOT_PREGNANT.getResult() == hcgResult) {
            pregnant = DailyStatusPregnantEnum.FALSE.getValue();
        } else if (HcgResultEnum.pregnancy.getResult() == hcgResult) {
            pregnant = DailyStatusPregnantEnum.TRUE.getValue();
        } else {
            return;
        }

        if (appUserDiary == null) {
            // save
            appUserDiary = new AppUserDiaryEntity();
            appUserDiary.setUserId(userId);
            appUserDiary.setDiaryDay(diaryDay);
            appUserDiary.setDiaryDayStr(diaryDayStr);
            appUserDiary.setPregnant(pregnant);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserDiary);
            appUserDiaryDAO.save(appUserDiary);
        } else {
            // update
            appUserDiary.setPregnant(pregnant);
            appUserDiaryDAO.updateById(appUserDiary);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer bindUnbindDevice(Long userId, UserBindDTO userBindDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        String bindVersion = userBindDTO.getBindVersion();
        String bindTime = userBindDTO.getBindTime();
        String bindDevice = userBindDTO.getBindDevice();
        Integer bindType = userBindDTO.getType();
        String sn = DeviceUtil.sn(bindDevice);

        AppUserBindEntity userBindEntity = appUserBindDAO.getByUserIdAndBindDevice(userId, bindDevice);

        // update info
        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        if (UserBindTypeEnum.ONLINE_UNBIND.getCode().equals(bindType)
                || UserBindTypeEnum.OUTLINE_UNBIND.getCode().equals(bindType)) {
            // 解绑
            userInfoEntity.setBindVersion("");
            userInfoEntity.setBindTime(bindTime);
            userInfoEntity.setBindDevice("");
            appUserInfoDAO.updateById(userInfoEntity);
            cacheManager.deleteUserDetailCache(userId);
            if (userBindEntity != null) {
                appUserBindDAO.removeById(userBindEntity.getId());
            }

        } else if (UserBindTypeEnum.BIND.getCode().equals(bindType)) {
            // 校验sn黑名单
            AppBlackSnEntity appBlackSn = appBlackSnDAO.getEnableOne(sn);
            if (appBlackSn != null) {
                return -1;
            }
            // 绑定
            userInfoEntity.setBindVersion(bindVersion);
            userInfoEntity.setBindTime(bindTime);
            userInfoEntity.setBindDevice(bindDevice);
            userInfoEntity.setBindFlag(1);
            appUserInfoDAO.updateById(userInfoEntity);
            cacheManager.deleteUserDetailCache(userId);
            if (userBindEntity == null) {
                userBindEntity = new AppUserBindEntity();
                userBindEntity.setBindDevice(bindDevice);
                userBindEntity.setSn(sn);
                userBindEntity.setUserId(userId);
                userBindEntity.setBindTime(bindTime);
                userBindEntity.setBindVersion(bindVersion);
                UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userBindEntity);
                appUserBindDAO.save(userBindEntity);
            } else {
                userBindEntity.setBindTime(bindTime);
                userBindEntity.setBindVersion(bindVersion);
                userBindEntity.setBindDevice(bindDevice);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userBindEntity);
                appUserBindDAO.updateById(userBindEntity);
            }
            // klavyio
            klaviyoProducer.analyzerPaired(userId, sn, bindTime);
        }

        // 绑定或解绑日志
        saveUserBindLog(timeZone, userId, sn, userBindDTO);

        return 0;
    }

    private void saveUserBindLog(String timeZone, Long userId, String sn, UserBindDTO userBindDTO) {
        AppUserBindLogEntity userBindLogEntity = new AppUserBindLogEntity();
        userBindLogEntity.setUserId(userId);
        userBindLogEntity.setBindDevice(userBindDTO.getBindDevice());
        userBindLogEntity.setBindVersion(userBindDTO.getBindVersion());
        userBindLogEntity.setSn(sn);
        userBindLogEntity.setType(userBindDTO.getType());
        userBindLogEntity.setBindTime(ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userBindLogEntity);
        appUserBindLogDAO.save(userBindLogEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String editBindVersion(Long userId, UserBindVersionDTO userBindVersionDTO) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        String bindVersion = userBindVersionDTO.getBindVersion();
        String oldVersion = userBindVersionDTO.getOldVersion();
        String bindDevice = userBindVersionDTO.getBindDevice();

        AppUserInfoEntity userInfoEntity = appUserInfoDAO.getByUserId(userId);
        if (StringUtils.isNotBlank(userBindVersionDTO.getBindVersion())) {
            userInfoEntity.setBindVersion(userBindVersionDTO.getBindVersion());
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userInfoEntity);
        appUserInfoDAO.updateById(userInfoEntity);
        cacheManager.deleteUserDetailCache(userId);

        AppUserBindEntity userBindEntity = appUserBindDAO.getByUserIdAndBindDevice(userId, bindDevice);
        if (Objects.nonNull(userBindEntity)) {
            userBindEntity.setBindVersion(userBindVersionDTO.getBindVersion());
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userBindEntity);
            appUserBindDAO.updateById(userBindEntity);
        }

        saveBindFirmwareLog(userId, timeZone, bindVersion, oldVersion, bindDevice, DeviceUtil.sn(bindDevice));
        return CommonResult.OK().getMsg();
    }

    private void saveBindFirmwareLog(Long userId, String timeZone, String bindVersion, String oldVersion, String bindDevice, String sn) {
        AppBindFirmwareLogEntity appBindFirmwareLogEntity = new AppBindFirmwareLogEntity();
        appBindFirmwareLogEntity.setUserId(userId);
        appBindFirmwareLogEntity.setBindDevice(bindDevice);
        appBindFirmwareLogEntity.setSn(sn);
        appBindFirmwareLogEntity.setOldVersion(oldVersion);
        appBindFirmwareLogEntity.setBindVersion(bindVersion);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appBindFirmwareLogEntity);
        appBindFirmwareLogDAO.save(appBindFirmwareLogEntity);
    }

    @Override
    public Map<Long, String> listEmailByIds(Set<Long> userIds) {
        List<AppUserEntity> appUserEntities = appUserDAO.listByIds(userIds);
        Map<Long, String> map = new HashMap<>();
        for (AppUserEntity appUserEntity : appUserEntities) {
            map.put(appUserEntity.getId(), appUserEntity.getEmail());
        }
        return map;
    }

    @Override
    public List<UserPeriodDTO> listPeriodByUserIds(Set<Long> userIds) {
        List<AppUserPeriodEntity> userPeriodEntities = appUserPeriodDAO.listByUserIds(userIds);
        return BeanUtil.copyToList(userPeriodEntities, UserPeriodDTO.class);
    }

    @Override
    public List<DeskSearchUserDTO> searchDeskUserInfos(String keyword) {
        return appUserDAO.searchDeskUserInfos(keyword);
    }

    @Override
    public DeskUserInfoDTO getDeskUserInfoDTO(Long userId) {
        return appUserDAO.getDeskUserInfoDTO(userId);
    }

    @Override
    public void deskEditPeriod(Long userId) {
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        algorithmCallManager.editDBPeriod(loginUserInfoDTO, AlgorithmRequestTypeEnum.SYSTEM_EDIT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void payWall(UserPaywallDTO userPaywallDTO) {
        Long userId = userPaywallDTO.getUserId();
        String timeZone = userPaywallDTO.getTimeZone();
        List<CycleDataDTO> cycleData = userPaywallDTO.getCycleData();

        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);

        String paywallReleaseDate = ZoneDateUtil
                .format(timeZone, paywallProperties.getPaywallReleaseDate(), DatePatternConst.DATE_PATTERN);
        String userCreateDate = ZoneDateUtil
                .format(timeZone, appUserInfo.getCreateTime(), DatePatternConst.DATE_PATTERN);
        // 只对release date之后注册的用户有效
        if (LocalDateUtil.minusToDay(userCreateDate, paywallReleaseDate) < 0) {
            return;
        }
        // 只对TTC用户有效
        if (UserGoalEnum.TTC != UserGoalEnum.get(appUserInfo.getGoalStatus())) {
            return;
        }
        // 只对有仪器用户有效
        if (StringUtils.isBlank(appUserInfo.getBindDevice())) {
            return;
        }
        // 只对第一个周期有效
        long realCycleCount = cycleData.stream()
                .filter(cycle -> CycleStatusEnum.REAL_CYCLE.getStatus() == cycle.getCycle_status())
                .count();
        if (realCycleCount != 1) {
            return;
        }
        // 只对第5次和第9次测试有效
        long historyCount = userPaywallDTO.getHistoryCount();
        if (historyCount != 5 && historyCount != 9) {
            return;
        }
        // 用户还没收到过
        long userPaywallCount = userPaywallDAO.countByUserId(userId);
        if (userPaywallCount > 0) {
            return;
        }
        // 发送弹窗
        String pushToken = appUserInfo.getPushToken();
        if (StringUtils.isBlank(pushToken)) {
            return;
        }
        String nowDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        String plus2Day = LocalDateUtil.plusDay(nowDate, 2, DatePatternConst.DATE_PATTERN) + " 23:59:59";
        PushNotificationDTO pushNotificationDTO = new PushNotificationDTO()
                .setUserId(userId)
                .setTimeZone(timeZone)
                .setPlatform(appUserInfo.getPlatform())
                .setDefineId(NotificationDefineEnum.PAY_WALL.getDefineId())
                .setPushFirebase(Boolean.TRUE)
                .setSilent(Boolean.TRUE)
                .setSaveRecord(Boolean.TRUE)
                .setExpireTime(ZoneDateUtil.timestamp(timeZone, plus2Day, DatePatternConst.DATE_TIME_PATTERN))
                .setTokens(Collections.singletonList(pushToken));
        messageProvider.sendNotification(pushNotificationDTO);
    }

    @Override
    public void waitShipping(Long userId) {
        appUserInfoDAO.changeBindFlag(userId);
    }
}
