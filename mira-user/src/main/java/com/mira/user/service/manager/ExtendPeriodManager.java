package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.LastCycleFlagEnum;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.user.async.PatientProducer;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.exception.UserException;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-02-22
 **/
@Slf4j
@Component
public class ExtendPeriodManager {
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private PatientProducer patientProducer;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private AlgorithmCallManager algorithmCallManager;
    @Resource
    private RedisComponent redisComponent;

    public void extendPeriod(String timeZone, Long userId, AlgorithmResultDTO algorithmResultDTO) {
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }

        String periods = appUserPeriodEntity.getPeriods();
        if (StringUtils.isBlank(periods)) {
            log.info("user:{}, periods empty", userId);
            return;
        }

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        // check no period mode
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            log.info("user:{} in no period mode, skip extend period", userId);
            return;
        }

        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);

        // 默认不延长
        int lastCycleFlag = LastCycleFlagEnum.ZERO.getFlag();
        List<UserPeriodDataDTO> extend_period = new ArrayList<>();
        if (cycleDataDTOS.size() >= 2) {
            for (int i = cycleDataDTOS.size() - 1; i > 0; i--) {
                CycleDataDTO lastUserCycleDataDTO = cycleDataDTOS.get(i);
                String date_period_start = lastUserCycleDataDTO.getDate_period_start();
                String date_period_end = lastUserCycleDataDTO.getDate_period_end();
                Integer cycleStatus = lastUserCycleDataDTO.getCycle_status();
                if (StringUtils.isBlank(date_period_end)) {
                    break;
                }
                // 处于预测周期的经期内
                if (cycleStatus == CycleStatusEnum.FORECAST_CYCLE.getStatus() &&
                        today.compareTo(date_period_start) >= 0) {
                    // 在len_cycle的基础上修正
                    lastCycleFlag = LastCycleFlagEnum.TWO.getFlag();
                    UserPeriodDataDTO extendData = new UserPeriodDataDTO();
                    extendData.setDate_period_start(date_period_start);
                    extendData.setDate_period_end(date_period_end);
                    extend_period.add(extendData);
                    break;
                }
            }
        }

        if (lastCycleFlag == LastCycleFlagEnum.TWO.getFlag()) {
            // 构建经期信息
            UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity);
            userPeriodParamDTO.setExtend_period(extend_period);

            // 调用算法
            PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone,
                    loginUserInfoDTO.getEmail(), appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
            prepareEditPeriodDTO.setLastCycleFlag(lastCycleFlag);
            algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.EXTEND_PERIOD);

            // 标记调用了延长经期
            String markKey = RedisCacheKeyConst.EXTEND_PERIOD_MARK.concat(userId.toString());
            redisComponent.setEx(markKey, today, 24, TimeUnit.HOURS);

            // 诊所处理病人相关业务
            patientProducer.periodChange(userId, AlgorithmRequestTypeEnum.EXTEND_PERIOD);
        }
    }
}
