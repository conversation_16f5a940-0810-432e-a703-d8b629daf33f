package com.mira.user.service.user;

import com.mira.api.message.dto.PushTokenDTO;
import com.mira.user.controller.vo.user.UserGoalScheduleOptionVO;
import com.mira.user.dto.info.*;
import com.mira.user.dto.schedule.GoalTestingScheduleDTO;
import com.mira.user.enums.user.EditGoalFlagEnum;

import java.util.List;

/**
 * 用户信息接口
 *
 * <AUTHOR>
 */
public interface IUserInfoService {
    /**
     * 修改用户info信息中的name
     *
     * @param userNameDTO 用户名
     */
    void editName(UserNameDTO userNameDTO);

    /**
     * 保存用户info信息中的birthday
     *
     * @param userBirthdayDTO 用户生日
     */
    void saveBirthday(UserBirthdayDTO userBirthdayDTO);

    /**
     * 保存用户平均经期长度
     *
     * @param userPeriodLengthDTO 用户平均经期长度
     */
    void savePeriodLength(UserPeriodLengthDTO userPeriodLengthDTO);

    /**
     * 编辑用户最近经期
     *
     * @param dates 经期日期
     */
    void savePeriod(List<String> dates);

    /**
     * 保存用户平均周期长度
     *
     * @param userCycleLengthDTO 用户平均周期长度
     */
    void saveCycleLength(UserCycleLengthDTO userCycleLengthDTO);

    /**
     * 保存用户info信息中的goal
     *
     * @param userGoalDTO 用户目标
     */
    void saveGoal(UserGoalDTO userGoalDTO);

    /**
     * 保存用户info信息中的conditions
     *
     * @param userConditionsDTO 用户Conditions
     */
    void saveConditions(UserConditionsDTO userConditionsDTO);

    /**
     * 保存用户info信息中的节育方式
     *
     * @param userHormonalBirthControlDTO 用户节育方式
     */
    void saveHormonalBirthControl(UserHormonalBirthControlDTO userHormonalBirthControlDTO);

    /**
     * 获取goal和testing schedule的备选项
     *
     * @return UserGoalScheduleOptionVO
     */
    UserGoalScheduleOptionVO getGoalScheduleOption();

    /**
     * 修改用户info信息中的goal
     *
     * @param goalTestingScheduleDTO 用户目标
     */
    void editGoal(GoalTestingScheduleDTO goalTestingScheduleDTO, Integer removePregnantInfo,
                  EditGoalFlagEnum editGoalFlagEnum);

    /**
     * 修改用户info信息中的birthday
     *
     * @param userBirthdayDTO 用户Birthday
     */
    void editBirthday(UserBirthdayDTO userBirthdayDTO);

    /**
     * 修改用户info信息中的conditions
     *
     * @param userConditionsDTO 用户Conditions
     */
    void editConditions(UserConditionsDTO userConditionsDTO);

    /**
     * 修改用户info信息中的节育方式
     *
     * @param userHormonalBirthControlDTO 用户节育方式
     */
    void editHormonalBirthControl(UserHormonalBirthControlDTO userHormonalBirthControlDTO);

    /**
     * 修改用户info信息中的pushToken
     *
     * @param pushTokenDTO 用户pushToken
     */
    void editPushToken(PushTokenDTO pushTokenDTO);

    /**
     * 修改用户头像
     *
     * @param userAvatarDTO 用户头像
     */
    void editAvatar(UserAvatarDTO userAvatarDTO);

    /**
     * Confirm change mode并切换到怀孕模式
     */
    void changeModePregnant(EditGoalFlagEnum editGoalFlagEnum);

    /**
     * Confirm change mode并切换到上一个模式
     */
    void changeModeLastMode(EditGoalFlagEnum editGoalFlagEnum, GoalTestingScheduleDTO goalTestingScheduleDTO);

    /**
     * menopause功能新增的接口
     *
     * @param select
     */
    void checkConceive(Boolean select);

    void changeDefinedIrregularCycleValue(Integer value);
}
