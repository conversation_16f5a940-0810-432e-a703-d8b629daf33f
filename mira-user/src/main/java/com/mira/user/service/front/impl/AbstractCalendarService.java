package com.mira.user.service.front.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.wand.DayTestProductsDTO;
import com.mira.api.bluetooth.dto.wand.TestRemindDTO;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.dto.wand.WandTestDataDTO;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.daily.DailyStatusSexEnum;
import com.mira.core.consts.enums.WeightUnitEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.LocalDateUtil;
import com.mira.user.controller.vo.calendar.*;
import com.mira.user.controller.vo.calendar.sub.FutureData;
import com.mira.user.controller.vo.calendar.sub.LogsData;
import com.mira.user.controller.vo.calendar.sub.PastData;
import com.mira.user.controller.vo.diary.UserDiaryMoodsVO;
import com.mira.user.dal.dao.AppUserDiaryDAO;
import com.mira.user.dal.entity.AppUserDiaryEntity;
import com.mira.user.dto.calendar.OvulationCustomDTO;
import com.mira.user.enums.calendar.CalendarViewTypeEnum;
import com.mira.user.service.front.ICalendarService;
import com.mira.user.service.manager.ManualDataManager;
import com.mira.user.service.manager.UserCustomLogManager;
import com.mira.user.service.manager.UserDiaryLogManager;
import com.mira.user.service.util.DayPropertyUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * abstract calendar service
 *
 * <AUTHOR>
 */
public class AbstractCalendarService implements ICalendarService {
    @Resource
    private AppUserDiaryDAO appUserDiaryDAO;
    @Resource
    private IBluetoothProvider bluetoothProvider;
    @Resource
    private UserCustomLogManager userCustomLogManager;
    @Resource
    private UserDiaryLogManager userDiaryLogManager;
    @Resource
    private ManualDataManager manualDataManager;

    protected List<String> buildSexList(Long userId) {
        return appUserDiaryDAO.listSexRecordsByUserId(userId).stream()
                .filter(entity -> DailyStatusSexEnum.C.getValue().equals(entity.getSex())
                        || DailyStatusSexEnum.S.getValue().equals(entity.getSex()))
                .map(AppUserDiaryEntity::getDiaryDayStr)
                .collect(Collectors.toList());
    }



    protected boolean buildDayDataVOCheck(String startDate, String endDate, CycleDataDTO cycleDataDTO) {
        Integer lenCycle = cycleDataDTO.getLen_cycle();
        String datePeriodStart = cycleDataDTO.getDate_period_start();
        return LocalDateUtil.minusToDay(datePeriodStart, endDate) <= 0 &&
                LocalDateUtil.minusToDay(startDate, datePeriodStart) <= lenCycle;
    }

    protected DayDataVO buildDayDataVO(int i, String day, List<String> cycleCdIndex,
                                       List<String> sexDateList, Set<String> testDates) {
        DayDataVO dayDataVO = new DayDataVO();
        dayDataVO.setDay(day);
        if (CollectionUtils.isNotEmpty(cycleCdIndex)) {
            dayDataVO.setCd(cycleCdIndex.get(i - 1));
        }
        if (sexDateList.contains(day)) {
            dayDataVO.setSex(DailyStatusSexEnum.C.getValue());
        } else {
            dayDataVO.setSex(DailyStatusSexEnum.N.getValue());
        }
        if (testDates.contains(day)) {
            dayDataVO.setTest(1);
        } else {
            dayDataVO.setTest(0);
        }

        return dayDataVO;
    }

    protected void buildDayDataVO(String day, String today, List<CycleDataDTO> cycleDataDTOS, DayDataVO dayDataVO) {
        if (LocalDateUtil.minusToDay(day, today) >= 0) {
            // 时间在今日或之后
            DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
            dayTestProductsDTO.setDate(day);
            dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
            dayTestProductsDTO.setWandTestBiomarkerDTOS(new ArrayList<>());
            List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);

            if (!testingDTOS.isEmpty()) {
                dayDataVO.setTestingDTOS(testingDTOS);
            }
        }
    }

    protected String buildDayProperty(Integer userMode, CycleDataDTO cycleDataDTO, String day, int lenCycle) {
        String dayProperty = null;
        UserGoalEnum userGoalEnum = UserGoalEnum.get(userMode);
        if (userGoalEnum == null) {
            return dayProperty;
        }
        switch (userGoalEnum) {
            case TTC:
            case CYCLE_TRACKING:
                dayProperty = DayPropertyUtil.setTTCDayProperty(day, cycleDataDTO);
                break;
            case TTA:
                dayProperty = DayPropertyUtil.setTTADayProperty(day, cycleDataDTO, lenCycle);
                break;
            case OFT:
            case PREGNANCY_TRACKING:
                dayProperty = DayPropertyUtil.setTTCDayProperty(day, cycleDataDTO);
                break;
            default:
                break;
        }
        return dayProperty;
    }

    protected void buildPastTimDayLog(Long userId, String today, String dateStr, CalendarDayLogVO calendarDayLogVO,
                                      List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDatas) {
        PastData pastData = new PastData();

        // 时间在今日之前
        List<HormoneDTO> dailyHormoneDatas = hormoneDatas.stream()
                .filter(hormoneData -> dateStr.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .collect(Collectors.toList());

        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setDate(dateStr);
        wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
        // 取出sameWandMark
        if (CollectionUtils.isNotEmpty(wandTestBiomarkerDTOS)) {
            int size = wandTestBiomarkerDTOS.size();
            ContextHolder.put("sameWandMark", wandTestBiomarkerDTOS.get(size - 1).getSameWandMark());
        }

        List<WandTestBiomarkerDTO> pendingWandTestBiomarkerDTOS = manualDataManager.getPendingWandTestBiomarkerDTOS(userId, dateStr);
        if (!pendingWandTestBiomarkerDTOS.isEmpty()) {
            wandTestBiomarkerDTOS.addAll(pendingWandTestBiomarkerDTOS);
        }

        pastData.setHormones(wandTestBiomarkerDTOS);

        // log
        LogsData logsData = buildDailyLog(userId, dateStr);
        if (logsData == null) {
            logsData = new LogsData();
            calendarDayLogVO.setViewType(CalendarViewTypeEnum.ADD_LOGS.getValue());
        } else {
            calendarDayLogVO.setViewType(CalendarViewTypeEnum.EDIT_LOGS.getValue());
        }
        pastData.setLogsData(logsData);

        // testing
        if (LocalDateUtil.minusToDay(dateStr, today) == 0) {
            DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
            dayTestProductsDTO.setDate(dateStr);
            dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
            dayTestProductsDTO.setWandTestBiomarkerDTOS(wandTestBiomarkerDTOS);
            List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);
            pastData.setTestingDTOS(testingDTOS);
        }

        calendarDayLogVO.setPastData(pastData);
    }

    protected void buildFutureTimeDayLog(String dateStr, CalendarDayLogVO calendarDayLogVO,
                                         List<CycleDataDTO> cycleDataDTOS) {
        // 时间在今日之后(不包含今日)
        DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
        dayTestProductsDTO.setDate(dateStr);
        dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
        dayTestProductsDTO.setWandTestBiomarkerDTOS(new ArrayList<>());
        List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);

        if (testingDTOS.isEmpty()) {
            calendarDayLogVO.setViewType(CalendarViewTypeEnum.FUTURE_NOT_TESTING_DAY.getValue());
        } else {
            calendarDayLogVO.setViewType(CalendarViewTypeEnum.FUTURE_TESTING_DAY.getValue());
            FutureData futureData = new FutureData();
            futureData.setTestingDTOS(testingDTOS);
            calendarDayLogVO.setFutureData(futureData);
        }
    }

    protected LogsData buildDailyLog(Long userId, String dateStr) {
        LogsData logsData = new LogsData();

        AppUserDiaryEntity appUserDiaryEntity = appUserDiaryDAO.getByUserIdAndDayStr(userId, dateStr);
        List<UserSymptomDTO> userSymptomDTOS = userDiaryLogManager.listUserSymptomDTO(userId, dateStr);
        List<TemperatureDTO> temperatureDTOS = userDiaryLogManager.listTemperatureDTO(userId, dateStr);
        UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryLogManager.getUserDiaryMoodsDTO(userId, dateStr);

        if (ObjectUtils.isEmpty(appUserDiaryEntity)
                && CollectionUtils.isEmpty(userSymptomDTOS)
                && CollectionUtils.isEmpty(temperatureDTOS)
                && ObjectUtils.isEmpty(userDiaryMoodsDTO)) {
            return null;
        }

        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        String weightUnit = customLogConfigDTO.getWeightUnit();
        logsData.setWeightUnit(weightUnit);
        String tempUnit = customLogConfigDTO.getTempUnit();
        logsData.setTempUnit(tempUnit);
        logsData.setTemperatures(temperatureDTOS);

        if (ObjectUtils.isNotEmpty(appUserDiaryEntity)) {
            BeanUtil.copyProperties(appUserDiaryEntity, logsData, CopyOptions.create().ignoreError());

            BigDecimal weightK = appUserDiaryEntity.getWeightK();
            if (weightK != null) {
                if (WeightUnitEnum.K.getValue().equals(weightUnit)) {
                    logsData.setWeightValue(appUserDiaryEntity.getWeightK());
                } else {
                    logsData.setWeightValue(appUserDiaryEntity.getWeightL());
                }
            }
        }

        Boolean medicationsConfig = customLogConfigDTO.getMedications();
        if (medicationsConfig) {
            logsData.setMedications(userDiaryLogManager.listUserMedicine(userId, dateStr));
        }
        logsData.setSymptoms(userSymptomDTOS);
        logsData.setAppUserDiaryMoodsVO(BeanUtil.toBean(userDiaryMoodsDTO, UserDiaryMoodsVO.class));

        return logsData;
    }

    @Override
    public CalendarDataVO calendarData(String startDate, String endDate) {
        return null;
    }

    @Override
    public V5CalendarDataVO calendarDataV5(String startDate, String endDate) {
        return null;
    }

    @Override
    public CalendarDayLogVO calendarDayLog(String dateStr) {
        return null;
    }

    @Override
    public V5CalendarDayLogVO calendarDayLogV5(String dateStr) {
        return null;
    }

    @Override
    public Integer sexConfig() {
        return 0;
    }

    @Override
    public Integer sexConfigEdit(Integer display) {
        return 0;
    }

    @Override
    public DayTestSuggestVO dayTestSuggest(String date) {
        return null;
    }

    @Override
    public void ovulationEdit(OvulationCustomDTO ovulationCustomDTO) {

    }

    @Override
    public CycleInfoVO cycleInfo() {
        return null;
    }
}
