package com.mira.user.service.survey.condition;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.mongo.dto.SurveyCondition;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <p>
 * Wand errors
 * <br/>
 * 试剂错误，1:1 in a cycle, 2:2 in a cycle, 3:3 in a cycle, 4:4 in a cycle, 5:>4 in a cycle
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-16
 **/
@Slf4j
public class WandErrorCondition {
    private final static int ONE_IN_CYCLE = 1;
    private final static int TWO_IN_CYCLE = 2;
    private final static int THREE_IN_CYCLE = 3;
    private final static int FOUR_IN_CYCLE = 4;
    private final static int GT_4_CYCLE = 5;


    public static boolean checkWandError(Long userId, String today, List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS, SurveyCondition surveyCondition) {
        Integer wandErrorCondition = surveyCondition.getWandError();
        if (wandErrorCondition == null || wandErrorCondition == -1) {
            return true;
        }
        if (cycleDataDTOS.isEmpty()) {
            return false;
        }
        try {
            // cycle
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            // hormone
            List<HormoneDTO> currentCycleHormones = CycleDataUtil.hormoneByCurrentCycle(currentCycleData, hormoneDTOS);
            // wand error count
            long wandErrorCountByUser = currentCycleHormones.stream()
                    .filter(hormone -> hormone.getFlag() == 0)
                    .count();
            if (ONE_IN_CYCLE == wandErrorCondition
                    && ONE_IN_CYCLE != wandErrorCountByUser) {
                return false;
            }
            if (TWO_IN_CYCLE == wandErrorCondition
                    && TWO_IN_CYCLE != wandErrorCountByUser) {
                return false;
            }
            if (THREE_IN_CYCLE == wandErrorCondition
                    && THREE_IN_CYCLE != wandErrorCountByUser) {
                return false;
            }
            if (FOUR_IN_CYCLE == wandErrorCondition
                    && FOUR_IN_CYCLE != wandErrorCountByUser) {
                return false;
            }
            if (GT_4_CYCLE == wandErrorCondition
                    && wandErrorCountByUser <= FOUR_IN_CYCLE) {
                return false;
            }
        } catch (Exception e) {
            log.error("【SurveyCondition】user:{} wand error handler error.", userId, e);
            return false;
        }
        return true;
    }
}
