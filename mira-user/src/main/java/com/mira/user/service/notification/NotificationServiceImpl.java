package com.mira.user.service.notification;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.message.consts.RateStarConst;
import com.mira.api.message.dto.PageNotificationRecordDTO;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.request.PageDTO;
import com.mira.mybatis.response.PageResult;
import com.mira.redis.cache.RedisComponent;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.controller.vo.user.NotificationRecordVO;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.entity.AppUserInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 系统通知记录接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationServiceImpl implements INotificationService {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private IMessageProvider messageProvicer;
    @Resource
    private KlaviyoProducer klaviyoProducer;
    @Resource
    private RedisComponent redisComponent;

    @Override
    public Boolean redPoint() {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        switch (userTypeEnum) {
            case APP_USER:
                return messageProvicer.isAllRead(loginInfo.getId()).getData();
            case PARTNER_USER:
                return null;
        }

        return null;
    }

    @Override
    public PageResult<NotificationRecordVO> page(PageDTO pageDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        switch (userTypeEnum) {
            case APP_USER:
                PageNotificationRecordDTO recordDTO = messageProvicer.getNotificationList(loginInfo.getId(), pageDTO.getCurrent(), pageDTO.getSize()).getData();
                if (CollectionUtils.isNotEmpty(recordDTO.getList())) {
                    List<NotificationRecordVO> voList = recordDTO.getList().stream()
                            .map(dto -> BeanUtil.toBean(dto, NotificationRecordVO.class))
                            .collect(Collectors.toList());
                    return new PageResult<>(voList, recordDTO.getTotal(), pageDTO.getSize(), pageDTO.getCurrent());
                }
            case PARTNER_USER:
                return new PageResult<>();
        }

        return new PageResult<>();
    }

    @Override
    public void close(Long recordId) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        switch (userTypeEnum) {
            case APP_USER:
                if (recordId == -1L) {
                    messageProvicer.updateAllRead(loginInfo.getId());
                } else {
                    messageProvicer.updateRead(loginInfo.getId(), recordId);
                }
                break;
            case PARTNER_USER:
                break;
        }
    }

    @Override
    public void closePush(Long notificationDefineId, Long userId, Long partnerId) {
        if (userId != null) {
            messageProvicer.updateReadByDefinedId(userId, notificationDefineId);
        }
    }

    @Override
    public void rateTheApp(Integer score) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        if (userTypeEnum == UserTypeEnum.APP_USER) {
            messageProvicer.rateTheApp(loginInfo.getId(), score);
        }

        // klaviyo
        if (RateStarConst.FOUR_STAR.equals(score)
                || RateStarConst.FIVE_STAR.equals(score)) {
            klaviyoProducer.rateTheApp(loginInfo.getId(), score);
        }
    }

    @Override
    public void remindTomorrowPeriodStartingSoon() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        if (appUserInfo == null) {
            log.info("user:{}, user info is empty", userId);
            return;
        }

        UserGoalEnum userGoalEnum = UserGoalEnum.get(appUserInfo.getGoalStatus());
        if (userGoalEnum == null) {
            log.info("user:{}, user goal is null", userId);
            return;
        }

        NotificationDefineEnum notificationDefineEnum;
        switch (userGoalEnum) {
            case TTC:
                notificationDefineEnum = NotificationDefineEnum.PERIOD_STARTING_SOON_TTC;
                break;
            case TTA:
                notificationDefineEnum = NotificationDefineEnum.PERIOD_STARTING_SOON_TTA;
                break;
            case OFT:
                notificationDefineEnum = NotificationDefineEnum.PERIOD_STARTING_SOON_TTC;
                break;
            case CYCLE_TRACKING:
                notificationDefineEnum = NotificationDefineEnum.PERIOD_STARTING_SOON_CYCLETRACKING;
                break;
            default:
                notificationDefineEnum = null;
        }

        if (notificationDefineEnum == null) {
            log.info("user:{}, goal:{}, notification define id is null", userId, userGoalEnum.getValue());
            return;
        }

        /*
        key, REPEAT_PUSH:user_id:define_id
        过期时间，1天
         */
        String key = RedisCacheKeyConst.REPEAT_PUSH + userId + ":" + notificationDefineEnum.getDefineId();
        redisComponent.setEx(key, "1", 1, TimeUnit.DAYS);
    }

    @Override
    public void remindTomorrowPredictedPeriodStarted() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        if (appUserInfo == null) {
            log.info("user:{}, user info is empty", userId);
            return;
        }

        UserGoalEnum userGoalEnum = UserGoalEnum.get(appUserInfo.getGoalStatus());
        if (userGoalEnum == null) {
            log.info("user:{}, user goal is null", userId);
            return;
        }

        NotificationDefineEnum notificationDefineEnum;
        switch (userGoalEnum) {
            case TTC:
                notificationDefineEnum = NotificationDefineEnum.PREDICTED_PERIOD_STARTED_TTC;
                break;
            case TTA:
                notificationDefineEnum = NotificationDefineEnum.PREDICTED_PERIOD_STARTED_TTA;
                break;
            case OFT:
                notificationDefineEnum = NotificationDefineEnum.PREDICTED_PERIOD_STARTED_TTC;
                break;
            case CYCLE_TRACKING:
                notificationDefineEnum = NotificationDefineEnum.PREDICTED_PERIOD_STARTED_CYCLETRACKING;
                break;
            default:
                notificationDefineEnum = null;
        }

        if (notificationDefineEnum == null) {
            log.info("user:{}, goal:{}, notification define id is null", userId, userGoalEnum.getValue());
            return;
        }

        String key = RedisCacheKeyConst.REPEAT_PUSH + userId + ":" + notificationDefineEnum.getDefineId();
        redisComponent.setEx(key, "1", 1, TimeUnit.DAYS);
    }

    @Override
    public void remindTomorrowFirmware() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        String key = RedisCacheKeyConst.REPEAT_PUSH + userId + ":"
                + NotificationDefineEnum.FIRMWARE_UPDATE.getDefineId();
        redisComponent.setEx(key, "1", 1, TimeUnit.DAYS);
    }

    @Override
    public void subscriptionStep2() {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(loginInfo.getId());

        messageProvicer.expireNotification(loginInfo.getId(), 162L);

        messageProvicer.sendNotification(new PushNotificationDTO()
                .setUserId(loginInfo.getId())
                .setTimeZone(loginInfo.getTimeZone())
                .setPlatform(appUserInfo.getPlatform())
                .setDefineId(163L)
                .setPushFirebase(Boolean.TRUE)
                .setSaveRecord(Boolean.TRUE)
                .setTokens(Collections.singletonList(appUserInfo.getPushToken())));
    }

    @Override
    public void subscriptionRemind() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        String key = "subscription_remind";
        redisComponent.sAdd(key, userId);
    }

}
