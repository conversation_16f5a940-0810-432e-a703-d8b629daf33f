package com.mira.user.service.doctor;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mira.api.bluetooth.consts.DefaultWandsParamConst;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.daily.DailyHormoneDTO;
import com.mira.api.bluetooth.dto.daily.UserDailyDataDTO;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.iam.dto.AuthTokenDTO;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.PasswordUtil;
import com.mira.mybatis.response.PageResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.api.user.consts.UserSourceConst;
import com.mira.user.controller.vo.doctor.GeneaAccountVO;
import com.mira.user.controller.vo.doctor.GeneaHormoneVO;
import com.mira.user.dal.dao.AppUserDAO;
import com.mira.user.dal.dao.AppUserExtraGeneaDAO;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.UserDataAnalyzerDAO;
import com.mira.user.dal.entity.AppUserEntity;
import com.mira.user.dal.entity.AppUserExtraGeneaEntity;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.UserDataAnalyzerEntity;
import com.mira.user.dto.doctor.*;
import com.mira.user.exception.UserException;
import com.mira.user.service.manager.CacheManager;
import com.mira.web.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * Doctor接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class DoctorServiceImpl implements IDoctorService {
    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private AppUserExtraGeneaDAO appUserExtraGeneaDAO;
    @Resource
    private UserDataAnalyzerDAO userDataAnalyzerDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private KlaviyoProducer klaviyoProducer;

    @Resource
    private IBluetoothProvider bluetoothProvider;
    @Resource
    private IAuthProvider authProvider;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createAccount(DoctorAccountDTO doctorAccountDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        String timeZone = loginInfo.getTimeZone();

        String tenant = doctorAccountDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        Long doctorUserId = loginInfo.getId();
        String patientNumber = StringUtils.trim(doctorAccountDTO.getPatientNumber());
        String initialPassword = doctorAccountDTO.getPassword();

        if (!PasswordUtil.checkRules(initialPassword)) {
            throw new UserException("Password must be 8-30 characters with both numbers and letters.");
        }

        AppUserExtraGeneaEntity geneaEntity = appUserExtraGeneaDAO.getByPatientNumberAndTenant(patientNumber, tenant);
        if (geneaEntity != null) {
            throw new UserException("Patient Number already exists.");
        }
        String email = tenant + "-" + patientNumber + "@miracare.com";

        // 创建 app_user
        AppUserEntity user = new AppUserEntity();
        user.setName(email);
        user.setSalt(PasswordUtil.generateSalt(20));
        user.setPassword(PasswordUtil.encryptPassword(initialPassword, user.getSalt()));
        user.setEmail(email);
        user.setStatus(1);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, user);
        user.setSource(2); // 表示Genea医生创建的用户
        user.setCreator(doctorUserId);
        user.setModifier(doctorUserId);
        appUserDAO.save(user);

        // klaviyo
        klaviyoProducer.accountCreated(user);

        // 创建 app_user_info
        AppUserInfoEntity userInfoEntity = new AppUserInfoEntity();
        userInfoEntity.setUserId(user.getId());
        userInfoEntity.setNotifications(1);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userInfoEntity);
        userInfoEntity.setCreator(doctorUserId);
        userInfoEntity.setModifier(doctorUserId);
        appUserInfoDAO.save(userInfoEntity);

        // 创建 app_user_extra_genea
        geneaEntity = new AppUserExtraGeneaEntity();
        geneaEntity.setPatientNumber(patientNumber);
        geneaEntity.setTenant(tenant);
        geneaEntity.setInitialPassword(initialPassword);
        geneaEntity.setEmail(email);
        geneaEntity.setUserId(user.getId());
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, geneaEntity);
        geneaEntity.setCreator(doctorUserId);
        geneaEntity.setModifier(doctorUserId);
        appUserExtraGeneaDAO.save(geneaEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void noteUpdate(DoctorEditNotesDTO doctorEditNotesDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        String timeZone = loginInfo.getTimeZone();

        String tenant = doctorEditNotesDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        Long doctorUserId = loginInfo.getId();
        String patientNumber = doctorEditNotesDTO.getPatientNumber();
        String notes = doctorEditNotesDTO.getNotes();

        AppUserExtraGeneaEntity geneaEntity = appUserExtraGeneaDAO.getByPatientNumberAndTenant(patientNumber, tenant);
        geneaEntity.setNotes(notes);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, geneaEntity);
        geneaEntity.setModifier(doctorUserId);
        appUserExtraGeneaDAO.updateById(geneaEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetPassword(DoctorAccountDTO doctorAccountDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        String timeZone = loginInfo.getTimeZone();

        String tenant = doctorAccountDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        Long doctorUserId = loginInfo.getId();
        String patientNumber = doctorAccountDTO.getPatientNumber();
        String initialPassword = doctorAccountDTO.getPassword();

        if (!PasswordUtil.checkRules(initialPassword)) {
            throw new UserException("Password must be 8-30 characters with both numbers and letters.");
        }

        // update app_user_extra_genea
        AppUserExtraGeneaEntity geneaEntity = appUserExtraGeneaDAO.getByPatientNumberAndTenant(patientNumber, tenant);
        geneaEntity.setInitialPassword(initialPassword);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, geneaEntity);
        geneaEntity.setModifier(doctorUserId);
        appUserExtraGeneaDAO.updateById(geneaEntity);

        // update app_user
        AppUserEntity user = appUserDAO.getById(geneaEntity.getUserId());
        user.setSalt(PasswordUtil.generateSalt(20));
        user.setPassword(PasswordUtil.encryptPassword(initialPassword, user.getSalt()));
        appUserDAO.updateById(user);
    }

    @Override
    public PageResult page(DoctorPageDTO doctorPageDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        String tenant = doctorPageDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        Page page = appUserExtraGeneaDAO
                .pageByTenant(doctorPageDTO.getCurrent(), doctorPageDTO.getSize(), tenant);
        if (ObjectUtils.isEmpty(page)) {
            return new PageResult<>();
        }

        List<AppUserExtraGeneaEntity> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<GeneaAccountVO> geneaAccountVOS = new ArrayList<>();
            records.stream().forEach(appUserExtraGeneaEntity -> {
                GeneaAccountVO geneaAccountVO = new GeneaAccountVO();
                BeanUtil.copyProperties(appUserExtraGeneaEntity, geneaAccountVO);
                geneaAccountVO.setAccountCreateTime(appUserExtraGeneaEntity.getCreateTimeStr());
                geneaAccountVOS.add(geneaAccountVO);
            });
            page.setRecords(geneaAccountVOS);
            return new PageResult(page);
        }
        return new PageResult(page);
    }

    @Override
    public PageResult<GeneaHormoneVO> dataPage(DoctorDataPageDTO doctorDataPageDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        String tenant = doctorDataPageDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        Long userId = doctorDataPageDTO.getUserId();
        AppUserEntity appUserEntity = appUserDAO.getById(userId);
        Integer source = appUserEntity.getSource();
        Integer transferFlag = appUserEntity.getTransferFlag();
        Integer turnFlag = UserSourceConst.ZERO;
        if ((source == UserSourceConst.ONE || source == UserSourceConst.TWO || source == UserSourceConst.THREE)
                && transferFlag == 0) {
            turnFlag = UserSourceConst.THREE;
        } else if (source == UserSourceConst.FOUR || transferFlag == 2) {
            turnFlag = UserSourceConst.FOUR;
        }

        List<GeneaHormoneVO> geneaHormoneVOS = getV4Results(userId);
        //        if (turnFlag == UserSourceConst.THREE) {
        //            geneaHormoneVOS = getV3Results(userId);
        //        } else if (turnFlag == UserSourceConst.FOUR) {
        //            geneaHormoneVOS = getV4Results(userId);
        //        }

        if (org.springframework.util.CollectionUtils.isEmpty(geneaHormoneVOS)) {
            return new PageResult<>();
        }
        Integer size = doctorDataPageDTO.getSize();
        Integer current = doctorDataPageDTO.getCurrent();
        Integer total = geneaHormoneVOS.size();
        Integer endIndex = current * size < total ? current * size : total;
        List<GeneaHormoneVO> subGeneaHormoneVOS = geneaHormoneVOS.subList((current - 1) * size, endIndex);
        PageResult<GeneaHormoneVO> geneaHormoneVOPageResult = new PageResult<>(
                subGeneaHormoneVOS, total, size, current);

        return geneaHormoneVOPageResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refresh(DoctorBaseDTO doctorBaseDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        String tenant = doctorBaseDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        List<AppUserExtraGeneaEntity> geneaEntityList = appUserExtraGeneaDAO.listByTenantAndActive(tenant, 1);
        if (CollectionUtils.isEmpty(geneaEntityList)) {
            return;
        }

        List<AppUserExtraGeneaEntity> updateGeneaEntities = new ArrayList<>();
        for (AppUserExtraGeneaEntity geneaEntity : geneaEntityList) {
            String email = geneaEntity.getEmail();
            //            List<String> emailList = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");
            //            if (!emailList.contains(email)) {
            //                continue;
            //            }
            Long userId = geneaEntity.getUserId();
            AppUserEntity appUserEntity = appUserDAO.getById(userId);
            if (appUserEntity == null) {
                continue;
            }
            Integer source = appUserEntity.getSource();
            Integer transferFlag = appUserEntity.getTransferFlag();

            Integer turnFlag = UserSourceConst.ZERO;
            if ((source == UserSourceConst.ONE || source == UserSourceConst.TWO || source == UserSourceConst.THREE)
                    && transferFlag == 0) {
                turnFlag = UserSourceConst.THREE;
            } else if (source == UserSourceConst.FOUR || transferFlag == 2) {
                turnFlag = UserSourceConst.FOUR;
            }
            //            if (turnFlag == UserSourceConst.THREE) {
            //                addV3UpdateGeneaEntities(updateGeneaEntities, geneaEntity);
            //            } else if (turnFlag == UserSourceConst.FOUR) {
            //                addV4UpdateGeneaEntities(updateGeneaEntities, geneaEntity);
            //            }
            try {
                addV4UpdateGeneaEntities(updateGeneaEntities, geneaEntity);
            } catch (Exception e) {
                log.error("refresh error for tenant:【{}】patient email:【{}】", tenant, email);
            }

        }

        if (CollectionUtils.isNotEmpty(updateGeneaEntities)) {
            appUserExtraGeneaDAO.updateBatchById(updateGeneaEntities);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void active(DoctorActiveDTO doctorActiveDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        String tenant = doctorActiveDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(loginInfo.getUsername())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        Long doctorUserId = loginInfo.getId();
        String patientNumber = doctorActiveDTO.getPatientNumber();
        Integer active = doctorActiveDTO.getActive();
        AppUserExtraGeneaEntity geneaEntity = appUserExtraGeneaDAO.getByPatientNumberAndTenant(patientNumber, tenant);
        geneaEntity.setActive(active);
        UpdateEntityTimeUtil.updateBaseEntityTime(loginInfo.getTimeZone(), geneaEntity);
        geneaEntity.setModifier(doctorUserId);
        appUserExtraGeneaDAO.updateById(geneaEntity);
    }

    @Override
    public String login(DoctorLoginDTO doctorLoginDTO) throws Exception {
        String tenant = doctorLoginDTO.getTenant().toLowerCase(Locale.ROOT);
        if (!(tenant + "@miracare.com").equalsIgnoreCase(doctorLoginDTO.getEmail())) {
            throw new UserException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        // 用户信息
        AppUserEntity user = appUserDAO.getByEmail(doctorLoginDTO.getEmail());
        // 账号不存在
        if (user == null) {
            throw new UserException("The account doesn't exist.");
        }

        Integer testMode = user.getTestMode();
        if (testMode != 1) {
            if (!PasswordUtil.match(doctorLoginDTO.getPassword(), user.getPassword(), user.getSalt())) {
                throw new UserException("Email address or password don't match.");
            }
        }

        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.APP_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(user.getEmail(), user.getPassword());

        return user.getId().toString().concat("-").concat(tokenResult.getData().getAccess_token());
    }

    @Override
    public void logout() {
        List<String> tokenHeaderList = UserTypeEnum.CLINIC_USER.getTokenHeaderList();
        tokenHeaderList.addAll(UserTypeEnum.COMPATIBLE_CLINIC_USER.getTokenHeaderList());
        for (String tokenHeader : tokenHeaderList) {
            String authorization = RequestUtil.getRequest().getHeader(tokenHeader);
            if (StringUtils.isNotEmpty(authorization)) {
                authProvider.deleteToken(authorization, UserTypeEnum.CLINIC_USER.getType());
            }
        }
    }

    private List<GeneaHormoneVO> getV3Results(Long userId) {
        UserDataAnalyzerEntity userDataAnalyzerEntity = userDataAnalyzerDAO.getByUserId(userId);
        if (userDataAnalyzerEntity == null) {
            return null;
        }

        List<UserDailyDataDTO> userDailyDataDTOS = JsonUtil.toArray(userDataAnalyzerEntity.getDailyData(), UserDailyDataDTO.class);
        List<GeneaHormoneVO> geneaHormoneVOS = new ArrayList<>();
        for (UserDailyDataDTO userDailyDataDTO : userDailyDataDTOS) {
            List<DailyHormoneDTO> hormones = userDailyDataDTO.getHormones();
            if (hormones != null) {
                for (DailyHormoneDTO userHormoneDTO : hormones) {
                    GeneaHormoneVO geneaHormoneVO = new GeneaHormoneVO();
                    String test_time = userHormoneDTO.getTest_time();
                    geneaHormoneVO.setTestTime(test_time);
                    DailyHormoneDTO.TestResult test_results = userHormoneDTO.getTest_results();
                    String wandBatch3 = userHormoneDTO.getWandBatch3();
                    buildTesultResultVO(test_results, geneaHormoneVO, wandBatch3);
                    if (StringUtils.isBlank(geneaHormoneVO.getEcode())) {
                        geneaHormoneVOS.add(geneaHormoneVO);
                    }
                }
            }
        }
        if (!geneaHormoneVOS.isEmpty()) {
            Collections.reverse(geneaHormoneVOS);
        }
        return geneaHormoneVOS;
    }

    private List<GeneaHormoneVO> getV4Results(Long userId) {
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        if (algorithmResultDTO == null) {
            return null;
        }

        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        List<GeneaHormoneVO> geneaHormoneVOS = new ArrayList<>();
        for (HormoneDTO hormoneDTO : hormoneDatas) {
            String testTime = hormoneDTO.getTest_time();
            String wandBatch3 = hormoneDTO.getWandBatch3();
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            Integer wandType = testResult.getWand_type();
            if (StringUtils.isBlank(testResult.getEcode()) || testResult.getEcode().startsWith("B")) {
                GeneaHormoneVO geneaHormoneVO = new GeneaHormoneVO();
                geneaHormoneVO.setTestTime(testTime);
                if (WandTypeEnum.LH.getInteger().equals(wandType)) {
                    geneaHormoneVO.setLH(testResult.getValue1());
                    geneaHormoneVOS.add(geneaHormoneVO);
                } else if (WandTypeEnum.E3G_LH.getInteger().equals(wandType)) {
                    // 显示两条
                    geneaHormoneVO.setE3G(testResult.getValue1());
                    geneaHormoneVO.setLH(testResult.getValue2());
                    geneaHormoneVOS.add(geneaHormoneVO);
                } else if (WandTypeEnum.HCG.getInteger().equals(wandType)) {
                    geneaHormoneVO.setHCG(testResult.getValue1());
                    geneaHormoneVOS.add(geneaHormoneVO);
                } else if (WandTypeEnum.PDG.getInteger().equals(wandType)) {
                    geneaHormoneVO.setPDG(testResult.getValue1());
                    geneaHormoneVOS.add(geneaHormoneVO);
                } else if (WandTypeEnum.E3G_HIGH_RANGE.getInteger().equals(wandType)) {
                    geneaHormoneVO.setE3G(testResult.getValue1());
                    geneaHormoneVOS.add(geneaHormoneVO);
                } else if (WandTypeEnum.LH_E3G_PDG.getInteger().equals(wandType)) {
                    geneaHormoneVO.setE3G(testResult.getValue3());
                    geneaHormoneVO.setLH(testResult.getValue1());
                    geneaHormoneVO.setPDG(testResult.getValue2());
                    geneaHormoneVOS.add(geneaHormoneVO);
                }
            }
        }
        if (!geneaHormoneVOS.isEmpty()) {
            Collections.reverse(geneaHormoneVOS);
        }
        return geneaHormoneVOS;
    }

    /**
     * 构造TesultResultVO，将测试数据和试剂参数比对，筛选异常数据
     */
    private void buildTesultResultVO(DailyHormoneDTO.TestResult test_results,
                                     GeneaHormoneVO geneaHormoneVO,
                                     String wandBatch3) {
        if (StringUtils.isBlank(wandBatch3)) {
            if (test_results.getE3G() != null) {
                geneaHormoneVO.setE3G(test_results.getE3G());
            }
            if (test_results.getLH() != null) {
                geneaHormoneVO.setLH(test_results.getLH());
            }
            if (test_results.getHCG() != null) {
                geneaHormoneVO.setHCG(test_results.getHCG());
            }
            if (test_results.getPDG() != null) {
                geneaHormoneVO.setPDG(test_results.getPDG());
            }
            if (StringUtils.isNotBlank(test_results.getEcode())) {
                geneaHormoneVO.setEcode(test_results.getEcode());
            }
            return;
        }

        String uStripType = WandTypeEnum.LH.getString();
        if (test_results.getE3G() != null) {
            uStripType = WandTypeEnum.E3G_LH.getString();
        } else if (test_results.getHCG() != null) {
            uStripType = WandTypeEnum.HCG.getString();
        } else if (test_results.getPDG() != null) {
            uStripType = WandTypeEnum.PDG.getString();
        }

        Float e3G = test_results.getE3G();
        Float lh = test_results.getLH();
        Float hcg = test_results.getHCG();
        Float pdg = test_results.getPDG();
        String ecode = test_results.getEcode();

        WandsParamRecordDTO wandsParamRecordDTO = bluetoothProvider.getWandParamRecord(wandBatch3, uStripType).getData();
        Float lhLowerLimit = DefaultWandsParamConst.LH_LOWER_LIMIT;
        Float lhUpperLimit = DefaultWandsParamConst.LH_UPPER_LIMIT;
        Float e3gLowerLimit = DefaultWandsParamConst.E3G_LOWER_LIMIT;
        Float e3gUpperLimit = DefaultWandsParamConst.E3G_UPPER_LIMIT;
        Float hcgLowerLimit = DefaultWandsParamConst.HCG_LOWER_LIMIT;
        Float hcgUpperLimit = DefaultWandsParamConst.HCG_UPPER_LIMIT;
        Float pdgLowerLimit = DefaultWandsParamConst.PDG_LOWER_LIMIT;
        Float pdgUpperLimit = DefaultWandsParamConst.PDG_UPPER_LIMIT;

        if (ObjectUtils.isNotEmpty(wandsParamRecordDTO)) {
            if (uStripType.equals(WandTypeEnum.LH.getString())) {
                lhLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                lhUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
            } else if (uStripType.equals(WandTypeEnum.E3G_LH.getString())) {
                lhLowerLimit = wandsParamRecordDTO.getFLowerLimit3();
                lhUpperLimit = wandsParamRecordDTO.getFUpperLimit3();
                e3gLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                e3gUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
            } else if (uStripType.equals(WandTypeEnum.HCG.getString())) {
                hcgUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
                hcgLowerLimit = wandsParamRecordDTO.getFLowerLimit3();
            }
        }

        if (WandTypeEnum.HCG.getString().equals(uStripType)) {
            if (hcgLowerLimit >= hcg) {
                geneaHormoneVO.setHCG(hcgLowerLimit);
            } else if (hcgUpperLimit <= hcg) {
                geneaHormoneVO.setHCG(hcgUpperLimit);
            } else {
                geneaHormoneVO.setHCG(test_results.getHCG());
            }
        } else if (WandTypeEnum.E3G_LH.getString().equals(uStripType)) {
            if (e3gLowerLimit >= e3G) {
                geneaHormoneVO.setE3G(e3gLowerLimit);
            } else if (e3gUpperLimit <= e3G) {
                geneaHormoneVO.setE3G(e3gUpperLimit);
            } else {
                geneaHormoneVO.setE3G(e3G);
            }
            if (lhLowerLimit >= lh) {
                geneaHormoneVO.setLH(lhLowerLimit);
            } else if (lhUpperLimit <= lh) {
                geneaHormoneVO.setLH(lhUpperLimit);
            } else {
                geneaHormoneVO.setLH(lh);
            }
        } else if (WandTypeEnum.LH.getString().equals(uStripType)) {
            if (lhLowerLimit >= lh) {
                geneaHormoneVO.setLH(lhLowerLimit);
            } else if (lhUpperLimit <= lh) {
                geneaHormoneVO.setLH(lhUpperLimit);
            } else {
                geneaHormoneVO.setLH(lh);
            }
        } else if (WandTypeEnum.PDG.getString().equals(uStripType)) {
            if (pdgLowerLimit >= pdg) {
                geneaHormoneVO.setPDG(pdgLowerLimit);
            } else if (pdgUpperLimit <= pdg) {
                geneaHormoneVO.setPDG(pdgUpperLimit);
            } else {
                geneaHormoneVO.setPDG(pdg);
            }
        }

        if (StringUtils.isNotBlank(ecode)) {
            geneaHormoneVO.setEcode(test_results.getEcode());
        }
    }

    private void addV3UpdateGeneaEntities(List<AppUserExtraGeneaEntity> updateGeneaEntities, AppUserExtraGeneaEntity geneaEntity) {
        // 用户有效的测试数据数量
        Integer resultsCount = 0;
        // 用户最近一次测量时间
        String lastTestTimeStr = geneaEntity.getLastTestTime();
        UserDataAnalyzerEntity userDataAnalyzerEntity = userDataAnalyzerDAO.getByUserId(geneaEntity.getUserId());
        if (ObjectUtils.isEmpty(userDataAnalyzerEntity)) {
            return;
        }

        List<UserDailyDataDTO> userDailyDataDTOS = JsonUtil.toArray(userDataAnalyzerEntity.getDailyData(), UserDailyDataDTO.class);
        for (UserDailyDataDTO userDailyDataDTO : userDailyDataDTOS) {
            if (CollectionUtils.isNotEmpty(userDailyDataDTO.getHormones())) {
                List<DailyHormoneDTO> hormones = userDailyDataDTO.getHormones();
                for (DailyHormoneDTO userHormoneDTO : hormones) {
                    DailyHormoneDTO.TestResult test_results = userHormoneDTO.getTest_results();
                    if (StringUtils.isBlank(test_results.getEcode())) {
                        resultsCount++;
                        String test_time_str = userHormoneDTO.getTest_time();
                        if (StringUtils.isBlank(lastTestTimeStr)) {
                            lastTestTimeStr = test_time_str;
                        } else {
                            if (LocalDateUtil.after(test_time_str, lastTestTimeStr, DatePatternConst.DATE_TIME_PATTERN)) {
                                lastTestTimeStr = test_time_str;
                            }
                        }
                    }
                }
            }
        }

        geneaEntity.setResultsCount(resultsCount);
        geneaEntity.setLastTestTime(lastTestTimeStr);
        updateGeneaEntities.add(geneaEntity);
    }

    private void addV4UpdateGeneaEntities(List<AppUserExtraGeneaEntity> updateGeneaEntities, AppUserExtraGeneaEntity geneaEntity) {
        // 用户有效的测试数据数量
        Integer resultsCount = 0;
        // 用户最近一次测量时间
        String lastTestTimeStr = geneaEntity.getLastTestTime();
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(geneaEntity.getUserId());
        if (ObjectUtils.isEmpty(algorithmResultDTO)) {
            return;
        }

        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        for (HormoneDTO hormoneDTO : hormoneDatas) {
            String testTime = hormoneDTO.getTest_time();
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            if (StringUtils.isBlank(testResult.getEcode()) || testResult.getEcode().startsWith("B")) {
                resultsCount++;
                if (StringUtils.isBlank(lastTestTimeStr)) {
                    lastTestTimeStr = testTime;
                } else {
                    if (LocalDateUtil.after(testTime, lastTestTimeStr, DatePatternConst.DATE_TIME_PATTERN)) {
                        lastTestTimeStr = testTime;
                    }
                }
            }
        }

        geneaEntity.setResultsCount(resultsCount);
        geneaEntity.setLastTestTime(lastTestTimeStr);
        updateGeneaEntities.add(geneaEntity);
    }
}
