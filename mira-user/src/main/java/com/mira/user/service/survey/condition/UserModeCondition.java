package com.mira.user.service.survey.condition;

import com.mira.api.mongo.dto.SurveyCondition;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-16
 **/
public class UserModeCondition {
    /**
     * 1. 用户模式
     *
     * @param goalStatus
     * @param surveyCondition
     * @return
     */
    public static boolean checkUserMode(Integer goalStatus, SurveyCondition surveyCondition) {
        List<Integer> userModes = surveyCondition.getMode();
        if (userModes.isEmpty()) {
            return true;
        }

        if (goalStatus == null || !userModes.contains(goalStatus)) {
            return false;
        }
        return true;
    }
}
