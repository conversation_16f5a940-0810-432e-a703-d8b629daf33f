package com.mira.user.handler.schedule;

import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.ZoneDateUtil;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

/**
 * 首次通知时间
 *
 * <AUTHOR>
 */
@Getter
public class FirstReminderTime {
    private Long product02FirstReminderTime;
    private Long product03FirstReminderTime;
    private Long product09FirstReminderTime;
    private Long product12FirstReminderTime;
    private Long product14FirstReminderTime;
    private Long product16FirstReminderTime;

    public FirstReminderTime create(String timeZone, ProductReminderTimesDTO productReminderTimesDTO) {
        if (CollectionUtils.isNotEmpty(productReminderTimesDTO.getProduct02ReminderTimes())) {
            product02FirstReminderTime = ZoneDateUtil.timestamp(timeZone,
                    productReminderTimesDTO.getProduct02ReminderTimes().get(0),
                    DatePatternConst.DATE_TIME_PATTERN);
        }

        if (CollectionUtils.isNotEmpty(productReminderTimesDTO.getProduct03ReminderTimes())) {
            product03FirstReminderTime = ZoneDateUtil.timestamp(timeZone,
                    productReminderTimesDTO.getProduct03ReminderTimes().get(0),
                    DatePatternConst.DATE_TIME_PATTERN);
        }

        if (CollectionUtils.isNotEmpty(productReminderTimesDTO.getProduct09ReminderTimes())) {
            product09FirstReminderTime = ZoneDateUtil.timestamp(timeZone,
                    productReminderTimesDTO.getProduct09ReminderTimes().get(0),
                    DatePatternConst.DATE_TIME_PATTERN);
        }

        if (CollectionUtils.isNotEmpty(productReminderTimesDTO.getProduct12ReminderTimes())) {
            product12FirstReminderTime = ZoneDateUtil.timestamp(timeZone,
                    productReminderTimesDTO.getProduct12ReminderTimes().get(0),
                    DatePatternConst.DATE_TIME_PATTERN);
        }

        if (CollectionUtils.isNotEmpty(productReminderTimesDTO.getProduct14ReminderTimes())) {
            product14FirstReminderTime = ZoneDateUtil.timestamp(timeZone,
                    productReminderTimesDTO.getProduct14ReminderTimes().get(0),
                    DatePatternConst.DATE_TIME_PATTERN);
        }

        if (CollectionUtils.isNotEmpty(productReminderTimesDTO.getProduct16ReminderTimes())) {
            product16FirstReminderTime = ZoneDateUtil.timestamp(timeZone,
                    productReminderTimesDTO.getProduct16ReminderTimes().get(0),
                    DatePatternConst.DATE_TIME_PATTERN);
        }

        return this;
    }
}
