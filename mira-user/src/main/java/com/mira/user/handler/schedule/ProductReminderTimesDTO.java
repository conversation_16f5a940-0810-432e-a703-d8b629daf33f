package com.mira.user.handler.schedule;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("构建product推送对象")
public class ProductReminderTimesDTO {
    List<String> product03ReminderTimes = new ArrayList<>();
    List<String> product02ReminderTimes = new ArrayList<>();
    List<String> product09ReminderTimes = new ArrayList<>();
    List<String> product12ReminderTimes = new ArrayList<>();
    List<String> product14ReminderTimes = new ArrayList<>();
    List<String> product16ReminderTimes = new ArrayList<>();
}
