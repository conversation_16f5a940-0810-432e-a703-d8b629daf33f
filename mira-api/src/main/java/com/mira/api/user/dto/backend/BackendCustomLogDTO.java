package com.mira.api.user.dto.backend;

import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-05-15
 **/
@Data
public class BackendCustomLogDTO extends UserDiaryMoodsDTO {
    private Long userId;



    private List<UserSymptomDTO> symptoms = new ArrayList<>();


}
