package com.mira.api.user.dto.user.diary;

import com.mira.api.user.enums.daily.DailySymptomEnum;
import com.mira.api.user.enums.daily.DailySymptomLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 症状
 */
@Getter
@Setter
@ApiModel("Symptom")
public class UserSymptomDTO {
    /**
     * @see DailySymptomEnum
     */
    @ApiModelProperty(value = "Symptom")
    private String value;

    /**
     * @see DailySymptomLevelEnum
     */
    @ApiModelProperty(value = "SymptomLevel")
    private Integer level;
}
