package com.mira.api.user.consts;

/**
 * 不同阶段不同试剂的上下限阈值
 *
 * <AUTHOR>
 */
public class PhaseThresholdConst {
    /**
     * follicular phase
     */
    public final static Float LH_FOLLICULAR_PHASE_LOWER = 1f;
    public final static Float LH_FOLLICULAR_PHASE_UPPER = 6f;
    public final static Float E3G_FOLLICULAR_PHASE_LOWER = 80f;
    public final static Float E3G_FOLLICULAR_PHASE_UPPER = 120f;
    public final static Float PDG_FOLLICULAR_PHASE_LOWER = 1f;
    public final static Float PDG_FOLLICULAR_PHASE_UPPER = 3f;
    public final static Float FSH_FOLLICULAR_PHASE_LOWER = 3f;
    public final static Float FSH_FOLLICULAR_PHASE_UPPER = 10f;

    /**
     * ovulatory phase
     */
    public final static Float LH_OVULATORY_PHASE_LOWER = 7f;
    public final static Float LH_OVULATORY_PHASE_UPPER = 25f;
    public final static Float E3G_OVULATORY_PHASE_LOWER = 120f;
    public final static Float E3G_OVULATORY_PHASE_UPPER = 400f;
    public final static Float PDG_OVULATORY_PHASE_LOWER = 3f;
    public final static Float PDG_OVULATORY_PHASE_UPPER = 7.5f;
    public final static Float FSH_OVULATORY_PHASE_LOWER = 10f;
    public final static Float FSH_OVULATORY_PHASE_UPPER = 20f;

    /**
     * luteal phase
     */
    public final static Float LH_LUTEAL_PHASE_LOWER = 2f;
    public final static Float LH_LUTEAL_PHASE_UPPER = 5f;
    public final static Float E3G_LUTEAL_PHASE_LOWER = 100f;
    public final static Float E3G_LUTEAL_PHASE_UPPER = 350f;
    public final static Float PDG_LUTEAL_PHASE_LOWER = 5f;
    public final static Float PDG_LUTEAL_PHASE_UPPER = 20f;
    public final static Float FSH_LUTEAL_PHASE_LOWER = 2f;
    public final static Float FSH_LUTEAL_PHASE_UPPER = 5f;
}
