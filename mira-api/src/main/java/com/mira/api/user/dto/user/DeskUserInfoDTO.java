package com.mira.api.user.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel
public class DeskUserInfoDTO {
    @ApiModelProperty(value = "用户id")
    private Long id;
    @ApiModelProperty(value = "email")
    private String email;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "用户创建时的时区")
    private String timeZone;
    @ApiModelProperty(value = "用户名")
    private String nickname;
    @ApiModelProperty(value = "出生年份")
    private Integer birthYear;

    @ApiModelProperty(value = "出生月份")
    private Integer birthMonth;

    @ApiModelProperty(value = "出生日期")
    private Integer birthDay;

    @ApiModelProperty(value = "用户目标:0:Cycle tracking;1:TTA;2:TTC;3:Pregnancy tracking;4:OFT")
    private Integer goalStatus;
    @ApiModelProperty(value = "sn")
    private String sn;
    /**
     * 状态  0：禁用   1：正常	2:未激活
     */
    @NotBlank(message = "状态不能为空")
    private Integer status;
    /**
     * 来源：0:1.2数据同步；1:1.3新数据;2:Genea医生创建的用户;3:Genea医生;4:1.4新数据
     */
    private Integer source;

    /**
     * 0:1.2用户未同步，1:1.2用户已同步到1.3，2:已同步到1.4
     */
    private Integer transferFlag;

    @ApiModelProperty(value = "周期长度")
    private String avgLenCycle;
    @ApiModelProperty(value = "经期长度")
    private String avgLenPeriod;
    /**
     * 经期标识：0：正常；-1：I don't know
     */
    private Integer periodFlag;

    /**
     * 周期标识：0：正常；-1：I don't know;-2 varies often ；-3  I don't know + varies often
     */
    private Integer cycleFlag;
    private Integer periodFlagSwitch;

    @ApiModelProperty(value = "绑定firmware版本")
    private String bindVersion;

    @ApiModelProperty(value = "是否被定义为不规则周期;null未判断;0否;1是")
    private Integer definedIrregularCycle;

    @ApiModelProperty(value = "绑定标示：0没有绑定过；1绑定过;2:wait shipping")
    private Integer bindFlag;

    /**
     * 是否新用户：0新用户，1老用户
     */
    private Integer fresh;

    @ApiModelProperty(value = "partner邮箱")
    private String partnerEmail;

    @ApiModelProperty(value = "partner状态:0:未激活1:已完成激活但未注册2:已完成注册")
    private String partnerStatus;

    private Integer bbtBindStatus;
    private String bbtBindCreateTime;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;


}
