package com.mira.api.user.enums;

import lombok.Getter;

/**
 * user event
 *
 * <AUTHOR>
 */
@Getter
public enum UserEventEnum {
    USER_TEST_COMPLETED("user_test_completed", "用户完成一次测试"),
    USER_CYCLE_COMPLETED("user_cycle_completed", "用户完成一个周期"),
    USER_PHASE_CHANGE("user_phase_change", "用户进入一个阶段")
    ;

    private final String event;
    private final String desc;

    UserEventEnum(String event, String desc) {
        this.event = event;
        this.desc = desc;
    }
}
