package com.mira.api.user.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户绑定解绑仪器请求参数")
public class UserBindDTO {
    @ApiModelProperty("绑定设备")
    private String bindDevice;

    @ApiModelProperty("绑定时间")
    private String bindTime;

    @ApiModelProperty("设备版本")
    private String bindVersion;

    @ApiModelProperty("绑定类型，参考UserBindTypeEnum")
    private Integer type;
}
