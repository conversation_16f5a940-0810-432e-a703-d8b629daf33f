package com.mira.api.user.provider;

import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.user.consts.UserApiConst;
import com.mira.api.user.dto.user.*;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.UpdatePregnantDiaryDTO;
import com.mira.api.user.dto.user.diary.UserDiaryDTO;
import com.mira.api.user.dto.user.diary.UserDiaryIntegrationDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserDiaryIntegrationDTO;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 业务服务接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "mira-user", contextId = "user")
public interface IUserProvider {
    /**
     * 根据邮箱获取用户信息
     *
     * @param email 邮箱
     * @return AppUserDTO
     */
    @GetMapping(UserApiConst.USER_EMAIL)
    CommonResult<AppUserDTO> getUserByEmail(@RequestParam("email") String email);

    /**
     * 根据id获取用户信息
     *
     * @param userId 用户id
     * @return AppUserDTO
     */
    @GetMapping(UserApiConst.USER_ID)
    CommonResult<AppUserDTO> getUserById(@RequestParam("userId") Long userId);

    /**
     * 保存用户
     *
     * @param appUserSaveDTO 用户信息
     * @return AppUserDTO
     */
    @PostMapping(UserApiConst.USER_SAVE)
    CommonResult<AppUserDTO> saveUser(@RequestBody AppUserSaveDTO appUserSaveDTO);

    /**
     * 根据id获取partner信息
     *
     * @param partnerId partner id
     * @return AppPartnerDTO
     */
    @GetMapping(UserApiConst.PARTNER_ID)
    CommonResult<AppPartnerDTO> getPartnerById(@RequestParam("partnerId") Long partnerId);

    /**
     * 根据邮箱获取partner信息
     *
     * @param email partner id
     * @return AppPartnerDTO
     */
    @GetMapping(UserApiConst.PARTNER_EMAIL)
    CommonResult<AppPartnerDTO> getPartnerByEmail(@RequestParam("email") String email);

    /**
     * 根据id获取用户详情
     *
     * @param userId 用户id
     * @return AppUserInfoDTO
     */
    @GetMapping(UserApiConst.USER_INFO_ID)
    CommonResult<AppUserInfoDTO> getUserInfoById(@RequestParam("userId") Long userId);

    /**
     * 更新用户详情
     *
     * @param appUserInfoDTO 用户详情信息
     */
    @PostMapping(UserApiConst.USER_INFO_UPDATE)
    CommonResult<Void> updateUserInfo(@RequestBody AppUserInfoDTO appUserInfoDTO);

    /**
     * 根据id获取用户beta测试的goal
     *
     * @param userId 用户id
     * @return UserGoalEnum
     */
    @GetMapping(UserApiConst.USER_GOAL_TRIAL)
    CommonResult<WandTypeEnum> getUserProductTrial(@RequestParam("userId") Long userId);

    /**
     * 获取用户某一日的温度记录列表
     *
     * @param userId  用户编号
     * @param dateStr 日期，e.g. 2022-01-01
     * @return List<TemperatureDTO>
     */
    @GetMapping(UserApiConst.USER_DIARY_TEMPERATURE)
    CommonResult<List<TemperatureDTO>> getTemperatureList(@RequestParam("userId") Long userId,
                                                          @RequestParam("dateStr") String dateStr);

    /**
     * 获取用户的日记选项配置
     *
     * @param userId 用户编号
     * @return CustomLogConfigDTO
     */
    @GetMapping(UserApiConst.USER_CUSTOM_LOG_CONFIG)
    CommonResult<CustomLogConfigDTO> getCustomLogConfig(@RequestParam("userId") Long userId);

    /**
     * 绑定或者解绑仪器
     *
     * @param userId      用户编号
     * @param userBindDTO 绑定信息
     * @return String
     */
    @PostMapping(UserApiConst.USER_BIND_UNBIND_DEVICE)
    CommonResult<Integer> bindUnbindDevice(@RequestParam("userId") Long userId,
                                           @RequestBody UserBindDTO userBindDTO);

    /**
     * 修改绑定的firmware版本
     *
     * @param userId             用户编号
     * @param userBindVersionDTO 绑定信息
     * @return String
     */
    @PostMapping(UserApiConst.USER_BIND_VERSION)
    CommonResult<String> editBindVersion(@RequestParam("userId") Long userId,
                                         @RequestBody UserBindVersionDTO userBindVersionDTO);

    /**
     * 添加用户温度记录
     *
     * @param userId             用户编号
     * @param userTemperatureDTO 温度记录
     * @return String
     */
    @PostMapping(UserApiConst.USER_TEMPERATURE_ADD)
    CommonResult<TemperatureResultDTO> addTemperature(@RequestParam("userId") Long userId,
                                                      @RequestBody UserTemperatureDTO userTemperatureDTO);

    /**
     * 更新温度单位
     *
     * @param tempUnit 温度单位
     */
    @PostMapping(UserApiConst.EDIT_TEMPERATURE_UNIT)
    CommonResult<Void> editTemperatureUnit(@RequestParam("userId") Long userId,
                                           @RequestParam("tempUnit") String tempUnit);

    /**
     * 获取用户的 Schedule
     *
     * @param userId 用户编号
     * @return TestingScheduleDTO
     */
    @GetMapping(UserApiConst.USER_SCHEDULE_INFO)
    CommonResult<TestingScheduleDTO> getTestingSchedule(@RequestParam("userId") Long userId);

    /**
     * 获取用户的日记列表
     *
     * @param userId    用户编号
     * @param startDate 日期，e.g. 2022-01-01
     * @param endDate   日期，e.g. 2022-01-01
     * @return List<UserDiaryDTO>
     */
    @GetMapping(UserApiConst.USER_DIARY_LIST)
    CommonResult<List<UserDiaryDTO>> listUserDiary(@RequestParam("userId") Long userId,
                                                   @RequestParam("startDate") String startDate,
                                                   @RequestParam("endDate") String endDate);

    /**
     * 获取用户日记相关数据
     *
     * @param userId  用户编号
     * @param dateStr 日期，e.g. 2022-01-01
     * @return UserDiaryIntegrationDTO
     */
    @GetMapping(UserApiConst.USER_DIARY_INTEGRATION)
    CommonResult<UserDiaryIntegrationDTO> getUserDiaryIntegration(@RequestParam("userId") Long userId,
                                                                  @RequestParam("dateStr") String dateStr);

    /**
     * 获取用户日记相关数据列表
     *
     * @param userId 用户编号
     * @param dates  日期列表，e.g. 2022-01-01
     * @return Map<String, UserDiaryIntegrationDTO>
     */
    @PostMapping(UserApiConst.USER_DIARY_INTEGRATION_LIST)
    CommonResult<Map<String, UserDiaryIntegrationDTO>> listUserDiaryIntegration(@PathVariable("userId") Long userId,
                                                                                @RequestBody List<String> dates);

    /**
     * 获取用户日记相关数据列表
     *
     * @param userId 用户编号
     * @return Map<String, UserDiaryIntegrationDTO>
     */
    @PostMapping(UserApiConst.USER_ALL_DIARY_INTEGRATION_LIST)
    CommonResult<Map<String, ExportUserDiaryIntegrationDTO>> listUserAllDiaryIntegration(@PathVariable("userId") Long userId);

    /**
     * 获取用户经期数据
     *
     * @param userId 用户编号
     * @return UserPeriodDTO
     */
    @GetMapping(UserApiConst.USER_PERIOD)
    CommonResult<UserPeriodDTO> getUserPeriod(@RequestParam("userId") Long userId);

    /**
     * 处理需要测试试剂可切换的用户
     *
     * @param userId   用户编号
     * @param wandType 试剂类型
     * @return String
     */
    @GetMapping(UserApiConst.WAND_CHANGE)
    CommonResult<String> wandChange(@RequestParam("userId") Long userId,
                                    @RequestParam("wandType") String wandType);

    /**
     * hcg测试怀孕结果同步到diary页面
     *
     * @param updatePregnantDiaryDTO 参数
     * @return String
     */
    @PostMapping(UserApiConst.USER_DIARY_UPDATE_PREGNANT)
    CommonResult<String> updatePregnantDiary(@RequestBody UpdatePregnantDiaryDTO updatePregnantDiaryDTO);

    /**
     * 构建tips
     *
     * @param userId user id
     * @return SysTipsDTO
     */
    @PostMapping(UserApiConst.USER_TIPS)
    CommonResult<SysTipsDTO> buildTipsResult(@RequestParam("userId") Long userId);

    /**
     * 构建编辑经期请求数据
     *
     * @param userId                   用户id
     * @param algorithmRequestTypeEnum 算法请求类型
     * @return AlgorithmEditPeriodDTO
     */
    @GetMapping(UserApiConst.USER_PERIOD_EDIT_BUILD)
    CommonResult<AlgorithmEditPeriodDTO> buildAlgorithmEditPeriodDTO(@RequestParam("userId") Long userId,
                                                                     @RequestParam("algorithmRequestTypeEnum") AlgorithmRequestTypeEnum algorithmRequestTypeEnum);

    /**
     * 根据用户id集合获取用户邮箱集合
     *
     * @param userIds id list
     * @return Map<Long, String>
     */
    @PostMapping(UserApiConst.LIST_EMAIL_USERIDS)
    CommonResult<Map<Long, String>> listEmailByIds(@RequestBody Set<Long> userIds);

    /**
     * 根据用户id集合获取用户邮箱集合
     *
     * @param userIds id list
     * @return List<UserPeriodDTO>
     */
    @PostMapping(UserApiConst.LIST_PERIOD_USERIDS)
    CommonResult<List<UserPeriodDTO>> listPeriodByUserIds(@RequestBody Set<Long> userIds);

    /**
     * 查询用户的10条仪器绑定历史记录
     *
     * @param userId id
     * @return List<UserBindLogDTO>
     */
    @PostMapping(UserApiConst.LIST_10_BIND_LOG)
    CommonResult<List<UserBindLogDTO>> list10BindLog(@RequestParam("userId") Long userId);

    /**
     * desk查询用户
     *
     * @param keyword 关键字
     * @return List<DeskSearchUserDTO>
     */
    @GetMapping(UserApiConst.SEARCH_DESK_USER)
    CommonResult<List<DeskSearchUserDTO>> searchDeskUser(@RequestParam("keyword") String keyword);

    /**
     * desk查询用户info信息
     *
     * @param userId 用户id
     * @return DeskUserInfoDTO
     */
    @GetMapping(UserApiConst.DESK_USER_INFO)
    CommonResult<DeskUserInfoDTO> getDeskUserInfoDTO(@RequestParam("userId") Long userId);

    @PostMapping(UserApiConst.SYSTEM_EDIT_PERIOD)
    CommonResult<Void> systemEditPeriod(@RequestParam("userId") Long userId);

    /**
     * 用户paywall记录
     *
     * @param userPaywallDTO 参数
     * @return String
     */
    @PostMapping(UserApiConst.USER_PAYWALL)
    CommonResult<String> payWall(@RequestBody UserPaywallDTO userPaywallDTO);

    /**
     * 用户等待发货
     *
     * @param userId 用户id
     * @return Void
     */
    @PostMapping(UserApiConst.WAIT_SHIPPING)
    CommonResult<Void> waitShipping(@RequestParam("userId") Long userId);
}
