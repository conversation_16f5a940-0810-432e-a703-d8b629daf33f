package com.mira.api.user.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("测试Schedule")
public class TestingScheduleDTO {
    @ApiModelProperty("是否推荐plus的测试日")
    private Integer plus;

    @ApiModelProperty("是否推荐confirm的测试日")
    private Integer confirm;

    @ApiModelProperty("是否推荐max的测试日")
    private Integer max;

    @ApiModelProperty("是否推荐ovum的测试日")
    private Integer ovum;
}
