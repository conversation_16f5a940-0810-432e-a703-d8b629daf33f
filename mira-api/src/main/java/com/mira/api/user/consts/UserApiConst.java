package com.mira.api.user.consts;

/**
 * api url 常量
 *
 * <AUTHOR>
 */
public class UserApiConst {
    //------------------用户服务------------------
    public final static String USER_EMAIL = "/user/email";
    public final static String USER_ID = "/user/id";
    public final static String USER_SAVE = "/user/save";
    public final static String USER_INFO_ID = "/user/info/id";
    public final static String USER_INFO_UPDATE = "/user/info/update";
    public final static String USER_GOAL_TRIAL = "/user/goal/trial";
    public final static String USER_PERIOD = "/user/period";
    public final static String USER_DIARY_TEMPERATURE = "/user/diary/temperature";
    public final static String USER_CUSTOM_LOG_CONFIG = "/user/diary/logConfig";
    public final static String USER_DIARY_UPDATE_PREGNANT = "/user/diary/pregnant";

    public final static String USER_TIPS = "/user/tips";
    public final static String USER_PERIOD_EDIT_BUILD = "/user/period/edit/build";

    public final static String USER_BIND_UNBIND_DEVICE = "/user/device/bind";
    public final static String USER_BIND_VERSION = "/user/device/bind/version";
    public final static String USER_TEMPERATURE_ADD = "/user/temperature/add";

    public final static String EDIT_TEMPERATURE_UNIT = "/user/temperature/unit/edit";
    public final static String USER_SCHEDULE_INFO = "/user/schedule/info";

    public final static String USER_DIARY_LIST = "/user/diary/list";
    public final static String USER_DIARY_INTEGRATION = "/user/diary/integration";
    public final static String USER_DIARY_INTEGRATION_LIST = "/user/diary/integration/list/{userId}";
    public final static String USER_ALL_DIARY_INTEGRATION_LIST = "/user/all/diary/integration/list/{userId}";
    public final static String PARTNER_ID = "/partner/id";
    public final static String PARTNER_EMAIL = "/partner/email";

    public final static String WAND_CHANGE = "/wand/change";

    public final static String LIST_EMAIL_USERIDS = "/user/list/email/userIds";
    public final static String LIST_PERIOD_USERIDS = "/user/list/period/userIds";
    public final static String BLACK_SN_PAGE = "/user/black-sn/page";
    public final static String CREATE_BLACK_SN = "/user/black-sn/create";
    public final static String ENABLE_BLACK_SN = "/user/black-sn/enable";
    public final static String DELETE_BLACK_SN = "/user/black-sn/delete";
    public final static String LIST_10_BIND_LOG = "/user/list/10bind-log";
    public final static String SEARCH_DESK_USER = "/user/search/desk-user";
    public final static String DESK_USER_INFO = "/user/desk-user-info";
    public final static String USER_PAYWALL = "/user/paywall";

    //------------------Fertility服务------------------
    public final static String QUIZ_USER_PROFILE = "/fertility/quiz/user-profile";

    //------------------IUserCustomLogProvider服务------------------
    public final static String CUSTOM_LOG_PAGE = "/user-custom-log/page";
    public final static String MOODS_SYMPTOMS_COUNT = "/user-custom-log/moods-symptoms-count";
    public final static String WAIT_SHIPPING = "/user/wait-shipping";

    //------------------Mira Desk-------------------
    public static final String SYSTEM_EDIT_PERIOD = "/period/system-edit-period";
}
