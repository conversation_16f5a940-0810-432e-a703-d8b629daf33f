package com.mira.api.user.enums;

import lombok.Getter;

/**
 * articles phase type enum
 *
 * <AUTHOR>
 */
@Getter
public enum PhaseEnum {
    FOLLICULAR_PHASE(1, "Follicular phase", "卵泡期"),
    FERTILE_WINDOW(2, "Fertile window", "排卵期"),
    LUTEAL_PHASE(3, "Luteal phase", "黄体期")
    ;

    private final int type;
    private final String name;
    private final String desc;

    PhaseEnum(int type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }
}
