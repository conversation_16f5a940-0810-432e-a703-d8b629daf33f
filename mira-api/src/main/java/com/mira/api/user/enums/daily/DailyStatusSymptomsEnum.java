package com.mira.api.user.enums.daily;

import lombok.Getter;

/**
 * 症状记录
 * key:symptoms
 */
@Getter
public enum DailyStatusSymptomsEnum {
    /**
     * 头部不适
     */
    H("h", "Headache"),
    /**
     * 腹部不适
     */
    C("c", "Cramps"),
    /**
     * 腰酸背痛
     */
    B("b", "Backache"),
    /**
     * 乳房胀痛
     */
    T("t", "Tender breasts"),
    /**
     * 白带异常
     */
    P("p", "Pelvic pain"),
    /**
     * 感冒发烧
     */
    S("s", "Sick"),
    /**
     * 点滴出血
     */
    O("o", "Spotting"),
    /**
     * 恶心想吐
     */
    N("n", "Nausea");

    private final String value;
    private final String description;

    DailyStatusSymptomsEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static DailyStatusSymptomsEnum getEnumByValue(String value) {
        for (DailyStatusSymptomsEnum symptomsEnum : values()) {
            if (symptomsEnum.getValue().equals(value)) {
                return symptomsEnum;
            }
        }
        return null;
    }
}
