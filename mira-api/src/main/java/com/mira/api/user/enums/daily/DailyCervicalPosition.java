package com.mira.api.user.enums.daily;

import lombok.Getter;

/**
 * @program: mira_server
 * @description: 子宫位置（Height）
 * @author: xizhao.dai
 * @create: 2021-05-21 13:43
 **/
@Getter
public enum DailyCervicalPosition {
    /**
     * Low
     */
    L("l", "Low"),
    /**
     * Middle
     */
    M("m", "Middle"),
    /**
     * High
     */
    H("h", "High");

    private final String value;
    private final String description;

    DailyCervicalPosition(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
