package com.mira.api.thirdparty.dto.amplitude;

import com.mira.api.thirdparty.enums.AmplitudeEventTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;

@Getter
@Setter
@ApiModel("Amplitude记录")
public class AmplitudeDTO {
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("事件类型")
    private AmplitudeEventTypeEnum amplitudeEventTypeEnum;

    @ApiModelProperty("用户属性")
    private HashMap<String, String> userProps;

    public AmplitudeDTO() {}

    public AmplitudeDTO(Long userId, AmplitudeEventTypeEnum amplitudeEventTypeEnum, HashMap<String, String> userProps) {
        this.userId = userId;
        this.amplitudeEventTypeEnum = amplitudeEventTypeEnum;
        this.userProps = userProps;
    }
}
