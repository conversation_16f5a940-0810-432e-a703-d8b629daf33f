package com.mira.api.thirdparty.dto.klaviyo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Klaviyo Metric
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class KlaviyoMetricResultDTO {
    private List<Data> data;

    @Getter
    @Setter
    public static class Data {
        private String type = "event";
        private String id;
        private Attributes attributes;
        private Relationships relationships;
    }

    @Getter
    @Setter
    public static class Attributes {
        private Long timestamp;
        private EventProperties event_properties;
    }

    @Getter
    @Setter
    public static class EventProperties {
        private String Subject;
    }

    @Getter
    @Setter
    public static class Relationships {
        private Metric metric;
    }

    @Getter
    @Setter
    public static class Metric {
        private MetricData data;
    }

    @Getter
    @Setter
    public static class MetricData {
        private String type = "metric";
        private String id;
    }
}