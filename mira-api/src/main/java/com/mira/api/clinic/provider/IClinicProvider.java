package com.mira.api.clinic.provider;

import com.mira.api.clinic.consts.ClinicApiConst;
import com.mira.api.clinic.dto.*;
import com.mira.api.clinic.dto.queue.PatientNewHormoneDTO;
import com.mira.api.clinic.dto.queue.PeriodChangeDTO;
import com.mira.api.clinic.dto.queue.PregnantFlagDTO;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Cinic服务接口
 *
 * <AUTHOR>
 */
@FeignClient("mira-clinic")
public interface IClinicProvider {
    /**
     * 获取诊所信息
     *
     * @param email 邮箱
     * @return UserClinicDTO
     * @deprecated since 7.6.27 replaced by listClinicInfos
     */
    @GetMapping(ClinicApiConst.CLINIC_INFO)
    CommonResult<UserClinicDTO> getClinicInfo(@RequestParam("email") String email);

    @GetMapping(ClinicApiConst.LIST_CLINIC_INFO)
    CommonResult<List<UserClinicDTO>> listClinicInfos(@RequestParam("email") String email);

    @GetMapping(ClinicApiConst.CLINIC_INFO_BY_CODE)
    CommonResult<ClinicDTO> getClinicByCode(@RequestParam("tenantCode") String tenantCode);

    /**
     * 获取用户作为病人的信息
     *
     * @param userId 用户id
     * @return UserPatientDTO
     * @deprecated since 7.6.27 replaced by listUserPatientDTOs
     */
    @GetMapping(ClinicApiConst.USER_PATIENT)
    CommonResult<UserPatientDTO> getUserPatientDTO(@RequestParam("userId") Long userId);

    @GetMapping(ClinicApiConst.LIST_USER_PATIENT)
    CommonResult<List<UserPatientDTO>> listUserPatientDTOs(@RequestParam("userId") Long userId);

    /**
     * 绑定诊所
     *
     * @param userId 用户id
     * @return String
     */
    @Deprecated(since = "7.6.27")
    @PostMapping(ClinicApiConst.BIND_USER)
    CommonResult<String> bind(@RequestParam("userId") Long userId, @RequestParam("email") String email);

    /**
     * 解除与诊所的绑定
     *
     * @param userId 用户id
     * @return String
     */
    @Deprecated(since = "7.6.27")
    @PostMapping(ClinicApiConst.UNBIND_USER)
    CommonResult<String> unbind(@RequestParam("userId") Long userId);


    /**
     * 绑定诊所
     *
     * @param userId 用户id
     * @return String
     */
    @PostMapping(ClinicApiConst.BIND_USER_V2)
    CommonResult<String> bindV2(@RequestParam("userId") Long userId, @RequestParam("email") String email, @RequestParam("tenantCode") String tenantCode);

    /**
     * 解除与诊所的绑定
     *
     * @param userId 用户id
     * @return String
     */
    @PostMapping(ClinicApiConst.UNBIND_USER_V2)
    CommonResult<String> unbindV2(@RequestParam("userId") Long userId, @RequestParam("tenantCode") String tenantCode);

    /**
     * 用户拒绝诊所的邀请
     *
     * @param userId
     * @param tenantCode
     * @return
     */
    @PostMapping(ClinicApiConst.REJECT_INVITE)
    CommonResult<String> rejectInvite(@RequestParam("userId") Long userId, @RequestParam("tenantCode") String tenantCode);

    /**
     * 处理经期变化
     *
     * @param periodChangeDTO 经期变动参数
     * @return String
     */
    @PostMapping(ClinicApiConst.CHANGE_PERIOD)
    CommonResult<String> changePeriod(@RequestBody PeriodChangeDTO periodChangeDTO);

    /**
     * 处理孕期变化
     *
     * @param pregnantFlagDTO 怀孕标记参数
     * @return String
     */
    @PostMapping(ClinicApiConst.HANDLE_PREGNANT_FLAG)
    CommonResult<String> handlePregnantFlag(@RequestBody PregnantFlagDTO pregnantFlagDTO);

    /**
     * 处理新数据上传后逻辑
     *
     * @param patientNewHormoneDTO 新测试数据参数
     * @return String
     */
    @PostMapping(ClinicApiConst.HANDLE_NEW_HORMONE)
    CommonResult<String> handleNewHormone(@RequestBody PatientNewHormoneDTO patientNewHormoneDTO);

    /**
     * 更新病人邮箱
     *
     * @param userId 用户id
     * @param email  邮箱
     * @return String
     */
    @PostMapping(ClinicApiConst.PATIENT_UPDATE_EMAIL)
    CommonResult<String> updatePatientEmail(@RequestParam("userId") Long userId, @RequestParam("email") String email);


    /**
     * 初始创建诊所
     *
     * @param initCreateClinicDTO
     * @return
     */
    @PostMapping(ClinicApiConst.INIT_CREATE_CLINIC)
    CommonResult<Void> initCreateClinic(@RequestBody InitCreateClinicDTO initCreateClinicDTO);

    /**
     * clinic分页查找对象（给后台管理系统使用）
     *
     * @param clinicPageRequestDTO
     * @return
     */
    @PostMapping(ClinicApiConst.CLINIC_PAGE)
    CommonResult<ClinicPageResponseDTO> clinicPage(@RequestBody ClinicPageRequestDTO clinicPageRequestDTO);

    /**
     * all clinic
     */
    @PostMapping(ClinicApiConst.ALL_CLINIC)
    CommonResult<List<ClinicListDTO>> allClinic();

    /**
     * 修改clinic基本信息
     *
     * @param editClinicDTO
     * @return
     */
    @PostMapping(ClinicApiConst.EDIT_CLINIC_INFO)
    CommonResult<Void> editClinicInfo(@RequestBody EditClinicDTO editClinicDTO);

    /**
     * 删除clinic基本信息
     *
     * @param id
     * @return
     */
    @PostMapping(ClinicApiConst.DELETE_CLINIC_INFO)
    CommonResult<Void> deleteClinicInfo(@RequestBody Long id);

    @PostMapping(ClinicApiConst.APPLY_PATIENT)
    CommonResult<String> applyPatient(@RequestBody ApplyPatientDTO applyPatientDTO);

    @PostMapping(ClinicApiConst.RESET_CLINIC_PW)
    CommonResult<String> resetClinicPassword(@RequestParam("email") String email);
}
