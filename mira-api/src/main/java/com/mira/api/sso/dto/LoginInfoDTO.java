package com.mira.api.sso.dto;

import com.mira.api.user.dto.user.DeviceInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 登录信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("登录信息DTO")
public class LoginInfoDTO extends DeviceInfoDTO {
    @ApiModelProperty("邮箱")
    @Pattern(regexp = "[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?",
            message = "The email address is invalid.")
    @Size(max = 99, message = "Please enter a valid email address.")
    @NotBlank(message = "Don't leave us hanging! Email and password should not be empty.")
    private String email;

    @ApiModelProperty("密码")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-zA-Z])[0-9A-Za-z~!@#$%^&*():,;'=?./-]{8,30}$",
            message = "Password must be 8-30 characters with both numbers and letters.")
    @NotBlank(message = "Don't leave us hanging! Email and password should not be empty.")
    private String password;
}
