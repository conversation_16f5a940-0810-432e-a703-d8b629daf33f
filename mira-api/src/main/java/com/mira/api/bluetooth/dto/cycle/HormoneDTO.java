package com.mira.api.bluetooth.dto.cycle;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mira.api.bluetooth.enums.DeviceErrorCodeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("激素数据")
public class HormoneDTO {
    @ApiModelProperty("hormone 测试结果数据时间戳")
    private String test_time;

    @ApiModelProperty("测试结果集")
    private TestResult test_results;

    @Getter
    @Setter
    @ApiModel("测试结果")
    public static class TestResult {
        /**
         * @see WandTypeEnum
         */
        @ApiModelProperty("试剂类型")
        private Integer wand_type;

        @ApiModelProperty("测量值1")
        private Float value1;

        @ApiModelProperty("测量值2")
        private Float value2;

        @ApiModelProperty("测量值3")
        private Float value3;

        /**
         * @see DeviceErrorCodeEnum
         */
        @ApiModelProperty("Warning/Error值")
        @JsonProperty("Ecode")
        private String Ecode;
    }

    @ApiModelProperty("试纸批次")
    private String wandBatch3;

    @ApiModelProperty("测试数据id")
    private Long id;

    @ApiModelProperty("是否参与计算: 0代表测量数据无效；1代表测量数据有效")
    private Integer flag = 1;

    @ApiModelProperty("额外信息（供算法使用）")
    private ExtraInfo extra_info;

    @Getter
    @Setter
    @ApiModel("额外信息（供算法使用）")
    public static class ExtraInfo {
        @ApiModelProperty("hcg该批次参考高值，当flag=3时，为必要字段")
        private Float hcg_limitupper2;
    }
}
