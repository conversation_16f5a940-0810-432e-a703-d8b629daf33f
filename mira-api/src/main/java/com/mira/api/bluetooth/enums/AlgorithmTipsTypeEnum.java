package com.mira.api.bluetooth.enums;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * tips 枚举
 */
@Getter
public enum AlgorithmTipsTypeEnum {
    INT("int", new HashMap<>()),
    FLOAT("float", new HashMap<>()),
    STRING("String", new HashMap<>()),

    /**
     * 试剂类型映射
     */
    WAND_TYPE_MAP("WandTypeMap", ImmutableMap.of(
            "1", "LH",
            "3", "E3G",
            "9", "PdG",
            "2", "hCG",
            "16", "FSH"
    )),

    /**
     * 试剂购买链接
     */
    WAND_LINK_MAP("WandLinkMap", ImmutableMap.of(
            "1", "[Mira Fertility Plus Wand(shop03)]",
            "3", "[Mira Fertility Plus Wand(shop03)]",
            "9", "[<PERSON> Confirm Wand(shop09)]",
            "12", "[Mira Fertility MAX Wand(shop12)]",
            "16", "[Mira Ovum Wand(shop16)]"
    )),

    /**
     * 试剂介绍链接
     */
    WAND_INFO_LINK_MAP("WandInfoLinkMap", ImmutableMap.of(
            "0", "[chart info(/chartExplanation.html)]"
    )),

    /**
     * 数值变化趋势
     */
    CHANGE_MAP("ChangeMap", ImmutableMap.of(
            "0", "the same as",
            "1", "lower than",
            "2", "higher than",
            "3", "much lower than",
            "4", "much higher than",
            "-1", "anomalous"
    )),

    /**
     * 数值比较程度高中低
     */
    LEVEL_MAP("LevelMap", ImmutableMap.of(
            "0", "low",
            "1", "medium",
            "2", "high",
            "-1", "still collecting information"
    )),

    /**
     * 测试数据返回，数据id+需要的数据型，例如："1292458:LH","1292458:E3G","1292458:PDG"
     */
    DATA_ID("DataId", new HashMap<>());

    private final String code;
    private final Map<String, String> map;

    AlgorithmTipsTypeEnum(String code, Map<String, String> map) {
        this.code = code;
        this.map = map;
    }

    public static AlgorithmTipsTypeEnum get(String code) {
        for (AlgorithmTipsTypeEnum tipsDataTypeEnum : values()) {
            if (tipsDataTypeEnum.getCode().equals(code)) {
                return tipsDataTypeEnum;
            }
        }
        return null;
    }
}
