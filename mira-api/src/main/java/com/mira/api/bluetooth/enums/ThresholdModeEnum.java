package com.mira.api.bluetooth.enums;

import lombok.Getter;

/**
 * 阈值模式枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ThresholdModeEnum {
    NORMAL(0, "默认正常LH阈值"),
    LOW_LH_NEED_USER_CONFIRM(1, "需要用户确认将LH阈值变低的状态"),
    LOW_LH(2, "LH阈值变低的状态")
    ;

    private final int code;
    private final String desc;

    ThresholdModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
