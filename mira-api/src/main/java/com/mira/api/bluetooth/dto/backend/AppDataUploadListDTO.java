package com.mira.api.bluetooth.dto.backend;


import com.mira.api.bluetooth.dto.backend.AppDataUploadDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * 蓝牙上传数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AppDataUploadListDTO extends AppDataUploadDTO {
    /**
     * 半基线宽
     */
    private Double uHalfBaseWidth1;
    private Double uHalfBaseWidth2;
    private Double uHalfBaseWidth3;

    /**
     * 平均基线
     */
    private Double avgBaseLine;
    /**
     * t1c斜率
     */
    private Double t1cBaseLinexielv;
    /**
     * t2c斜率
     */
    private Double t2cBaseLinexielv;
    /**
     * 跑版校验结果
     */
    private Boolean checkDataResult;

    /**
     * sn批号
     */
    private String snBatchId;


}
