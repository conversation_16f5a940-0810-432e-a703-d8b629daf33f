package com.mira.api.bluetooth.dto.backend;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-29
 **/
@Data
public class DataManualAuditDTO {
    private Long id;
    /**
     * t1浓度值
     */
    private BigDecimal t1ConValue;
    /**
     * t2浓度值
     */
    private BigDecimal t2ConValue;
    /**
     * t3浓度值
     */
    private BigDecimal t3ConValue;

    /**
     * 0:等于数值；1:大于数值；2:小于数值(默认0)
     */
    private Integer data1Compare;
    private Integer data2Compare;
    private Integer data3Compare;
}
