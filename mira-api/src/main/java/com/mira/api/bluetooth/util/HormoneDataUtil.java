package com.mira.api.bluetooth.util;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.core.util.LocalDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 算法结果试剂信息工具类
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2025-01-14
 **/
@Slf4j
public class HormoneDataUtil {
    public static Set<String> buildTestDates(List<HormoneDTO> hormoneDatas) {
        List<String> testTimes = hormoneDatas.stream()
                                             .filter(hormoneDTO -> StringUtils.isBlank(hormoneDTO.getTest_results().getEcode())
                                                     || (hormoneDTO.getTest_results().getEcode().startsWith("B")))
                                             .map(HormoneDTO::getTest_time)
                                             .collect(Collectors.toList());
        Set<String> testDates = new LinkedHashSet<>();
        for (String testTime : testTimes) {
            String testDate = LocalDateUtil.dateTime2Date(testTime);
            testDates.add(testDate);
        }
        return testDates;
    }
}
