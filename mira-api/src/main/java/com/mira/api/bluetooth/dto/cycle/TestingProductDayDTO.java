package com.mira.api.bluetooth.dto.cycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("测试日")
public class TestingProductDayDTO {
    @ApiModelProperty("product03测试日")
    private List<String> product03 = new ArrayList<>();

    @ApiModelProperty("product02测试日")
    private List<String> product02 = new ArrayList<>();

    @ApiModelProperty("product09测试日")
    private List<String> product09 = new ArrayList<>();

    @ApiModelProperty("product12测试日")
    private List<String> product12 = new ArrayList<>();

    @ApiModelProperty("product14测试日")
    private List<String> product14 = new ArrayList<>();

    @ApiModelProperty("product16测试日")
    private List<String> product16 = new ArrayList<>();
}
