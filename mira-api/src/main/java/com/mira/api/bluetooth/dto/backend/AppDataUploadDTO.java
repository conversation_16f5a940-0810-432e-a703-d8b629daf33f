package com.mira.api.bluetooth.dto.backend;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 蓝牙上传数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AppDataUploadDTO {
    /**
     * 以下为data_upload_entity表字段
     */
    private Long id;

    /**
     * 创建者 默认0代表系统创建
     */
    protected Long creator;

    /**
     * 修改者 默认0代表系统修改
     */
    protected Long modifier;

    /**
     * 创建时间
     */
    protected Long createTime;

    /**
     * 创建时间
     */
    protected String createTimeStr;

    /**
     * 修改时间
     */
    protected Long modifyTime;

    /**
     * 修改时间
     */
    protected String modifyTimeStr;

    /**
     * 时区
     */
    protected String timeZone;


    @ApiModelProperty(value = "状态 0 正常状态 1 删除状态")
    private Integer deleted;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 序列号
     */
    private String sn;

    /**
     * 固件版本
     */
    private String firmwareVersion;

    /**
     * 是否同步app(预留)
     */
    private Integer updateServerFlag;

    /**
     * 仪器第几次检测统计
     */
    private Integer analyzerNumber;

    /**
     * 结果版本
     */
    private String resultVersionFormat;

    /**
     * 首次插入时间戳
     */
    private String firstTimeStamp;

    /**
     * 试剂完成时间
     */
    private String completeTime;

    /**
     * 试剂完成时间戳
     */
    private Long completeTimestamp;

    /**
     * 试剂类型
     */
    private String testWandType;

    /**
     * 试剂批次
     */
    private String testWandBatch;

    /**
     * 批次编号
     */
    private String batchInNumber;

    /**
     * t1线峰高位置
     */
    private BigDecimal t1PeakHeightPosition;

    /**
     * t1线绝对峰高
     */
    private BigDecimal t1AbsolutePeakHeight;

    /**
     * t1线峰面积
     */
    private BigDecimal t1PeakArea;

    /**
     * t1浓度值
     */
    private BigDecimal t1ConValue;

    /**
     * t1警告代码
     */
    private String t1WarningCode;

    /**
     * t1错误代码
     */
    private String t1ErrorCode;

    /**
     * t1定性判断值(只针对LH： None/Low/Medium/High 依次为 0/1/2/3)
     */
    private String t1Level;

    /**
     * t1测量物类型
     */
    private String t1WandType;

    /**
     * t2线峰高位置
     */
    private BigDecimal t2PeakHeightPosition;

    /**
     * t2线绝对峰高
     */
    private BigDecimal t2AbsolutePeakHeight;

    /**
     * t2线峰面积
     */
    private BigDecimal t2PeakArea;

    /**
     * t2浓度值
     */
    private BigDecimal t2ConValue;

    /**
     * t2警告代码
     */
    private String t2WarningCode;

    /**
     * t2错误代码
     */
    private String t2ErrorCode;

    /**
     * t2定性判断值(只针对LH： None/Low/Medium/High 依次为 0/1/2/3)
     */
    private String t2Level;

    /**
     * t2测量物类型(试剂类型为HCG+LH时： 判断是否显示HCGLH_Flag的标志位 (0不显示LH ，1显示LH)',
     */
    private String t2WandType;

    /**
     * t3线峰高位置
     */
    private BigDecimal t3PeakHeightPosition;

    /**
     * t3线绝对峰高
     */
    private BigDecimal t3AbsolutePeakHeight;

    /**
     * t3线峰面积
     */
    private BigDecimal t3PeakArea;

    /**
     * t3浓度值
     */
    private BigDecimal t3ConValue;

    /**
     * t3警告代码
     */
    private String t3WarningCode;

    /**
     * t3错误代码
     */
    private String t3ErrorCode;

    /**
     * t3定性判断值(只针对LH： None/Low/Medium/High 依次为 0/1/2/3)
     */
    private String t3Level;

    /**
     * t1相对峰高
     */
    private BigDecimal t1RelativelyPeakHeight;

    /**
     * t2相对峰高
     */
    private BigDecimal t2RelativelyPeakHeight;

    /**
     * t3相对峰高
     */
    private BigDecimal t3RelativelyPeakHeight;

    /**
     * t4相对峰高
     */
    private BigDecimal t4RelativelyPeakHeight;

    /**
     * c1相对峰高
     */
    private BigDecimal c1RelativelyPeakHeight;

    /**
     * c1线峰高位置
     */
    private BigDecimal c1PeakHeightPosition;

    /**
     * c1线绝对峰高
     */
    private BigDecimal c1AbsolutePeakHeight;

    /**
     * c1线峰面积
     */
    private BigDecimal c1PeakArea;

    /**
     * 试剂棒结果C1_Flag是否有效（1 显示结果，0不显示结果）
     */
    private Integer c1WandValid;

    /**
     * c2线峰高位置
     */
    private BigDecimal c2PeakHeightPosition;

    /**
     * c2线绝对峰高
     */
    private BigDecimal c2AbsolutePeakHeight;

    /**
     * c2线峰面积
     */
    private BigDecimal c2PeakArea;

    /**
     * 试剂棒结果C2_Flag是否有效（1 显示结果，0不显示结果）
     */
    private Integer c2WandValid;

    /**
     * 白域均值
     */
    private BigDecimal whiteFieldAvg;

    /**
     * PD板上温度
     */
    private BigDecimal PDTemperature;

    /**
     * back_groud_befor_c
     */
    private String backGroudBeforC;

    /**
     * back_groud_end_t
     */
    private String backGroudEndT;

    /**
     * MCU温度
     */
    private Integer MCUTemperature;

    /**
     * 电池电压
     */
    private Double batteryPercentage;

    /**
     * 错误代码
     */
    private String error;

    /**
     * 警告代码
     */
    private String warning;

    /**
     * 原始数据
     */
    private String rawResultData;

    /**
     * 是否是有效数据
     */
    private Integer avlidFlage;

    /**
     * 数据来源，参考DataSourceEnum
     */
    private Integer source;

    /**
     * 数据类型:0:auto;1manual(default0)
     */
    private Integer autoFlag;

    /**
     * 系统操作备注
     */
    private String sysNote;
}
