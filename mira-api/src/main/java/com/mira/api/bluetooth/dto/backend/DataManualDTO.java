package com.mira.api.bluetooth.dto.backend;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-21
 **/
@Data
public class DataManualDTO {
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * email
     */
    private String email;

    /**
     * 试剂完成时间
     */
    private String completeTime;

    private Long completeTimestamp;

    /**
     * 试剂类型
     */
    private String testWandType;
    /**
     * t1浓度值
     */
    private BigDecimal t1ConValue;
    /**
     * t2浓度值
     */
    private BigDecimal t2ConValue;
    /**
     * t3浓度值
     */
    private BigDecimal t3ConValue;

    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;
    /**
     * 状态: 0:未处理;1:正在处理;2:添加成功;3:已同步;4:添加失败(默认0);5:忽略请求
     */
    @ApiModelProperty(value = "状态: 0:未处理;1:正在处理;2:Your data was added;" +
            "3:Your data was synchronized;" +
            "4:Your data was not added to your profile" +
            "5:Your data was ignored")
    private Integer status;
    /**
     * 通知状态: 0:未通知(默认);29:Your data was added;30:Your data was synchronized;31:Your data was not added to your profile
     */
    @ApiModelProperty(value = "通知状态: 0:未通知(默认);29:Your data was added;30:Your data was synchronized;31:Your data was not added to your profile")
    private Long notificationStatus;

    /**
     * 0:等于数值；1:大于数值；2:小于数值(默认0)
     */
    private Integer data1Compare;
    private Integer data2Compare;
    private Integer data3Compare;


    private Long modifier;

    private Long createTime;

    private Long modifyTime;

    private String createTimeStr;

    private String modifyTimeStr;

    /**
     * 时区
     */
    private String timeZone;
}
