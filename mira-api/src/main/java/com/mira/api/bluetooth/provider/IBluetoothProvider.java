package com.mira.api.bluetooth.provider;

import com.mira.api.bluetooth.consts.BluetoothApiConst;
import com.mira.api.bluetooth.dto.bbt.BBTInfoDTO;
import com.mira.api.bluetooth.dto.wand.*;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 蓝牙服务接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "mira-bluetooth", contextId = "bluetooth")
public interface IBluetoothProvider {
    /**
     * 获取指定测试历史记录
     *
     * @param id        测试记录id
     * @param bioMarker 试剂类型
     * @return WandTestBiomarkerDTO
     */
    @GetMapping(BluetoothApiConst.WAND_TEST_DATA_BIOMARKER)
    CommonResult<WandTestBiomarkerDTO> getWandTestBiomarkerData(@RequestParam("id") Long id,
                                                                @RequestParam("bioMarker") String bioMarker,
                                                                @RequestParam(value = "trialFlag", required = false) Integer trialFlag);

    /**
     * 获取指定日期试剂的测试历史记录
     *
     * @param wandTestDataDTO 每日测试数据
     * @return List<WandTestBiomarkerDTO>
     * @deprecated
     */
    @PostMapping(BluetoothApiConst.DAY_WAND_TEST_DATA)
    CommonResult<List<WandTestBiomarkerDTO>> getDayWandBiomarkerData(@RequestBody WandTestDataDTO wandTestDataDTO);

    /**
     * 获取指定日期试剂的测试历史记录
     *
     * @param wandTestDataDTOS 每日测试数据
     * @return WandTestBiomarkerVO 列表
     * @deprecated
     */
    @PostMapping(BluetoothApiConst.DAY_WAND_TEST_DATA_LIST)
    CommonResult<List<WandDiaryTestBiomarkerDTO>> getDayWandBiomarkerData(@RequestBody List<WandTestDataDTO> wandTestDataDTOS);


    /**
     * 获取指定日期试剂的测试历史记录
     *
     * @param wandTestDataDTO 每日测试数据
     * @return List<WandTestBiomarkerDTO>
     */
    @PostMapping(BluetoothApiConst.DAY_WAND_TEST_DATA_V2)
    CommonResult<List<WandTestBiomarkerDTO>> getDayWandBiomarkerData(@RequestBody WandTestDataDTO wandTestDataDTO, @RequestParam("userId") Long userId);

    /**
     * 获取指定日期试剂的测试历史记录
     *
     * @param wandTestDataDTOS 每日测试数据
     * @return WandTestBiomarkerVO 列表
     */
    @PostMapping(BluetoothApiConst.DAY_WAND_TEST_DATA_LIST_V2)
    CommonResult<List<WandDiaryTestBiomarkerDTO>> getDayWandBiomarkerData(@RequestBody List<WandTestDataDTO> wandTestDataDTOS, @RequestParam("userId") Long userId);

    /**
     * 获取指定日期试剂的测试提醒
     *
     * @param dayTestProductsDTO 请求参数
     * @return TestRemindDTO 列表
     */
    @Deprecated
    @PostMapping(BluetoothApiConst.DAY_TEST_REMIND)
    CommonResult<List<TestRemindDTO>> getTestRemind(@RequestBody DayTestProductsDTO dayTestProductsDTO);

    /**
     * 获取指定日期试剂的测试提醒
     *
     * @param dayTestProductsDTOS 请求参数
     * @return List<DayTestProductsDTO>
     */
    @Deprecated
    @PostMapping(BluetoothApiConst.DAY_TEST_REMIND_V2)
    CommonResult<List<TestDiaryRemindDTO>> getTestRemind(@RequestBody List<DayTestProductsDTO> dayTestProductsDTOS);

    /**
     * 获取试剂参数
     *
     * @param wandBatch3 批次
     * @param uStripType 试纸类型
     * @return WandsParamRecordDTO
     */
    @GetMapping(BluetoothApiConst.WAND_PARAM_RECORD)
    CommonResult<WandsParamRecordDTO> getWandParamRecord(@RequestParam("wandBatch3") String wandBatch3,
                                                         @RequestParam("uStripType") String uStripType);

    /**
     * 获取bbt信息
     *
     * @param userId 用户id
     * @return BBTInfoDTO
     */
    @GetMapping(BluetoothApiConst.BBT_BIND_INFO)
    CommonResult<BBTInfoDTO> getBbtInfo(@RequestParam("userId") Long userId);
}
