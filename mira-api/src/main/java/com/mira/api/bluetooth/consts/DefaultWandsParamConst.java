package com.mira.api.bluetooth.consts;

public class DefaultWandsParamConst {
    /**
     * lh试纸探测上下限
     */
    public static final Float LH_LOWER_LIMIT = 3F;
    public static final Float LH_UPPER_LIMIT = 400F;

    /**
     * lh+e3g试纸 e3g探测上下限
     */
    public static final Float E3G_LOWER_LIMIT = 3F;
    public static final Float E3G_UPPER_LIMIT = 320F;

    /**
     * hcg探测上下限
     */
    public static final Float HCG_LOWER_LIMIT = 1F;
    public static final Float HCG_UPPER_LIMIT = 75000F;

    /**
     * pdg探测上下限
     */
    public static final Float PDG_LOWER_LIMIT = 1F;
    public static final Float PDG_UPPER_LIMIT = 15F;

    /**
     * E3G高量程探测上下限
     */
    public static final Float E3G_HIGH_RANGE_LOWER_LIMIT = 10F;
    public static final Float E3G_HIGH_RANGE_UPPER_LIMIT = 4000F;

    /**
     * lh+e3g+pdg试纸 探测上下限
     */
    public static final Float LHE3GPDG_E3G_LOWER_LIMIT = 10F;
    public static final Float LHE3GPDG_E3G_UPPER_LIMIT = 640F;
    public static final Float LHE3GPDG_LH_LOWER_LIMIT = 1F;
    public static final Float LHE3GPDG_LH_UPPER_LIMIT = 400F;
    public static final Float LHE3GPDG_PDG_LOWER_LIMIT = 1F;
    public static final Float LHE3GPDG_PDG_UPPER_LIMIT = 32F;
    
    /**
     * hCG定性 探测上下限
     */
    public static final Float HCG_QUALITATIVE_LOWER_LIMIT = 1F;
    public static final Float HCG_QUALITATIVE_UPPER_LIMIT = 25000F;

    /**
     * FSH（4）卵泡生成素 探测上下限
     */
    public static final Float FSH_LOWER_LIMIT = 1F;
    public static final Float FSH_UPPER_LIMIT = 200F;
}
