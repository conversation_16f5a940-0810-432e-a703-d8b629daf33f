package com.mira.api.bluetooth.dto.wand;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("试纸参数")
public class WandsParamRecordDTO {
    @ApiModelProperty("试纸批次（主键）")
    private String uBatch;

    @ApiModelProperty("试纸批次（1.3版本）")
    private String wandBatch3;

    @ApiModelProperty("试纸批次（1.2版本）")
    private String wandBatch2;

    @ApiModelProperty("批次数量")
    private Integer batchSize;

    @ApiModelProperty("试纸类型")
    private String uStripType;

    @ApiModelProperty("孵化时间")
    private String uIncubateTime;

    @ApiModelProperty("探测下限1")
    private Float fLowerLimit1;

    @ApiModelProperty("探测上限1")
    private Float fUpperLimit1;

    @ApiModelProperty("探测下限2")
    private Float fLowerLimit2;

    @ApiModelProperty("探测上限2")
    private Float fUpperLimit2;

    @ApiModelProperty("探测下限3")
    private Float fLowerLimit3;

    @ApiModelProperty("探测上限3")
    private Float fUpperLimit3;

    @ApiModelProperty("半基线宽1")
    private Double uHalfBaseWidth1;

    @ApiModelProperty("半基线宽2")
    private Double uHalfBaseWidth2;

    @ApiModelProperty("半基线宽3")
    private Double uHalfBaseWidth3;

    @ApiModelProperty("t1c斜率")
    private Float slopeT1c;

    @ApiModelProperty("t2c斜率")
    private Float slopeT2c;

    @ApiModelProperty("平均基线高")
    private Float avgBaseLine;
}
