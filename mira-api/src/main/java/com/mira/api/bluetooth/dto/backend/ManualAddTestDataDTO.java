package com.mira.api.bluetooth.dto.backend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 手动添加数据参数类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("手动添加数据参数")
public class ManualAddTestDataDTO {
    @ApiModelProperty("用户邮箱")
    @NotBlank(message = "emial can not be empty.")
    private String email;

    @ApiModelProperty("测试完成时间，e.g. 2022-10-01 08:00:00")
    @NotBlank(message = "completeTime can not be empty.")
    private String completeTime;

    @ApiModelProperty("试剂类型编号，e.g. 03")
    @NotBlank(message = "testWandType can not be empty.")
    private String testWandType;

    @ApiModelProperty("t1浓度值，没有就留空")
    private BigDecimal t1;

    @ApiModelProperty("t2浓度值，没有就留空")
    private BigDecimal t2;

    @ApiModelProperty("t3浓度值，没有就留空")
    private BigDecimal t3;
}
