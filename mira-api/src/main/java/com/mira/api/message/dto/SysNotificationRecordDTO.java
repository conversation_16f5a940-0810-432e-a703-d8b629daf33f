package com.mira.api.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("系统通知记录")
public class SysNotificationRecordDTO extends SysNotificationDefineDTO {
    @ApiModelProperty("通知记录id")
    private Long recordId;

    @ApiModelProperty("是否已读：0：未读；1：已读")
    private Integer read;

    @ApiModelProperty("创建时间")
    private String createTimeStr;

}
