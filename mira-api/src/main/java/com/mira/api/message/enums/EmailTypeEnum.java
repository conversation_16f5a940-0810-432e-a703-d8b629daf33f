package com.mira.api.message.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 邮件类型枚举
 */
@Getter
public enum EmailTypeEnum {
    /**
     * 用户注册
     */
    USER_REGISTER(1, "Activate your Mira account!", "userRegister"),

    /**
     * 用户修改邮箱
     */
    USER_CHANGE_EMAIL(2, "Confirm Your New Mira App Email", "userChangeEmail"),

    /**
     * 用户重置密码
     */
    USER_RESET_PW(3, "Reset Your Mira App Password", "userResetPw"),

    /**
     * 用户邀请 partner
     */
    USER_INVITE_PARTNER(4, "Activate Your Mira Partner Account!", "userInvitePartner"),

    /**
     * partner重置密码
     */
    PARTNER_RESET_PW(5, "Reset Your Mira App Password", "partnerResetPw"),

    /**
     * 用户修改 partner's email（发给old partner email）
     */
    PARTNER_CHANGE_EMAIL_TO_OLD_EMAIL(6, "Invitation revoked", "partnerChangeEmail2OldEmail"),

    /**
     * 用户修改 partner's email（发给new partner email）
     */
    PARTNER_CHANGE_EMAIL_TO_NEW_EMAIL(7, "Confirm Your New Mira App Email", "partnerChangeEmail2NewEmail"),

    /**
     * 用户邀请 partner inform（email 已注册成用户）
     */
    USER_INFORM_PARTNER_EXIST_USER(8, "You’ve been invited to Mira App. Confirm your account", "userInformPartnerExistUser"),

    /**
     * 用户邀请 partner inform（email 已注册成别的 partner）
     */
    USER_INFORM_PARTNER_EXIST_PARTNER(9, "You’ve been invited to Mira App. Confirm your account", "userInformPartnerExistPartner"),

    /**
     * 用户修改邮箱（发给 new email）
     */
    USER_CHANGE_EMAIL_TO_NEW_EMAIL(13, "Confirm Your New Mira App Email", "userChangeEmail2NewEmail"),

    /**
     * 用户修改邮箱（发给 old email）
     */
    USER_CHANGE_EMAIL_TO_OLD_EMAIL(14, "Confirm Your New Mira App Email", "userChangeEmail2OldEmail"),

    /**
     * 邀请医生邮件模版
     */
    INVITE_DOCTOR_EMAIL(15, "You’ve been invited to Mira Dashboard", "inviteDoctor"),

    /**
     * 邀请护士邮件模版
     */
    INVITE_NURSE_EMAIL(16, "You’ve been invited to Mira Dashboard", "inviteNurse"),

    /**
     * 邀请病人（已有 email 账号）邮件模版
     */
    INVITE_PATIENT_WITH_USER_EMAIL(17, "You’ve been invited to Mira App", "inviteUserHasApp"),

    /**
     * 邀请病人（没有 email 账号）邮件模版
     */
    INVITE_PATIENT_WITHOUT_USER_EMAIL(18, "You’ve been invited to Mira App", "inviteUserNotHasApp"),

    /**
     * （nurse）Mira Notification - Period started
     */
    NURSE_PERIOD_STARTED(19, "Mira Notification - Period started", "nursePeriodStarted"),

    /**
     * （nurse）Mira Notification - LH surge
     */
    NURSE_LH_SURGE(20, "Mira Notification - LH surge", "nurseLHSurge"),

    /**
     * （nurse）Mira Notification - No ovulation detected
     */
    NURSE_NO_OVULATION(21, "Mira Notification - No ovulation detected", "nurseNoOvulation"),

    /**
     * （nurse）Mira Notification - High E3G results
     */
    NURSE_HIGH_E3G(22, "Mira Notification - High E3G results", "nurseHighE3G"),

    /**
     * （nurse）Mira Notification - first test taken
     */
    NURSE_FIRST_TEST(23, "Mira Notification - First test taken", "nurseFirstTest"),

    /**
     * （nurse）Mira Notification - Period updated
     */
    NURSE_PERIOD_UPDATED(24, "Mira Notification - Period updated", "nursePeriodStarted"),

    /**
     * （nurse）Mira Notification - Positive hCG
     */
    NURSE_POSITIVE_HCG(25, "Mira Notification - Positive hCG", "nursePositiveHCG"),

    /**
     * （nurse）Mira Notification - Pregnant
     */
    NURSE_PREGNANT_LOG(26, "Mira Notification - Pregnant", "nursePregnantLog"),

    /**
     * 用户当日测试次数限制
     */
    USER_SN_LIMIT(27, "User Sn Limit", "userSnLimit"),

    /**
     * 来自诊所的通知
     */
    NOTIFICATION_FROM_CLINIC(28, "Notification from Clinic", "notificationFromClinic"),

    /**
     * 登录设备验证
     */
    VERIFY_LOGIN_DEVICE(29, "Confirm your Mira account", "verifyLoginDevice")

    ;

    private final Integer code;
    private final String subject;
    private final String template;

    EmailTypeEnum(Integer code, String subject, String template) {
        this.code = code;
        this.subject = subject;
        this.template = template;
    }

    public static EmailTypeEnum get(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (EmailTypeEnum emailTypeEnum : EmailTypeEnum.values()) {
            if (Objects.equals(code, emailTypeEnum.getCode())) {
                return emailTypeEnum;
            }
        }
        return null;
    }
}
