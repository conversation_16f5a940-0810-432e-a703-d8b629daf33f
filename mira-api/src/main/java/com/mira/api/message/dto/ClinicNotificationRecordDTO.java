package com.mira.api.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("clinic通知记录")
public class ClinicNotificationRecordDTO {
    @ApiModelProperty("通知记录id")
    private Long notificationRecordId;

    @ApiModelProperty("是否已读：0：未读；1：已读（默认0）")
    private Integer read;

    @ApiModelProperty("推送的文字内容")
    private String content;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("icon")
    private String icon;

    @ApiModelProperty("创建时间")
    private String createTimeStr;

}
