package com.mira.api.mongo.dto;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;


/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-03-24
 **/
@Getter
@Setter
@ApiModel("App Survey")
public class SurveyDTO {
    private String id;

    /**
     * survey name
     */
    private String name;
    /**
     * 横幅标题
     */
    private Object banner;

    /**
     * 是否激活：0-未激活，1-激活
     */
    private Integer status;
    /**
     * survey json数据
     */
    private Object surveyContent;

    /**
     * 发出时间
     */
    private String startTime;

    /**
     * 过期时间
     */
    private String endTime;


    /**
     * 额外信息
     */
    private Object extraJson;

    /**
     * 条件 同notification
     */
    private Object conditions;

    private String timeZone;
}
