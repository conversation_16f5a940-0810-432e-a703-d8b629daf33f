package com.mira.api.mongo.provider;

import com.mira.api.mongo.consts.AlgorithmRequestApiConst;
import com.mira.api.mongo.dto.AlgorithmRequestDTO;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @program: mira_server_microservices
 * @description: 算法请求调用
 * @author: xizhao.dai
 * @create: 2024-01-23 15:55
 **/
@FeignClient(value = "mira-mongo", contextId = "algorithm-request")
public interface IAlgorithmRequestProvider {
    /**
     * save算法请求
     *
     * @param algorithmRequestDTO 算法请求
     * @return true/false
     */
    @PostMapping(AlgorithmRequestApiConst.SAVE)
    CommonResult<String> save(@RequestBody AlgorithmRequestDTO algorithmRequestDTO);
}