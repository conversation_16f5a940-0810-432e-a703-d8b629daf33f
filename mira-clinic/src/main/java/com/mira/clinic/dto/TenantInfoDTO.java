package com.mira.clinic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("租户信息")
public class TenantInfoDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户code")
    private String tenantCode;

    @ApiModelProperty("管理员名称")
    private String name;

    @ApiModelProperty("管理员email")
    private String email;

    @ApiModelProperty("管理员mobile")
    private String mobile;

    @ApiModelProperty("管理员状态:1:邀请中；2:正常激活状态")
    private Integer status;

    @ApiModelProperty("管理员角色编码:1:clinic管理员;2:医生;3:护士")
    private Integer role;

    @ApiModelProperty("租户信息")
    private TenantBaseInfo tenant;

}
