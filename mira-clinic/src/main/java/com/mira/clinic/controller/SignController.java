package com.mira.clinic.controller;

import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.clinic.controller.dto.LoginDTO;
import com.mira.clinic.controller.vo.LoginVO;
import com.mira.clinic.controller.vo.TenantInfoVO;
import com.mira.clinic.controller.vo.VerifyCodeVO;
import com.mira.clinic.service.ISignService;
import com.mira.core.annotation.Anonymous;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "登录相关")
@RestController
@RequestMapping("/tenant/sign")
public class SignController {
    @Resource
    private ISignService signService;

    @Anonymous
    @ApiOperation("获取图片验证码")
    @GetMapping("/verify-code")
    public VerifyCodeVO verfiyCode() throws IOException {
        return signService.getVerifyCode();
    }

    @Anonymous
    @ApiOperation("登陆")
    @PostMapping("/login")
    public LoginVO login(@Valid @RequestBody LoginDTO loginDTO) throws Exception {
        return signService.login(loginDTO);
    }

    @Anonymous
    @ApiOperation("退出登陆")
    @PostMapping("/logout")
    public void logout() {
        signService.logout();
    }

    @ApiOperation("获取Doctor用户详情")
    @GetMapping("/info")
    public TenantInfoVO info() {
        return signService.info();
    }

    @ApiOperation("创建CheckToken")
    @PostMapping("/tenant-check-token")
    public String createTenantCheckToken(@RequestParam Long patientId) throws Exception {
        return signService.createTenantCheckToken(patientId);
    }

    @ApiOperation("修改clinic用户的密码")
    @PostMapping("/edit/pw")
    public void editPassword(@Valid @RequestBody EditPasswordDTO editPasswordDTO) {
        signService.editPassword(editPasswordDTO);
    }

    @ApiOperation("同意HIPAA隐私协议")
    @PostMapping("/agree/hipaa")
    public Integer agreeHipaa(@RequestParam Integer agree) {
        return signService.agreeHipaa(agree);
    }
}
