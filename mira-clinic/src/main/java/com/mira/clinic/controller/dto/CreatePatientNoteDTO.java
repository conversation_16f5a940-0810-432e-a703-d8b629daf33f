package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("医生创建关于病人的note请求参数")
public class CreatePatientNoteDTO {
    @ApiModelProperty(value = "病人id", required = true)
    private Long patientId;

    @ApiModelProperty(value = "内容", required = true)
    private String content;
}
