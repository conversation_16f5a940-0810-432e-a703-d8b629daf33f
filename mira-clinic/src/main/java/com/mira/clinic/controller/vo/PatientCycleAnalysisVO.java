package com.mira.clinic.controller.vo;

import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.api.user.enums.UserGoalEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("周期分析")
public class PatientCycleAnalysisVO {
    /**
     * @see UserGoalEnum
     */
    @ApiModelProperty("用户目标")
    private Integer userMode;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @ApiModelProperty(" menopause定义Stage的种类:\n" +
            "    null或者0:无标识\n" +
            "    1:'Late reproductive age'\n" +
            "    2:'Early menopause transition'\n" +
            "    3:'Late menopause transition'\n" +
            "    4:'Menopause'\n" +
            "    5:'Early menopause'\n" +
            "    6:'remature menopause'")
    private Integer menopauseStage;

    @ApiModelProperty("周期长度平均值")
    private Integer cycleLength;

    @ApiModelProperty("经期长度平均值")
    private Integer periodLength;

    @ApiModelProperty("黄体期长度平均值")
    private Integer lutealPhases;

    @ApiModelProperty("月经结束到排卵长度平均值")
    private Integer folicularPhases;

    @ApiModelProperty("排卵日Index平均值")
    private Integer ovulationEstimate;

    @ApiModelProperty("排卵日Index单位")
    private String ovulationEstimateUnit;

    /**
     * 需要针对menopause模式增加最近的FSH测试值
     */
    @ApiModelProperty("最后一条FSH测试数据")
    private TestDataDTO lastFsh;

    @ApiModelProperty("上一个测试的经期日期")
    private String lastMenstrualPeriodDate;

    @ApiModelProperty("经期延后多少天")
    private Integer periodDelay;
}
