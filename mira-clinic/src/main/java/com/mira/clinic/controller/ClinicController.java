package com.mira.clinic.controller;

import com.mira.clinic.controller.dto.*;
import com.mira.clinic.controller.vo.TenantDoctorPageVO;
import com.mira.clinic.controller.vo.TenantDoctorVO;
import com.mira.clinic.controller.vo.TenantSettingVO;
import com.mira.clinic.service.IClinicService;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 诊所管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "诊所管理")
@RestController
@RequestMapping("/tenant/clinic")
public class ClinicController {
    @Resource
    private IClinicService clinicService;

    @ApiOperation("医生与护士分页列表")
    @PostMapping("/doctor-page")
    public PageResult<TenantDoctorPageVO> doctorPage(@RequestBody TenantDoctorPageDTO tenantDoctorPageDTO) {
        return clinicService.doctorPage(tenantDoctorPageDTO);
    }

    @ApiOperation("医生与护士列表")
    @PostMapping("/doctor-list")
    public List<TenantDoctorVO> doctorList(@RequestBody TenantDoctorListDTO tenantDoctorListDTO) {
        return clinicService.doctorList(tenantDoctorListDTO);
    }

    @ApiOperation("删除医生/护士")
    @PostMapping("/doctor/remove")
    public void doctorRemove(@RequestParam Long tenantId) {
        clinicService.doctorRemove(tenantId);
    }

    @ApiOperation("删除病人")
    @PostMapping("/patient/remove")
    public void patientRemove(@RequestParam Long patientId) {
        clinicService.patientRemove(patientId);
    }

    @ApiOperation("修改医生护士关联关系")
    @PostMapping("/edit/doctor-nurse")
    public void editDoctorNurse(@RequestBody EditDoctorNurseDTO editDoctorNurseDTO) {
        clinicService.editDoctorNurse(editDoctorNurseDTO);
    }

    @ApiOperation("修改护士医生关联关系")
    @PostMapping("/edit/nurse-doctor")
    public void editNurseDoctor(@RequestBody EditNurseDoctorDTO editNurseDoctorDTO) {
        clinicService.editNurseDoctor(editNurseDoctorDTO);
    }

    @ApiOperation("setting设置")
    @PostMapping("/edit/setting")
    public void editSetting(@RequestBody EditSettingDTO editSettingDTO) {
        clinicService.editSetting(editSettingDTO);
    }

    @ApiOperation("setting查询")
    @PostMapping("/setting")
    public TenantSettingVO setting() {
        return clinicService.setting();
    }
}
