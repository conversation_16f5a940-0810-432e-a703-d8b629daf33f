package com.mira.clinic.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * clinic 设置
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-04-19
 **/
@Getter
@Setter
@TableName("app_tenant_setting")
public class AppTenantSettingEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * period started notification  默认打开
     */
    private Integer notificationPeriodStarted;

    /**
     * period edited notification  默认打开
     */
    private Integer notificationPeriodEdited;

    /**
     * LH surge notification 默认打开
     */
    private Integer notificationLhSurge;

    /**
     * first test taken notification 默认打开
     */
    private Integer notificationFirstTest;

    /**
     * Positive HCG notification 1为打开 默认打开
     */
    @TableField("notification_positive_hCG")
    private Integer notificationPositiveHCG;

    /**
     * Pregnant notification 1为打开 默认打开
     */
    private Integer notificationPregnant;
}