package com.mira.clinic.service.manager;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.iam.consts.TokenCacheConst;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.properties.CacheExpireProperties;
import com.mira.core.util.JsonUtil;
import com.mira.redis.cache.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Cache manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class CacheManager {
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Resource
    private IAlgorithmProvider algorithmProvider;
    @Resource
    private IUserProvider userProvider;

    /**
     * 检查发送给护士的邮件历史
     *
     * @param mailTypeEnum 邮件类型
     * @param nurseId      护士id
     * @param patientId    病人id
     * @return
     */
    public boolean checkExistNurseEmailHistory(EmailTypeEnum mailTypeEnum, Long nurseId, Long patientId) {
        String cacheKey = RedisCacheKeyConst.NURSE_EMAIL_HISTORY + mailTypeEnum.getCode() + ":" + nurseId + ":" + patientId;
        Boolean exist = redisComponent.exists(cacheKey);
        if (!exist) {
            redisComponent.setEx(cacheKey, "1", cacheExpireProperties.getNurseEmailInterval(), TimeUnit.MINUTES);
        }
        return exist;
    }

    /**
     * 获取算法结果表缓存
     *
     * @param userId 用户编号
     * @return AlgorithmResultDTO
     */
    public AlgorithmResultDTO getCacheAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        AlgorithmResultDTO algorithmResultDTO= null;
        try {
            algorithmResultDTO = redisComponent.get(cacheKey, AlgorithmResultDTO.class);
        } catch (Exception e) {
            log.error("get algorithm result cache error", e);
        }
        if (ObjectUtils.isEmpty(algorithmResultDTO)) {
            algorithmResultDTO = algorithmProvider.getAlgorithmResultCache(userId).getData();
        }
        return algorithmResultDTO;
    }

    /**
     * 获取Clinic标记令牌
     *
     * @param id id
     * @return 令牌
     */
    public String getClinicMarkToken(Long id) {
        String cacheKey = TokenCacheConst.CLINIC_ID_TOKEN_MAPPING + id;
        return redisComponent.get(cacheKey);
    }

    /**
     * 获取用户的 push token
     *
     * @param userId 用户id
     * @return PushTokenDTO
     */
    public PushTokenDTO getPushToken(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_FIREBASE_TOKEN + userId;
        PushTokenDTO cache;
        try {
            cache = redisComponent.get(cacheKey, PushTokenDTO.class);
        } catch (Exception e) {
            log.error("get push token cache error", e);
            cache = null;
        }
        if (ObjectUtils.isNotEmpty(cache) && StringUtils.isNotBlank(cache.getPushToken())) {
            return cache;
        }

        AppUserInfoDTO appUserInfoDTO = userProvider.getUserInfoById(userId).getData();
        PushTokenDTO pushTokenDTO = new PushTokenDTO();
        pushTokenDTO.setPushToken(appUserInfoDTO.getPushToken());
        pushTokenDTO.setPlatform(appUserInfoDTO.getPlatform());
        redisComponent.setEx(cacheKey, pushTokenDTO, cacheExpireProperties.getPushToken(), TimeUnit.DAYS);

        return pushTokenDTO;
    }
}
