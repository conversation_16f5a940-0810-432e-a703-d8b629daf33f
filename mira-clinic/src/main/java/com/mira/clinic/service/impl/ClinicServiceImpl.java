package com.mira.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mira.api.clinic.dto.*;
import com.mira.api.clinic.enums.ClinicStatusEnum;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.clinic.async.AmplitudeProducer;
import com.mira.clinic.async.KlaviyoProducer;
import com.mira.clinic.controller.dto.*;
import com.mira.clinic.controller.vo.TenantDoctorPageVO;
import com.mira.clinic.controller.vo.TenantDoctorVO;
import com.mira.clinic.controller.vo.TenantSettingVO;
import com.mira.clinic.dal.dao.*;
import com.mira.clinic.dal.entity.*;
import com.mira.clinic.dto.AppTenantPatientDTO;
import com.mira.clinic.enums.ClinicRoleEnum;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.service.IClinicService;
import com.mira.clinic.service.manager.CacheManager;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.response.PageResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Clinic服务接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ClinicServiceImpl implements IClinicService {
    @Resource
    private AppTenantDAO appTenantDAO;
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;
    @Resource
    private AppTenantDoctorPatientDAO appTenantDoctorPatientDAO;
    @Resource
    private AppTenantDoctorNurseDAO appTenantDoctorNurseDAO;
    @Resource
    private AppTenantSettingDAO appTenantSettingDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private IAuthProvider authProvider;
    @Resource
    private AmplitudeProducer amplitudeProducer;
    @Resource
    private KlaviyoProducer klaviyoProducer;

    @Override
    public UserClinicDTO getClinicInfo(String email) {
        try {
            UserClinicDTO userClinicDTO = new UserClinicDTO();
            AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByEmail(email);
            if (ObjectUtils.isEmpty(appTenantPatient)) {
                return null;
            }
            userClinicDTO.setStatus(appTenantPatient.getStatus());
            userClinicDTO.setBindTime(appTenantPatient.getModifyTime());
            AppTenantEntity appTenant = appTenantDAO.getByTenantCode(appTenantPatient.getTenantCode());
            userClinicDTO.setName(appTenant.getName());
            userClinicDTO.setCode(appTenant.getCode());
            userClinicDTO.setIcon(appTenant.getIcon());
            return userClinicDTO;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<UserClinicDTO> listClinicInfos(String email) {
        List<UserClinicDTO> userClinicDTOS = new ArrayList<>();
        try {
            List<AppTenantPatientDTO> appTenantPatientDTOS = appTenantPatientDAO.listByPatientEmail(email);
            if (CollectionUtils.isEmpty(appTenantPatientDTOS)) {
                return userClinicDTOS;
            }
            for (AppTenantPatientDTO appTenantPatientDTO : appTenantPatientDTOS) {
                UserClinicDTO userClinicDTO = new UserClinicDTO();
                Integer status = appTenantPatientDTO.getStatus();
                if (ClinicStatusEnum.REJECTION.getCode().equals(status)) {
                    continue;
                }
                userClinicDTO.setId(appTenantPatientDTO.getTenantId());
                userClinicDTO.setStatus(status);
                userClinicDTO.setBindTime(appTenantPatientDTO.getModifyTime());
                userClinicDTO.setInviteTime(appTenantPatientDTO.getCreateTime());
                AppTenantEntity appTenant = appTenantDAO.getByTenantCode(appTenantPatientDTO.getTenantCode());
                userClinicDTO.setName(appTenant.getName());
                userClinicDTO.setCode(appTenant.getCode());
                userClinicDTO.setIcon(appTenant.getIcon());
                userClinicDTOS.add(userClinicDTO);
            }
        } catch (Exception e) {
            log.error("email:{} 获取用户绑定的clinic信息失败,{}", email, e);
        }
        return userClinicDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Deprecated(since = "7.6.27")
    public void bind(Long userId, String email) {
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getByEmail(email);
        if (ObjectUtils.isNotEmpty(patientEntity)) {
            patientEntity.setStatus(ClinicStatusEnum.NORMAL.getCode());
            patientEntity.setUserId(userId);
            UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
            appTenantPatientDAO.updateById(patientEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Deprecated(since = "7.6.27")
    public void unbind(Long userId) {
        AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(appTenantPatient)) {
            Long patientId = appTenantPatient.getId();
            // 删除病人
            AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
            if (appTenantPatientEntity.getUserId() != null) {
                appTenantPatientEntity.setUserId(-appTenantPatientEntity.getUserId());
            }
            appTenantPatientDAO.removePatient(patientId);
            // 删除病人和医生的绑定关系
            appTenantDoctorPatientDAO.removeDoctorPatient(patientId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindV2(Long userId, String email, String tenantCode) {
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getByEmailAndTenantCode(email, tenantCode);
        if (ObjectUtils.isNotEmpty(patientEntity)) {
            patientEntity.setStatus(ClinicStatusEnum.NORMAL.getCode());
            patientEntity.setUserId(userId);
            UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
            appTenantPatientDAO.updateById(patientEntity);
            // set amplitude property
            amplitude(userId, tenantCode, "$set");
            // update klaviyo property
            klaviyo(userId, email, "yes");
        }
    }

    private void amplitude(Long userId, String tenantCode, String operator) {
        AppTenantEntity tenantEntity = appTenantDAO.getByTenantCode(tenantCode);
        Map<String, String> values = new HashMap<>();
        values.put("Clinic", tenantEntity.getName());
        amplitudeProducer.updateAmplitude(userId, values, operator);
    }

    private void klaviyo(Long userId, String email, String value) {
        klaviyoProducer.bindClinic(userId, email, value);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unbindV2(Long userId, String tenantCode) {
        AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getByUserIdAndTenantCode(userId, tenantCode);
        if (ObjectUtils.isNotEmpty(appTenantPatient)) {
            Long patientId = appTenantPatient.getId();
            // 删除病人
            AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
            if (appTenantPatientEntity.getUserId() != null) {
                appTenantPatientEntity.setUserId(-appTenantPatientEntity.getUserId());
            }
            appTenantPatientDAO.removePatient(patientId);
            // 删除病人和医生的绑定关系
            appTenantDoctorPatientDAO.removeDoctorPatient(patientId);

            // unset amplitude property
            amplitude(userId, tenantCode, "$unset");
            // update klaviyo property
            klaviyo(userId, appTenantPatient.getInitEmail(), "no");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectInvite(Long userId, String tenantCode) {
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getByUserIdAndTenantCodeIgnoreStatus(userId, tenantCode);
        if (ObjectUtils.isEmpty(patientEntity)) {
            return;
        }
        patientEntity.setStatus(ClinicStatusEnum.REJECTION.getCode());
        patientEntity.setUserId(userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(patientEntity.getTimeZone(), patientEntity);
        appTenantPatientDAO.updateById(patientEntity);

    }

    @Override
    public ClinicDTO getClinicByCode(String tenantCode) {
        AppTenantEntity tenant = appTenantDAO.getByTenantCode(tenantCode);
        if (ObjectUtils.isEmpty(tenant)) {
            return null;
        }
        ClinicDTO clinicDTO = new ClinicDTO();
        clinicDTO.setName(tenant.getName());
        clinicDTO.setCode(tenant.getCode());
        clinicDTO.setIcon(tenant.getIcon());
        clinicDTO.setDescription(tenant.getDescription());
        return clinicDTO;
    }

    @Override
    public PageResult<TenantDoctorPageVO> doctorPage(TenantDoctorPageDTO pageDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String tenantCode = appTenantDoctor.getTenantCode();

        Integer role = pageDTO.getRole();
        List<Integer> allowRoles = Arrays.asList(ClinicRoleEnum.DOCTOR.getCode(), ClinicRoleEnum.NURSE.getCode());
        if (!allowRoles.contains(role)) {
            throw new ClinicException("Request param role error.");
        }

        Page<AppTenantDoctorEntity> page = appTenantDoctorDAO.pageByTenantCodeAndRole(pageDTO, tenantCode, role, pageDTO.getKeyword());
        if (ObjectUtils.isEmpty(page)) {
            return new PageResult<>();
        }
        List<AppTenantDoctorEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageResult<>();
        }

        List<Long> tenantIds = records.stream()
                                      .map(AppTenantDoctorEntity::getId)
                                      .collect(Collectors.toList());
        List<TenantDoctorNurseListDTO> tenantDoctorNurseListDTOS = null;
        if (CollectionUtils.isEmpty(tenantIds)) {
            tenantDoctorNurseListDTOS = new ArrayList<>();
        } else {
            if (ClinicRoleEnum.DOCTOR.getCode().equals(role)) { // 查询医生列表，需要查询关联的护士
                tenantDoctorNurseListDTOS = appTenantDoctorNurseDAO.listNurseDTO(tenantIds);
            } else if (ClinicRoleEnum.NURSE.getCode().equals(role)) {
                tenantDoctorNurseListDTOS = appTenantDoctorNurseDAO.listDoctorDTO(tenantIds);
            }
        }

        List<TenantDoctorPageVO> tenantDoctorPageVOS = new ArrayList<>();
        List<TenantDoctorNurseListDTO> finalTenantDoctorNurseListDTOS = tenantDoctorNurseListDTOS;
        for (AppTenantDoctorEntity appTenantDoctorEntity : records) {
            TenantDoctorPageVO tenantDoctorPageVO = new TenantDoctorPageVO();
            BeanUtil.copyProperties(appTenantDoctorEntity, tenantDoctorPageVO);
            List<TenantDoctorVO> tenantDoctorVOS = new ArrayList<>();
            if (ClinicRoleEnum.DOCTOR.getCode().equals(role)) {
                List<TenantDoctorNurseListDTO> relatedNurses = finalTenantDoctorNurseListDTOS.stream()
                                                                                             .filter(tenantDoctorNurseListDTO -> tenantDoctorNurseListDTO.getDoctorId().equals(tenantDoctorPageVO.getId()))
                                                                                             .collect(Collectors.toList());
                for (TenantDoctorNurseListDTO tenantDoctorNurseListDTO : relatedNurses) {
                    TenantDoctorVO tenantDoctorVO = new TenantDoctorVO();
                    tenantDoctorVO.setId(tenantDoctorNurseListDTO.getNurseId());
                    tenantDoctorVO.setEmail(tenantDoctorNurseListDTO.getEmail());
                    tenantDoctorVO.setName(tenantDoctorNurseListDTO.getName());
                    tenantDoctorVOS.add(tenantDoctorVO);
                }
            } else if (ClinicRoleEnum.NURSE.getCode().equals(role)) {
                List<TenantDoctorNurseListDTO> relatedDoctors = finalTenantDoctorNurseListDTOS.stream()
                                                                                              .filter(tenantDoctorNurseListDTO -> tenantDoctorNurseListDTO.getNurseId().equals(tenantDoctorPageVO.getId()))
                                                                                              .collect(Collectors.toList());
                for (TenantDoctorNurseListDTO tenantDoctorNurseListDTO : relatedDoctors) {
                    TenantDoctorVO tenantDoctorVO = new TenantDoctorVO();
                    tenantDoctorVO.setId(tenantDoctorNurseListDTO.getDoctorId());
                    tenantDoctorVO.setEmail(tenantDoctorNurseListDTO.getEmail());
                    tenantDoctorVO.setName(tenantDoctorNurseListDTO.getName());
                    tenantDoctorVOS.add(tenantDoctorVO);
                }
            }

            tenantDoctorPageVO.setRelatedDoctors(tenantDoctorVOS);
            tenantDoctorPageVOS.add(tenantDoctorPageVO);
        }

        return new PageResult<>(tenantDoctorPageVOS, page.getTotal(), page.getSize(), page.getCurrent());
    }

    @Override
    public List<TenantDoctorVO> doctorList(TenantDoctorListDTO pageDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String tenantCode = appTenantDoctor.getTenantCode();

        Integer role = pageDTO.getRole();
        List<Integer> allowRoles = Arrays.asList(ClinicRoleEnum.DOCTOR.getCode(), ClinicRoleEnum.NURSE.getCode());
        if (!allowRoles.contains(role)) {
            throw new ClinicException("Request param role error.");
        }

        List<AppTenantDoctorEntity> appTenantDoctorEntities = appTenantDoctorDAO.listByTenantCodeAndRole(tenantCode, role, pageDTO.getKeyword());
        List<TenantDoctorVO> tenantDoctorVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appTenantDoctorEntities)) {
            appTenantDoctorEntities.forEach(appTenantDoctorEntity -> {
                TenantDoctorVO tenantDoctorVO = new TenantDoctorVO();
                BeanUtil.copyProperties(appTenantDoctorEntity, tenantDoctorVO);
                tenantDoctorVOS.add(tenantDoctorVO);
            });
        }
        return tenantDoctorVOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doctorRemove(Long tenantId) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity loginDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(loginDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 删除医生/护士
        AppTenantDoctorEntity tenantDoctor = appTenantDoctorDAO.getById(tenantId);
        Integer role = tenantDoctor.getRole();
        tenantDoctor.setEmail(tenantDoctor.getEmail()
                + ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)
                + new Random().nextInt(1000));
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, tenantDoctor);
        appTenantDoctorDAO.updateById(tenantDoctor);
        appTenantDoctorDAO.removeById(tenantId);

        // 删除医生/护士令牌
        String clinicToken = cacheManager.getClinicMarkToken(tenantId);
        if (StringUtils.isNotBlank(clinicToken)) {
            authProvider.deleteToken(clinicToken, UserTypeEnum.CLINIC_USER.getType());
        }
        // 删除医生护士绑定关系
        if (ClinicRoleEnum.DOCTOR.getCode().equals(role)) {
            appTenantDoctorNurseDAO.removeByDoctorId(tenantId);
        } else if (ClinicRoleEnum.NURSE.getCode().equals(role)) {
            appTenantDoctorNurseDAO.removeByNurseId(tenantId);
        }
        // 如果是医生，删除医生病人绑定关系
        if (ClinicRoleEnum.DOCTOR.getCode().equals(role)) {
            appTenantDoctorPatientDAO.removeByDoctorId(tenantId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void patientRemove(Long patientId) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        // unset amplitude property
        AppTenantPatientEntity patientEntity = appTenantPatientDAO.getById(patientId);
        if (Objects.nonNull(patientEntity)) {
            Long userId = patientEntity.getUserId();
            amplitude(userId, patientEntity.getTenantCode(), "$unset");
            // update klaviyo property
            klaviyo(userId, patientEntity.getInitEmail(), "no");
        }

        // 这里都使用物理删除
        appTenantPatientDAO.removePatient(patientId);
        appTenantDoctorPatientDAO.removeDoctorPatient(patientId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editDoctorNurse(EditDoctorNurseDTO editDoctorNurseDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        Long doctorId = editDoctorNurseDTO.getDoctorId();
        List<AppTenantDoctorNurseEntity> dbDoctorNurseEntities = appTenantDoctorNurseDAO.listByDoctorId(doctorId);

        List<Long> paramNurseIds = editDoctorNurseDTO.getNurseIds();
        List<AppTenantDoctorNurseEntity> addList = new ArrayList<>();

        // 数据库不存在关联的护士
        if (dbDoctorNurseEntities.isEmpty()) {
            if (CollectionUtils.isNotEmpty(paramNurseIds)) {
                // add all
                for (Long nurseId : paramNurseIds) {
                    AppTenantDoctorNurseEntity tenantDoctorNurseEntity = new AppTenantDoctorNurseEntity();
                    tenantDoctorNurseEntity.setTenantCode(tenantCode);
                    tenantDoctorNurseEntity.setNurseId(nurseId);
                    tenantDoctorNurseEntity.setDoctorId(doctorId);
                    UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorNurseEntity);
                    addList.add(tenantDoctorNurseEntity);
                }
                if (!addList.isEmpty()) {
                    appTenantDoctorNurseDAO.saveBatch(addList);
                }
            }
            return;
        }

        // 数据库存在关联的护士
        List<Long> dbNurseIds = dbDoctorNurseEntities.stream()
                                                     .map(AppTenantDoctorNurseEntity::getNurseId)
                                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paramNurseIds)) {
            // delete all
            appTenantDoctorNurseDAO.removeByDoctorIdAndNurseIds(doctorId, dbNurseIds);
            return;
        }

        // paramNurseIds not null && dbNurseIds not null
        List<Long> nurseIds2 = new ArrayList<>(paramNurseIds);
        paramNurseIds.removeAll(dbNurseIds);
        // 剩下的paramNurseIds需要被添加
        // add all
        for (Long nurseId : paramNurseIds) {
            AppTenantDoctorNurseEntity tenantDoctorNurseEntity = new AppTenantDoctorNurseEntity();
            tenantDoctorNurseEntity.setTenantCode(tenantCode);
            tenantDoctorNurseEntity.setNurseId(nurseId);
            tenantDoctorNurseEntity.setDoctorId(doctorId);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorNurseEntity);
            addList.add(tenantDoctorNurseEntity);
        }
        if (!addList.isEmpty()) {
            appTenantDoctorNurseDAO.saveBatch(addList);
        }

        dbNurseIds.removeAll(nurseIds2);
        // 剩下的dbNurseIds需要被删除
        if (CollectionUtils.isNotEmpty(dbNurseIds)) {
            appTenantDoctorNurseDAO.removeByDoctorIdAndNurseIds(doctorId, dbNurseIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editNurseDoctor(EditNurseDoctorDTO editNurseDoctorDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        Long nurseId = editNurseDoctorDTO.getNurseId();
        List<Long> paramDoctorIds = editNurseDoctorDTO.getDoctorIds();

        List<AppTenantDoctorNurseEntity> dbDoctorNurseEntities = appTenantDoctorNurseDAO.listByNureseId(nurseId);
        List<AppTenantDoctorNurseEntity> addList = new ArrayList<>();

        // 数据库不存在关联的医生
        if (dbDoctorNurseEntities.isEmpty()) {
            if (CollectionUtils.isNotEmpty(paramDoctorIds)) {
                // add all
                for (Long doctorId : paramDoctorIds) {
                    AppTenantDoctorNurseEntity tenantDoctorNurseEntity = new AppTenantDoctorNurseEntity();
                    tenantDoctorNurseEntity.setTenantCode(tenantCode);
                    tenantDoctorNurseEntity.setNurseId(nurseId);
                    tenantDoctorNurseEntity.setDoctorId(doctorId);
                    UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorNurseEntity);
                    addList.add(tenantDoctorNurseEntity);
                }
                if (!addList.isEmpty()) {
                    appTenantDoctorNurseDAO.saveBatch(addList);
                }
            }
            return;
        }

        // 数据库存在关联的医生
        List<Long> dbDoctorIds = dbDoctorNurseEntities.stream()
                                                      .map(AppTenantDoctorNurseEntity::getDoctorId)
                                                      .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paramDoctorIds)) {
            // delete all
            appTenantDoctorNurseDAO.removeByNurseIdAndDoctorIds(nurseId, dbDoctorIds);
            return;
        }

        // paramDoctorIds not null && dbDoctorIds not null
        List<Long> doctorIds2 = new ArrayList<>(paramDoctorIds);
        paramDoctorIds.removeAll(dbDoctorIds);
        // 剩下的paramDoctorIds需要被添加
        // add all
        for (Long doctorId : paramDoctorIds) {
            AppTenantDoctorNurseEntity tenantDoctorNurseEntity = new AppTenantDoctorNurseEntity();
            tenantDoctorNurseEntity.setTenantCode(tenantCode);
            tenantDoctorNurseEntity.setNurseId(nurseId);
            tenantDoctorNurseEntity.setDoctorId(doctorId);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorNurseEntity);
            addList.add(tenantDoctorNurseEntity);
        }
        if (!addList.isEmpty()) {
            appTenantDoctorNurseDAO.saveBatch(addList);
        }

        dbDoctorIds.removeAll(doctorIds2);
        // 剩下的dbDoctorIds需要被删除
        if (CollectionUtils.isNotEmpty(dbDoctorIds)) {
            appTenantDoctorNurseDAO.removeByNurseIdAndDoctorIds(nurseId, dbDoctorIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editSetting(EditSettingDTO editSettingDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        AppTenantSettingEntity tenantSettingEntity = appTenantSettingDAO.getByTenantCode(tenantCode);
        if (tenantSettingEntity == null) {
            // save
            tenantSettingEntity = new AppTenantSettingEntity();
            tenantSettingEntity.setTenantCode(tenantCode);
            tenantSettingEntity.setNotificationFirstTest(1);
            tenantSettingEntity.setNotificationLhSurge(1);
            tenantSettingEntity.setNotificationPeriodEdited(1);
            tenantSettingEntity.setNotificationPeriodStarted(1);
            tenantSettingEntity.setNotificationPositiveHCG(1);
            tenantSettingEntity.setNotificationPregnant(1);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantSettingEntity);
            appTenantSettingDAO.save(tenantSettingEntity);
        } else {
            // update
            BeanUtil.copyProperties(editSettingDTO, tenantSettingEntity);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, tenantSettingEntity);
            appTenantSettingDAO.updateById(tenantSettingEntity);
        }
    }

    @Override
    public TenantSettingVO setting() {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        TenantSettingVO tenantSettingVO = new TenantSettingVO();
        tenantSettingVO.setTenantCode(tenantCode);
        tenantSettingVO.setNotificationFirstTest(1);
        tenantSettingVO.setNotificationLhSurge(1);
        tenantSettingVO.setNotificationPeriodEdited(1);
        tenantSettingVO.setNotificationPeriodStarted(1);
        tenantSettingVO.setNotificationPositiveHCG(1);
        tenantSettingVO.setNotificationPregnant(1);
        AppTenantSettingEntity tenantSettingEntity = appTenantSettingDAO.getByTenantCode(tenantCode);
        if (tenantSettingEntity != null) {
            BeanUtil.copyProperties(tenantSettingEntity, tenantSettingVO);
        }
        return tenantSettingVO;
    }

    @Override
    @Deprecated(since = "7.6.27")
    public UserPatientDTO getUserPatientDTO(Long userId) {
        UserPatientDTO userPatientDTO = null;
        AppTenantPatientEntity tenantPatientEntity = appTenantPatientDAO.getByUserId(userId);
        if (tenantPatientEntity != null) {
            userPatientDTO = new UserPatientDTO();
            userPatientDTO.setStatus(tenantPatientEntity.getStatus());
            userPatientDTO.setTenantCode(tenantPatientEntity.getTenantCode());
            return userPatientDTO;
        }
        return userPatientDTO;
    }

    @Override
    public List<UserPatientDTO> listUserPatientDTOs(Long userId) {
        List<UserPatientDTO> userPatientDTOs = new ArrayList<>();
        List<AppTenantPatientEntity> appTenantPatientEntities = appTenantPatientDAO.listByUserId(userId);
        if (CollectionUtils.isEmpty(appTenantPatientEntities)) {
            return userPatientDTOs;
        }
        appTenantPatientEntities
                .stream()
                .forEach(tenantPatientEntity -> {
                    UserPatientDTO userPatientDTO = new UserPatientDTO();
                    userPatientDTO.setStatus(tenantPatientEntity.getStatus());
                    userPatientDTO.setTenantCode(tenantPatientEntity.getTenantCode());
                    userPatientDTOs.add(userPatientDTO);
                });
        return userPatientDTOs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initCreateClinic(InitCreateClinicDTO initCreateClinicDTO) {
        String timeZone = "Asia/Shanghai";
        String code = initCreateClinicDTO.getClinicCode();
        String password = initCreateClinicDTO.getPassword();
        AppTenantEntity appTenantEntity = appTenantDAO.getOne(
                Wrappers.<AppTenantEntity>lambdaQuery().eq(AppTenantEntity::getCode, code)
        );
        if (appTenantEntity != null) {
            throw new ClinicException("clinic: " + code + " has existed");
        }
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getByEmail(initCreateClinicDTO.getEmail());
        if (appTenantDoctor != null) {
            throw new ClinicException("email: " + initCreateClinicDTO.getEmail() + "has existed in clinic system, " +
                    "with tenant code " + appTenantDoctor.getTenantCode());
        }

        appTenantEntity = new AppTenantEntity();
        appTenantEntity.setCode(code);
        appTenantEntity.setName(initCreateClinicDTO.getClinicName());
        appTenantEntity.setIcon(initCreateClinicDTO.getIcon());
        appTenantEntity.setType(initCreateClinicDTO.getClinicType());
        appTenantEntity.setNotificationIcon(initCreateClinicDTO.getNotificationIcon());
        appTenantEntity.setDescription(initCreateClinicDTO.getWebsiteUrl());
        appTenantEntity.setInitPassword(password);
        appTenantEntity.setTimeZone(timeZone);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantEntity);
        appTenantDAO.save(appTenantEntity);

        appTenantDoctor = new AppTenantDoctorEntity();
        appTenantDoctor.setTenantCode(code);
        appTenantDoctor.setEmail(initCreateClinicDTO.getEmail());
        appTenantDoctor.setName(initCreateClinicDTO.getEmail());
        appTenantDoctor.setStatus(2);
        appTenantDoctor.setDoctorType(0);
        if (initCreateClinicDTO.getClinicType() == 0) {
            appTenantDoctor.setRole(1);
        } else if (initCreateClinicDTO.getClinicType() == 1) {
            appTenantDoctor.setRole(2);
        }

        String salt = RandomStringUtils.randomAlphanumeric(20);
        appTenantDoctor.setPassword(new Sha256Hash(password, salt).toHex());
        appTenantDoctor.setSalt(salt);
        appTenantDoctor.setStatus(2);
        appTenantEntity.setTimeZone(timeZone);
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantDoctor);
        appTenantDoctorDAO.save(appTenantDoctor);
    }

    @Override
    public ClinicPageResponseDTO clinicPage(ClinicPageRequestDTO clinicPageRequestDTO) {
        int currIndex = (clinicPageRequestDTO.getCurrent() - 1) * clinicPageRequestDTO.getSize();
        List<ClinicListDTO> clinicListDTOS = appTenantDAO.listByPage(currIndex, clinicPageRequestDTO.getSize(), clinicPageRequestDTO.getKeyword());
        Long count = appTenantDAO.countByKeyword(clinicPageRequestDTO.getKeyword());
        ClinicPageResponseDTO clinicPageResponseDTO = new ClinicPageResponseDTO();
        clinicPageResponseDTO.setClinicListDTOS(clinicListDTOS);
        clinicPageResponseDTO.setTotal(count);
        return clinicPageResponseDTO;
    }

    @Override
    public List<ClinicListDTO> allClinic() {
        return appTenantDAO.all();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editClinicInfo(EditClinicDTO editClinicDTO) {
        AppTenantEntity appTenantEntity = appTenantDAO.getById(editClinicDTO.getId());
        String existTenantCode = appTenantEntity.getCode();
        String newClinicCode = editClinicDTO.getClinicCode();
        String email = editClinicDTO.getEmail();
        List<AppTenantDoctorEntity> appTenantDoctorEntities = appTenantDoctorDAO.listByTenantCode(existTenantCode);
        //如果clinic已经有医生，护士，病人了，不允许修改tenantCode
        if (!existTenantCode.equals(newClinicCode)) {
            if (appTenantDoctorEntities.size() >= 2) {
                throw new ClinicException("tenant code:【" + existTenantCode + "】can not be changed " +
                        "because of having existed doctor or " +
                        "nurse.");
            }
        }
        appTenantEntity.setCode(newClinicCode);
        appTenantEntity.setName(editClinicDTO.getClinicName());
        appTenantEntity.setIcon(editClinicDTO.getIcon());
        appTenantEntity.setNotificationIcon(editClinicDTO.getNotificationIcon());
        appTenantEntity.setDescription(editClinicDTO.getWebsiteUrl());
        UpdateEntityTimeUtil.setBaseEntityTime(appTenantEntity.getTimeZone(), appTenantEntity);
        appTenantDAO.updateById(appTenantEntity);

        AppTenantDoctorEntity appTenantDoctorEntity =
                appTenantDoctorEntities.stream()
                                       .filter(appTenantDoctorEntity1 -> appTenantDoctorEntity1.getRole() == 1)
                                       .findFirst()
                                       .get();
        if (!appTenantDoctorEntity.getEmail().equals(email)) {
            AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getByEmail(email);
            if (appTenantDoctor != null) {
                throw new ClinicException("email: " + email + "has existed in clinic system, " +
                        "with tenant code " + appTenantDoctor.getTenantCode() + "role:" + appTenantDoctor.getRole());
            }
        }
        appTenantDoctorEntity.setEmail(email);
        appTenantDoctorEntity.setTenantCode(newClinicCode);
        appTenantDoctorEntity.setName(email);
        UpdateEntityTimeUtil.updateBaseEntityTime(appTenantDoctorEntity.getTimeZone(), appTenantDoctorEntity);
        appTenantDoctorDAO.updateById(appTenantDoctorEntity);
    }

    @Override
    public void deleteClinicInfo(Long id) {
        AppTenantEntity appTenantEntity = appTenantDAO.getById(id);
        String existTenantCode = appTenantEntity.getCode();
        List<AppTenantDoctorEntity> appTenantDoctorEntities = appTenantDoctorDAO.listByTenantCode(existTenantCode);
        //如果clinic已经有医生，护士，病人了，不允许修改tenantCode
        if (appTenantDoctorEntities.size() >= 2) {
            throw new ClinicException("tenant code:【" + existTenantCode + "】can not be changed " +
                    "because of having existed doctor or " +
                    "nurse.");
        }
        AppTenantDoctorEntity appTenantDoctorEntity =
                appTenantDoctorEntities.stream()
                                       .filter(appTenantDoctorEntity1 -> appTenantDoctorEntity1.getRole() == 1)
                                       .findFirst()
                                       .get();
        appTenantDAO.getBaseMapper().deleteClinicInfo(id);
        appTenantDoctorDAO.getBaseMapper().deleteClinicManager(appTenantDoctorEntity.getId());
    }
}
