package com.mira.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Maps;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleAnalysisDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.dto.wand.WandTestDataDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.provider.IMenopauseProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.UserPeriodDTO;
import com.mira.api.user.dto.user.diary.*;
import com.mira.api.user.dto.user.diary.excel.ExportTemperatureDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserDiaryIntegrationDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserMedicineDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserSymptomDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.clinic.controller.vo.*;
import com.mira.clinic.dal.dao.AppTenantDoctorDAO;
import com.mira.clinic.dal.dao.AppTenantPatientDAO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantPatientEntity;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.service.IPatientDataService;
import com.mira.clinic.service.manager.CacheManager;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.consts.enums.WeightUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.DaySuffixUtil;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 病人数据接口实现
 *
 * <AUTHOR>
 */
@Service
public class PatientDataServiceImpl implements IPatientDataService {
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private IUserProvider userProvider;
    @Resource
    private IBluetoothProvider bluetoothProvider;

    @Resource
    private IMenopauseProvider menopauseProvider;

    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public PatientCycleAnalysisVO cycleAnalysis(Long patientId) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        String clinicTenantCode = appTenantDoctor.getTenantCode();

        AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);

        if (appTenantPatientEntity == null) {
            throw new ClinicException("the patient not exist");
        }
        String patientTenantCode = appTenantPatientEntity.getTenantCode();
        if (!patientTenantCode.equals(clinicTenantCode)) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        Long userId = appTenantPatientEntity.getUserId();
        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
        if (appUserDTO == null) {
            throw new ClinicException("the patient user not exist.");
        }

        PatientCycleAnalysisVO patientCycleAnalysisVO = new PatientCycleAnalysisVO();
        AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        CycleAnalysisDTO cycleAnalysis = JsonUtil.toObject(cacheAlgorithmResult.getCycleAnalysis(), CycleAnalysisDTO.class);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cacheAlgorithmResult.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(cacheAlgorithmResult.getHormoneData(), HormoneDTO.class);

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer ttaSwitch = loginUserInfoDTO.getTtaSwitch();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        Integer userMode = UserGoalEnum.getUserMode(ttaSwitch, goalStatus);
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();

        patientCycleAnalysisVO.setUserMode(userMode);
        patientCycleAnalysisVO.setTrackingMenopause(trackingMenopause == null ? 0 : trackingMenopause);

        if (trackingMenopause != null && trackingMenopause == 1) {
            MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                    AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_clinicPatientCycleAnalysis).getData();
            patientCycleAnalysisVO.setMenopauseStage(menopauseResultDTO.getDefineStage());

            HormoneDTO lastFshHormoneDTO = hormoneDatas.stream()
                                                       .filter(hormoneDTO -> WandTypeEnum.FSH.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                                                       .max((Comparator.comparing(HormoneDTO::getTest_time)))
                                                       .orElse(null);
            if (lastFshHormoneDTO != null) {
                TestDataDTO lastFsh = new TestDataDTO();
                lastFsh.setTestTime(lastFshHormoneDTO.getTest_time());
                lastFsh.setValue(lastFshHormoneDTO.getTest_results().getValue1());
                patientCycleAnalysisVO.setLastFsh(lastFsh);
            }
            patientCycleAnalysisVO.setLastMenstrualPeriodDate(null);

        }

        if (cycleAnalysis != null) {
            patientCycleAnalysisVO.setCycleLength(cycleAnalysis.getCycle_len());
            patientCycleAnalysisVO.setPeriodLength(cycleAnalysis.getPeriod_len());
            patientCycleAnalysisVO.setLutealPhases(cycleAnalysis.getLuteal_phases());
            patientCycleAnalysisVO.setFolicularPhases(cycleAnalysis.getFolicular_phases());
            Integer ovulationEstimate = cycleAnalysis.getOvulation_estimate();
            patientCycleAnalysisVO.setOvulationEstimate(ovulationEstimate);
            if (ovulationEstimate != null) {
                patientCycleAnalysisVO.setOvulationEstimateUnit(DaySuffixUtil.getDaySuffix(ovulationEstimate));
            }
        }
        return patientCycleAnalysisVO;
    }

    @Override
    public List<PatientCalendarDayLogVO> dayLogs(Long patientId, String startDate, String endDate) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String clinicTenantCode = appTenantDoctor.getTenantCode();

        List<PatientCalendarDayLogVO> dayLogs = new ArrayList<>();
        AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
        if (appTenantPatientEntity == null) {
            throw new ClinicException("the patient not exist");
        }
        String patientTenantCode = appTenantPatientEntity.getTenantCode();
        if (!patientTenantCode.equals(clinicTenantCode)) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        Long userId = appTenantPatientEntity.getUserId();
        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
        if (appUserDTO == null) {
            throw new ClinicException("the patient user not exist.");
        }

        AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(cacheAlgorithmResult.getHormoneData(), HormoneDTO.class);

        CustomLogConfigDTO customLogConfigDTO = userProvider.getCustomLogConfig(userId).getData();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate) && LocalDateUtil.minusToDay(startDate, endDate) > 0) {
            return dayLogs;

        } else if ((StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) ||
                (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate) && startDate.equals(endDate))) {
            conditionOne(userId, timeZone, startDate, hormoneDatas, customLogConfigDTO, dayLogs);

        } else if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            conditionTwo(userId, startDate, endDate, hormoneDatas, customLogConfigDTO, dayLogs);

        } else if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
            conditionThree(userId, hormoneDatas, customLogConfigDTO, dayLogs);
        }

        return dayLogs;
    }

    private void conditionOne(Long userId, String timeZone, String startDate,
                              List<HormoneDTO> hormoneDatas,
                              CustomLogConfigDTO customLogConfigDTO,
                              List<PatientCalendarDayLogVO> dayLogs) {
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        // 加载startDate的数据
        if (LocalDateUtil.minusToDay(startDate, today) > 0) {
            return;
        }
        PatientCalendarDayLogVO calendarDayLogVO = new PatientCalendarDayLogVO();
        calendarDayLogVO.setDate(startDate);
        PatientCalendarDayLogVO.PastData pastData = new PatientCalendarDayLogVO.PastData();
        PatientCalendarDayLogVO.LogsData logsData = new PatientCalendarDayLogVO.LogsData();
        // 时间在今日之前
        List<HormoneDTO> dailyHormoneDatas = hormoneDatas.stream()
                                                         .filter(hormoneData -> startDate.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                                                         .collect(Collectors.toList());
        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setRequestType("doctor");
        wandDayTestDataDTO.setDate(startDate);
        wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
        pastData.setHormones(wandTestBiomarkerDTOS);

        UserDiaryIntegrationDTO userDiaryIntegrationDTO = userProvider.getUserDiaryIntegration(userId, startDate).getData();
        logsData.setSymptoms(userDiaryIntegrationDTO.getUserSymptomDTOS());
        if (customLogConfigDTO.getMedications()) {
            logsData.setMedications(userDiaryIntegrationDTO.getUserMedicineDTOS());
        }
        logsData.setTempUnit(customLogConfigDTO.getTempUnit());
        logsData.setTemperatures(userDiaryIntegrationDTO.getTemperatureDTOS());
        logsData.setAppUserDiaryMoodsVO(userDiaryIntegrationDTO.getUserDiaryMoodsDTO());

        String weightUnit = customLogConfigDTO.getWeightUnit();
        this.buildDiaryLogsData(logsData, userDiaryIntegrationDTO.getUserDiaryDTO(), weightUnit);
        pastData.setLogsData(logsData);

        calendarDayLogVO.setPastData(pastData);
        dayLogs.add(calendarDayLogVO);
    }

    private void conditionTwo(Long userId, String startDate, String endDate,
                              List<HormoneDTO> hormoneDatas,
                              CustomLogConfigDTO customLogConfigDTO,
                              List<PatientCalendarDayLogVO> dayLogs) {
        // 加载给定的某个月的数据
        List<String> dates = new ArrayList<>();
        List<UserDiaryDTO> userDiaryDTOS = userProvider.listUserDiary(userId, startDate, endDate).getData();
        List<String> diaryDates = userDiaryDTOS.stream()
                                               .map(UserDiaryDTO::getDiaryDayStr)
                                               .collect(Collectors.toList());
        for (String diaryDate : diaryDates) {
            if (!dates.contains(diaryDate)) {
                dates.add(diaryDate);
            }
        }

        Map<String, UserDiaryIntegrationDTO> diaryIntegrationDTOMap = Maps.newHashMap();
        if (!dates.isEmpty()) {
            diaryIntegrationDTOMap = userProvider.listUserDiaryIntegration(userId, dates).getData();
        }

        for (String date : dates) {
            UserDiaryIntegrationDTO userDiaryIntegrationDTO = diaryIntegrationDTOMap.get(date);

            PatientCalendarDayLogVO calendarDayLogVO = new PatientCalendarDayLogVO();
            calendarDayLogVO.setDate(date);
            PatientCalendarDayLogVO.PastData pastData = new PatientCalendarDayLogVO.PastData();
            PatientCalendarDayLogVO.LogsData logsData = new PatientCalendarDayLogVO.LogsData();
            // 时间在今日之前
            List<HormoneDTO> dailyHormoneDatas = hormoneDatas.stream()
                                                             .filter(hormoneData -> date.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                                                             .collect(Collectors.toList());
            WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
            wandDayTestDataDTO.setRequestType("doctor");
            wandDayTestDataDTO.setDate(date);
            wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
            List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
            pastData.setHormones(wandTestBiomarkerDTOS);

            UserDiaryDTO userDiaryDTO = userDiaryIntegrationDTO.getUserDiaryDTO();
            List<UserSymptomDTO> userSymptomDTOS = userDiaryIntegrationDTO.getUserSymptomDTOS();
            UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryIntegrationDTO.getUserDiaryMoodsDTO();
            List<UserMedicineDTO> userMedicineDTOS = userDiaryIntegrationDTO.getUserMedicineDTOS();
            List<TemperatureDTO> temperatureDTOS = userDiaryIntegrationDTO.getTemperatureDTOS();

            if (ObjectUtils.isNotEmpty(userSymptomDTOS)) {
                logsData.setSymptoms(userSymptomDTOS);
            }
            if (ObjectUtils.isNotEmpty(userDiaryMoodsDTO)) {
                logsData.setAppUserDiaryMoodsVO(userDiaryMoodsDTO);
            }
            if (customLogConfigDTO.getMedications()) {
                logsData.setMedications(userMedicineDTOS);
            }

            logsData.setTempUnit(customLogConfigDTO.getTempUnit());
            logsData.setTemperatures(temperatureDTOS);

            String weightUnit = customLogConfigDTO.getWeightUnit();
            this.buildDiaryLogsData(logsData, userDiaryDTO, weightUnit);
            pastData.setLogsData(logsData);

            calendarDayLogVO.setPastData(pastData);
            dayLogs.add(calendarDayLogVO);
        }
    }

    private void conditionThree(Long userId,
                                List<HormoneDTO> hormoneDatas,
                                CustomLogConfigDTO customLogConfigDTO,
                                List<PatientCalendarDayLogVO> dayLogs) {
        // 加载所有测试数据的日期
        List<String> dates = new ArrayList<>();
        for (HormoneDTO hormoneData : hormoneDatas) {
            String date = LocalDateUtil.dateTime2Date(hormoneData.getTest_time());
            if (!dates.contains(date)) {
                dates.add(date);
            }
        }

        Map<String, UserDiaryIntegrationDTO> diaryIntegrationDTOMap = Maps.newHashMap();
        if (!dates.isEmpty()) {
            diaryIntegrationDTOMap = userProvider.listUserDiaryIntegration(userId, dates).getData();
        }

        for (String date : dates) {
            UserDiaryIntegrationDTO userDiaryIntegrationDTO = diaryIntegrationDTOMap.get(date);

            PatientCalendarDayLogVO calendarDayLogVO = new PatientCalendarDayLogVO();
            calendarDayLogVO.setDate(date);
            PatientCalendarDayLogVO.PastData pastData = new PatientCalendarDayLogVO.PastData();
            PatientCalendarDayLogVO.LogsData logsData = new PatientCalendarDayLogVO.LogsData();
            // 时间在今日之前
            List<HormoneDTO> dailyHormoneDatas = hormoneDatas.stream()
                                                             .filter(hormoneData -> date.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                                                             .collect(Collectors.toList());
            WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
            wandDayTestDataDTO.setRequestType("doctor");
            wandDayTestDataDTO.setDate(date);
            wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
            List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
            pastData.setHormones(wandTestBiomarkerDTOS);

            UserDiaryDTO userDiaryDTO = userDiaryIntegrationDTO.getUserDiaryDTO();
            List<UserSymptomDTO> userSymptomDTOS = userDiaryIntegrationDTO.getUserSymptomDTOS();
            UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryIntegrationDTO.getUserDiaryMoodsDTO();
            List<UserMedicineDTO> userMedicineDTOS = userDiaryIntegrationDTO.getUserMedicineDTOS();
            List<TemperatureDTO> temperatureDTOS = userDiaryIntegrationDTO.getTemperatureDTOS();

            if (ObjectUtils.isNotEmpty(userSymptomDTOS)) {
                logsData.setSymptoms(userSymptomDTOS);
            }
            if (ObjectUtils.isNotEmpty(userDiaryMoodsDTO)) {
                logsData.setAppUserDiaryMoodsVO(userDiaryMoodsDTO);
            }
            if (customLogConfigDTO.getMedications()) {
                logsData.setMedications(userMedicineDTOS);
            }

            logsData.setTempUnit(customLogConfigDTO.getTempUnit());
            logsData.setTemperatures(temperatureDTOS);

            String weightUnit = customLogConfigDTO.getWeightUnit();
            this.buildDiaryLogsData(logsData, userDiaryDTO, weightUnit);
            pastData.setLogsData(logsData);

            calendarDayLogVO.setPastData(pastData);
            dayLogs.add(calendarDayLogVO);
        }
    }

    private void buildDiaryLogsData(PatientCalendarDayLogVO.LogsData logsData, UserDiaryDTO userDiaryDTO, String weightUnit) {
        logsData.setWeightUnit(weightUnit);
        if (ObjectUtils.isEmpty(userDiaryDTO)) {
            return;
        }
        logsData.setSex(userDiaryDTO.getSex());
        logsData.setNotes(userDiaryDTO.getNotes());
        logsData.setMucusType(userDiaryDTO.getMucusType());
        logsData.setMucusFlow(userDiaryDTO.getMucusFlow());
        logsData.setPregnant(userDiaryDTO.getPregnant());
        logsData.setOpk(userDiaryDTO.getOpk());
        logsData.setFlowAndSpotting(userDiaryDTO.getFlowAndSpotting());
        logsData.setCervicalPosition(userDiaryDTO.getCervicalPosition());
        logsData.setCervicalFirmness(userDiaryDTO.getCervicalFirmness());
        logsData.setCervicalOpenness(userDiaryDTO.getCervicalOpenness());
        logsData.setGlucoseControl(userDiaryDTO.getGlucoseControl());
        if (userDiaryDTO.getWeightK() != null) {
            if (WeightUnitEnum.K.getValue().equals(weightUnit)) {
                logsData.setWeightValue(userDiaryDTO.getWeightK());
            } else {
                logsData.setWeightValue(userDiaryDTO.getWeightL());
            }
        }
    }

    @Override
    public PatientExportDataVO exportAllDayLogs(Long patientId) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String clinicTenantCode = appTenantDoctor.getTenantCode();

        List<PatientCalendarDayLogVO> dayLogs = new ArrayList<>();
        AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
        if (appTenantPatientEntity == null) {
            throw new ClinicException("the patient not exist");
        }
        String patientTenantCode = appTenantPatientEntity.getTenantCode();
        if (!patientTenantCode.equals(clinicTenantCode)) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        Long userId = appTenantPatientEntity.getUserId();
        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
        if (appUserDTO == null) {
            throw new ClinicException("the patient user not exist.");
        }

        CustomLogConfigDTO customLogConfigDTO = userProvider.getCustomLogConfig(userId).getData();
        //参考 conditionThree

        Map<String, ExportUserDiaryIntegrationDTO> diaryIntegrationDTOMap = userProvider.listUserAllDiaryIntegration(userId).getData();

        PatientExportDataVO patientExportDataVO = new PatientExportDataVO();
        List<PatientDayLogVO> patientDayLogs = new ArrayList<>();
        diaryIntegrationDTOMap.forEach(
                (date, userDiaryIntegrationDTO) -> {
                    PatientDayLogVO patientDayLogVO = new PatientDayLogVO();
                    patientDayLogVO.setDate(date);
                    UserDiaryDTO userDiaryDTO = userDiaryIntegrationDTO.getUserDiaryDTO();
                    UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryIntegrationDTO.getUserDiaryMoodsDTO();
                    ExportUserMedicineDTO exportUserMedicineDTO = userDiaryIntegrationDTO.getMedicines();
                    ExportUserSymptomDTO exportUserSymptomDTO = userDiaryIntegrationDTO.getSymptoms();
                    List<ExportTemperatureDTO> temperatureDTOS = userDiaryIntegrationDTO.getTemperatureDTOS();

                    patientDayLogVO.setTempUnit(customLogConfigDTO.getTempUnit());
                    patientDayLogVO.setTemperatures(temperatureDTOS);

                    String weightUnit = customLogConfigDTO.getWeightUnit();
                    patientDayLogVO.setWeightUnit(weightUnit);
                    if (!ObjectUtils.isEmpty(userDiaryDTO)) {
                        patientDayLogVO.setSex(userDiaryDTO.getSex());
                        patientDayLogVO.setMucusType(userDiaryDTO.getMucusType());
                        patientDayLogVO.setMucusFlow(userDiaryDTO.getMucusFlow());
                        patientDayLogVO.setPregnant(userDiaryDTO.getPregnant());
                        patientDayLogVO.setOpk(userDiaryDTO.getOpk());
                        patientDayLogVO.setFlowAndSpotting(userDiaryDTO.getFlowAndSpotting());
                        patientDayLogVO.setCervicalPosition(userDiaryDTO.getCervicalPosition());
                        patientDayLogVO.setCervicalFirmness(userDiaryDTO.getCervicalFirmness());
                        patientDayLogVO.setCervicalOpenness(userDiaryDTO.getCervicalOpenness());
                        if (userDiaryDTO.getWeightK() != null) {
                            if (WeightUnitEnum.K.getValue().equals(weightUnit)) {
                                patientDayLogVO.setWeight(userDiaryDTO.getWeightK());
                            } else {
                                patientDayLogVO.setWeight(userDiaryDTO.getWeightL());
                            }
                        }
                        patientDayLogVO.setDiaryCreateTimeStr(userDiaryDTO.getCreateTimeStr());
                    }
                    if (exportUserSymptomDTO != null) {
                        patientDayLogVO.setSymptoms(exportUserSymptomDTO.getUserSymptoms());
                        patientDayLogVO.setSymptomCreateTimeStr(exportUserSymptomDTO.getCreateTimeStr());
                    }
                    if (customLogConfigDTO.getMedications() && exportUserMedicineDTO != null) {
                        patientDayLogVO.setMedications(exportUserMedicineDTO.getUserMedicines());
                        patientDayLogVO.setMedicineCreateTimeStr(exportUserMedicineDTO.getCreateTimeStr());
                    }
                    if (ObjectUtils.isNotEmpty(userDiaryMoodsDTO)) {
                        BeanUtil.copyProperties(userDiaryMoodsDTO, patientDayLogVO);
                        patientDayLogVO.setMoodCreateTimeStr(userDiaryMoodsDTO.getCreateTimeStr());
                    }
                    patientDayLogs.add(patientDayLogVO);
                }
        );

        patientExportDataVO.setPatientDayLogs(patientDayLogs);

        UserPeriodDTO userPeriodDTO = userProvider.getUserPeriod(userId).getData();
        String periodData = userPeriodDTO.getPeriodData();
        if (StringUtils.isNotBlank(periodData)) {
            List<PatientManualPeriodInfo> patientManualPeriodInfos = JsonUtil.toArray(periodData, PatientManualPeriodInfo.class);
            patientManualPeriodInfos
                    .forEach(patientManualPeriodInfo -> {
                        patientManualPeriodInfo.setDate_period_end(
                                LocalDateUtil.plusDay(patientManualPeriodInfo.getDate_period_end(), -1,
                                        DatePatternConst.DATE_PATTERN)
                        );
                    });
            patientExportDataVO.setPatientManualPeriodInfos(patientManualPeriodInfos);
        }
        return patientExportDataVO;
    }
}
