package com.mira.clinic.service;

import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.clinic.controller.dto.LoginDTO;
import com.mira.clinic.controller.vo.LoginVO;
import com.mira.clinic.controller.vo.TenantInfoVO;
import com.mira.clinic.controller.vo.VerifyCodeVO;

import java.io.IOException;

/**
 * 登录相关接口
 *
 * <AUTHOR>
 */
public interface ISignService {
    /**
     * 获取图片验证码
     *
     * @return VerifyCodeVO
     */
    VerifyCodeVO getVerifyCode() throws IOException;

    /**
     * 登陆
     *
     * @param loginDTO 登陆参数
     * @return LoginVO
     */
    LoginVO login(LoginDTO loginDTO) throws Exception;

    /**
     * 退出登陆
     */
    void logout();

    /**
     * 获取Doctor用户详情
     *
     * @return TenantInfoVO
     */
    TenantInfoVO info();

    /**
     * 创建CheckToken
     *
     * @param patientId 病人id
     * @return CheckToken
     */
    String createTenantCheckToken(Long patientId) throws Exception;

    /**
     * 修改密码
     *
     * @param editPasswordDTO
     */
    void editPassword(EditPasswordDTO editPasswordDTO);

    Integer agreeHipaa(Integer agree);
}
