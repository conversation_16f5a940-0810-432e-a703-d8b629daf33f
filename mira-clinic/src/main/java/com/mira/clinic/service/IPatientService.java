package com.mira.clinic.service;

import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.clinic.enums.PregnantFlagEnum;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.clinic.controller.dto.EditPatientDoctorDTO;
import com.mira.clinic.controller.dto.PatientDataPageDTO;
import com.mira.clinic.controller.dto.TenantDoctorPatientPageDTO;
import com.mira.clinic.controller.dto.TenantPatientPageDTO;
import com.mira.clinic.controller.vo.PatientHormoneVO;
import com.mira.clinic.controller.vo.TenantPatientPageByClinicVO;
import com.mira.clinic.controller.vo.TenantPatientPageVO;
import com.mira.mybatis.response.PageResult;

/**
 * 病人相关业务接口
 *
 * <AUTHOR>
 */
public interface IPatientService {
    /**
     * 处理病人编辑经期
     *
     * @param userId                   用户id
     * @param timeZone                 时区
     * @param algorithmRequestTypeEnum 算法请求类型
     */
    void processEditPeriod(Long userId, String timeZone, AlgorithmRequestTypeEnum algorithmRequestTypeEnum);

    /**
     * 处理病人孕期变化
     *
     * @param userId           用户id
     * @param timeZone         时区
     * @param pregnantFlagEnum 怀孕标记
     */
    void processPregnantFlag(Long userId, String timeZone, PregnantFlagEnum pregnantFlagEnum);

    /**
     * 处理病人新上传数据
     *
     * @param userId        用户id
     * @param timeZone      时区
     * @param emailTypeEnum 邮件类型
     */
    void processNewHormone(Long userId, String timeZone, EmailTypeEnum emailTypeEnum);

    /**
     * 查询诊所管理员的所有病人
     *
     * @param tenantPatientPageDTO 查询参数
     * @return 病人列表
     */
    PageResult<TenantPatientPageByClinicVO> patientPageByClinic(TenantPatientPageDTO tenantPatientPageDTO);

    /**
     * 查询医生的所有病人
     *
     * @param tenantDoctorPatientPageDTO 查询参数
     * @return 病人列表
     */
    PageResult<TenantPatientPageVO> patientPageByDoctor(TenantDoctorPatientPageDTO tenantDoctorPatientPageDTO);

    /**
     * 查询护士的所有病人
     *
     * @param tenantDoctorPatientPageDTO 查询参数
     * @return 病人列表
     */
    PageResult<TenantPatientPageVO> patientPageByNurse(TenantDoctorPatientPageDTO tenantDoctorPatientPageDTO);

    /**
     * 修改病人关联的医生
     *
     * @param editPatientDoctorDTO 修改参数
     */
    void editPatientDoctor(EditPatientDoctorDTO editPatientDoctorDTO);

    /**
     * 分页展示病人的测试数据结果
     *
     * @param patientDataPageDTO 查询参数
     * @return 病人测试数据结果
     */
    PageResult<PatientHormoneVO> dataPage(PatientDataPageDTO patientDataPageDTO);

    /**
     * 更新病人邮箱
     *
     * @param userId    用户id
     * @param email     邮箱
     */
    void updatePatientEmail(Long userId, String email);
}
