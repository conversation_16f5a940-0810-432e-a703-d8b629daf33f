package com.mira.clinic.async;

import com.mira.api.message.dto.CommonEmailDTO;
import com.mira.api.message.dto.SendEmailDTO;
import com.mira.api.message.enums.EmailRoleEnum;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.clinic.controller.dto.InvitePatientDTO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantEntity;
import com.mira.api.clinic.dto.NursePatientEmailDTO;
import com.mira.clinic.dto.TenantInfoDTO;
import com.mira.clinic.factory.email.InviteDoctorEmailFactory;
import com.mira.clinic.factory.email.InviteNurseEmailFactory;
import com.mira.clinic.factory.email.InvitePatientEmailFactory;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.web.properties.RsaProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 邮件发送生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class EmailProducer {
    @Resource
    private RsaProperties rsaProperties;

    @Resource
    private IMessageProvider messageProvider;

    /**
     * 发送病人信息的邮件给护士
     *
     * @param commonEmailDTO       邮件信息
     * @param nursePatientEmailDTO 病人信息
     */
    public void sendNursePatientEmail(String timeZone, NursePatientEmailDTO nursePatientEmailDTO, EmailTypeEnum emailTypeEnum) {
        Map<String, String> emailVariable = new HashMap<>();
        emailVariable.put("patientNumber", nursePatientEmailDTO.getPatientNumber());
        emailVariable.put("id", nursePatientEmailDTO.getNurseId().toString());

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(nursePatientEmailDTO.getNurseId());
        commonEmailDTO.setTimeZone(timeZone);
        commonEmailDTO.setToEmail(nursePatientEmailDTO.getNurseEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(emailTypeEnum);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.NURSE);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
    }

    /**
     * 邀请医生
     *
     * @param appTenantDoctor 医生信息
     * @param appTenant       诊所信息
     */
    public Map<String, String> inviteDoctor(AppTenantDoctorEntity appTenantDoctor, AppTenantEntity appTenant) {
        Map<String, String> emailVariable = InviteDoctorEmailFactory.createTemplate(appTenantDoctor, appTenant, rsaProperties);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(appTenantDoctor.getId());
        commonEmailDTO.setTimeZone(appTenantDoctor.getTimeZone());
        commonEmailDTO.setToEmail(appTenantDoctor.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(EmailTypeEnum.INVITE_DOCTOR_EMAIL);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.DOCTOR);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 邀请护士
     *
     * @param appTenantDoctor 护士信息
     * @param appTenant       诊所信息
     * @param doctorIds       doctor列表
     */
    public Map<String, String> inviteNurse(AppTenantDoctorEntity appTenantDoctor,
                                           AppTenantEntity appTenant,
                                           List<Long> doctorIds) {
        Map<String, String> emailVariable = InviteNurseEmailFactory.createTemplate(appTenantDoctor, appTenant, doctorIds, rsaProperties);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(appTenantDoctor.getId());
        commonEmailDTO.setTimeZone(appTenantDoctor.getTimeZone());
        commonEmailDTO.setToEmail(appTenantDoctor.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(EmailTypeEnum.INVITE_NURSE_EMAIL);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.NURSE);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 邀请病人
     *
     * @param emailTypeEnum    邮件类型
     * @param tenantInfoDTO    诊所信息
     * @param invitePatientDTO 邀请病人信息
     * @param userId           用户id
     * @param patientId        病人id
     */
    public Map<String, String> invitePatient(EmailTypeEnum emailTypeEnum,
                                             TenantInfoDTO tenantInfoDTO,
                                             InvitePatientDTO invitePatientDTO,
                                             Long userId,
                                             Long patientId) {
        Map<String, String> emailVariable = InvitePatientEmailFactory
                .createTemplate(rsaProperties, tenantInfoDTO, invitePatientDTO, userId, patientId);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(tenantInfoDTO.getId());
        commonEmailDTO.setTimeZone(ContextHolder.get(HeaderConst.TIME_ZONE));
        commonEmailDTO.setToEmail(invitePatientDTO.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(emailTypeEnum);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.USER);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送邮件给病人
     *
     * @param appUserDTO     用户信息
     * @param appUserInfoDTO 用户详细信息
     * @param clinicName     诊所名称
     */
    public void sendEmailToPatient(AppUserDTO appUserDTO, AppUserInfoDTO appUserInfoDTO, String clinicName) {
        HashMap<String, String> emailVariable = new HashMap<>();
        emailVariable.put("userNickName", appUserInfoDTO.getNickname());
        emailVariable.put("clinicName", clinicName);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(appUserDTO.getId());
        commonEmailDTO.setTimeZone(appUserDTO.getTimeZone());
        commonEmailDTO.setToEmail(appUserDTO.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(EmailTypeEnum.NOTIFICATION_FROM_CLINIC);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.DOCTOR);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
    }
}
