spring:
  application:
    name: mira-bluetooth
  profiles:
    active: dev
  mvc:
    pathmatch:
      # spring boot 2.6 以上兼容 swagger3
      matching-strategy: ant_path_matcher
  cloud:
    # 负载均衡
    loadbalancer:
      nacos:
        enabled: true
  # 链路追踪
  sleuth:
    traceId128: true
    sampler:
      probability: 0.1
feign:
  client:
    config:
      default:
        # 连接超时时间
        connectTimeout: 30000
        # 读取超时时间
        readTimeout: 60000
  # 不使用 httpclient
  httpclient:
    enabled: false
  # 使用 okhttp
  okhttp:
    enabled: true

retrofit:
  # 超时时间
  global-timeout:
    connect-timeout-ms: 30000
    read-timeout-ms: 30000
    write-timeout-ms: 30000
  # 错误重试
  global-retry:
    enable: true
    max-retries: 1

server:
  port: 8084
  shutdown: graceful
  tomcat:
    threads:
      # 最大工作线程数
      max: 200
      # 最小空闲线程数
      min-spare: 20
    # 最大连接数
    max-connections: 8192
    # 等待队列长度
    accept-count: 100
    # 连接超时时间(毫秒)
    connection-timeout: 30000
    # Keep-Alive超时
    keep-alive-timeout: 60000
    # 单连接最大Keep-Alive请求数
    max-keep-alive-requests: 200
    # 处理器缓存
    processor-cache: 200