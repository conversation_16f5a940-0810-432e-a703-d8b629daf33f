package com.mira.bluetooth.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 怀孕模式信息表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_pregnant_mode_info_v2")
public class AppPregnantModeInfoV2Entity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手动选择的最后一个实周期的开始日
     */
    private String lastPeriodDate;

    /**
     * 用户自己输入的受孕第一天（如果有数据，用它来作为怀孕模式的week1 day1）
     */
    private String conceptionDate;

    /**
     * 医生给的预测生产的日（预产期要大于受孕日）
     */
    private String dueDate;

    /**
     * 当前肚子里的小孩数
     */
    private Integer numberOfChildren;

    /**
     * 是否结束. 0表示未结束，1表示结束 default 0
     */
    private Integer isEnd;
    /**
     * 结束怀孕原因：1:delivered baby;2:miscarriage;3:abortion;4:skip
     */
    private Integer endReason;
    /**
     * 实际生产时间（生产后结束怀孕模式）
     */
    private String deliveryDate;
    
}
