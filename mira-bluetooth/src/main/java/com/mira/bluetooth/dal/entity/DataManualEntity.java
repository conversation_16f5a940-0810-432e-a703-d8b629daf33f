package com.mira.bluetooth.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-21
 **/
@Getter
@Setter
@TableName("app_data_manual")
public class DataManualEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 试剂完成时间
     */
    @TableField("complete_time")
    private String completeTime;

    @TableField("complete_timestamp")
    private Long completeTimestamp;

    /**
     * 试剂类型
     */
    @TableField("test_wand_type")
    private String testWandType;
    /**
     * 试剂批次
     */
    @TableField("test_wand_batch")
    private String testWandBatch;
    /**
     * t1浓度值
     */
    @TableField("t1_con_value")
    private BigDecimal t1ConValue;
    /**
     * t2浓度值
     */
    @TableField("t2_con_value")
    private BigDecimal t2ConValue;
    /**
     * t3浓度值
     */
    @TableField("t3_con_value")
    private BigDecimal t3ConValue;

    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;
    /**
     * 状态: 0:未处理;1:正在处理;2:添加成功;3:添加失败(默认0)
     */
    private Integer status;
    /**
     * 通知状态: 0:未通知(默认);29:Your data was added;30:Your data was synchronized;31:Your data was not added to your profile
     */
    private Long notificationStatus;
    /**
     * 0:等于数值；1:大于数值；2:小于数值(默认0)
     */
    private Integer data1Compare;
    private Integer data2Compare;
    private Integer data3Compare;
}
