package com.mira.bluetooth.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mira.bluetooth.dal.entity.DataManualEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * @program: mira_server
 * @description:
 * @author: xizhao.dai
 * @create: 2022-03-21 15:41
 **/
@Mapper
public interface DataManualMapper extends BaseMapper<DataManualEntity> {
    Page<DataManualEntity> dataPage(Page<?> page, @Param("map") Map<String, Object> map);
    Page<DataManualEntity> userDataPage(Page<?> page, @Param("map") Map<String, Object> map);
}