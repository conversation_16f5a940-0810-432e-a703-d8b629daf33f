package com.mira.bluetooth.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.bluetooth.enums.BluetoothDataErrorEnum;
import com.mira.api.bluetooth.enums.BluetoothDataWarningEnum;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.dal.mapper.AppDataUploadMapper;
import com.mira.core.consts.enums.WandTypeEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * app_data_upload DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppDataUploadDAO extends ServiceImpl<AppDataUploadMapper, AppDataUploadEntity> {
    private final static List<String> IN_LIST_ERROR_CODE;
    private final static List<String> IN_LIST_WARN_CODE;
    private final static List<String> COUNT_VALID_WARN_CODE;

    static {
        IN_LIST_ERROR_CODE = Arrays.asList(
                BluetoothDataErrorEnum.CODE_0.getValue(),
                BluetoothDataErrorEnum.CODE_1.getValue(),
                BluetoothDataErrorEnum.CODE_2_L.getValue(),
                BluetoothDataErrorEnum.CODE_2_H.getValue(),
                BluetoothDataErrorEnum.CODE_3.getValue(),
                BluetoothDataErrorEnum.CODE_4.getValue(),
                BluetoothDataErrorEnum.CODE_5.getValue(),
                BluetoothDataErrorEnum.CODE_6.getValue()
        );
        IN_LIST_WARN_CODE = Arrays.asList(
                BluetoothDataWarningEnum.CODE_0.getValue(),
                BluetoothDataWarningEnum.CODE_1.getValue(),
                BluetoothDataWarningEnum.CODE_5.getValue(),
                BluetoothDataWarningEnum.CODE_6.getValue(),
                BluetoothDataWarningEnum.CODE_7.getValue(),
                BluetoothDataWarningEnum.CODE_9.getValue()
        );
        COUNT_VALID_WARN_CODE = Arrays.asList(
                BluetoothDataWarningEnum.CODE_0.getValue(),
                BluetoothDataWarningEnum.CODE_5.getValue(),
                BluetoothDataWarningEnum.CODE_9.getValue()
        );
    }

    public List<Long> getCompleteTimestampByUserId(Long userId) {
        return baseMapper.getCompleteTimestampByUserId(userId);
    }

    public Long getUserMaxCompleteTime(Long userId) {
        AppDataUploadEntity appDataUploadEntity = getOne(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .select(AppDataUploadEntity::getCompleteTimestamp)
                .eq(AppDataUploadEntity::getUserId, userId)
                .orderByDesc(AppDataUploadEntity::getCompleteTimestamp)
                .last("limit 1"));
        if (ObjectUtils.isEmpty(appDataUploadEntity)) {
            return null;
        }
        return ObjectUtils.isNotEmpty(appDataUploadEntity.getCompleteTimestamp())
                ? appDataUploadEntity.getCompleteTimestamp()
                : null;
    }

    public List<AppDataUploadEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getUserId, userId)
                .in(AppDataUploadEntity::getError, IN_LIST_ERROR_CODE)
                .in(AppDataUploadEntity::getWarning, IN_LIST_WARN_CODE)
                .orderByAsc(AppDataUploadEntity::getCompleteTime));
    }

    public long countValidDataBySnAndDay(String sn, String day) {
        List<String> allowTestWandTypes = Arrays.asList(
                "0" + WandTypeEnum.LH.getString(),
                "0" + WandTypeEnum.E3G_LH.getString(),
                "0" + WandTypeEnum.HCG.getString(),
                "0" + WandTypeEnum.PDG.getString(),
                WandTypeEnum.LH_E3G_PDG.getString(),
                WandTypeEnum.E3G_HIGH_RANGE.getString(),
                WandTypeEnum.HCG_QUALITATIVE.getString(),
                WandTypeEnum.FSH.getString());

        return count(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getSn, sn)
                .in(AppDataUploadEntity::getTestWandType, allowTestWandTypes)
                .in(AppDataUploadEntity::getWarning, COUNT_VALID_WARN_CODE)
                .eq(AppDataUploadEntity::getError, BluetoothDataErrorEnum.CODE_0.getValue())
                .ge(AppDataUploadEntity::getCompleteTime, day));
    }

    public long countValidDataByUserId(Long userId) {
        List<String> allowTestWandTypes = Arrays.asList(
                "0" + WandTypeEnum.LH.getString(),
                "0" + WandTypeEnum.E3G_LH.getString(),
                "0" + WandTypeEnum.HCG.getString(),
                "0" + WandTypeEnum.PDG.getString(),
                WandTypeEnum.LH_E3G_PDG.getString(),
                WandTypeEnum.E3G_HIGH_RANGE.getString(),
                WandTypeEnum.HCG_QUALITATIVE.getString(),
                WandTypeEnum.FSH.getString());

        return count(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getUserId, userId)
                .in(AppDataUploadEntity::getTestWandType, allowTestWandTypes)
                .in(AppDataUploadEntity::getWarning, COUNT_VALID_WARN_CODE)
                .eq(AppDataUploadEntity::getError, BluetoothDataErrorEnum.CODE_0.getValue()));
    }
}
