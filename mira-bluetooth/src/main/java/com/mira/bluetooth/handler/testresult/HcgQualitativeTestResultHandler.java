package com.mira.bluetooth.handler.testresult;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.handler.ITestResultHandler;
import com.mira.core.consts.enums.WandTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * HCG_Qualitative
 *
 * <AUTHOR>
 */
@Component
public class HcgQualitativeTestResultHandler implements ITestResultHandler<AppDataUploadEntity, HormoneDTO.TestResult> {
    @PostConstruct
    public void init() {
        TestResultHandler.set(WandTypeEnum.HCG_QUALITATIVE.getString(), this);
    }

    @Override
    public void handle(AppDataUploadEntity dataUploadEntity, HormoneDTO.TestResult testResult) {
        testResult.setWand_type(WandTypeEnum.HCG_QUALITATIVE.getInteger());
        testResult.setValue1(dataUploadEntity.getT3ConValue().floatValue());
    }
}
