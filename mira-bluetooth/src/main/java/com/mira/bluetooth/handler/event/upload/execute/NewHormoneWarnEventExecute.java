package com.mira.bluetooth.handler.event.upload.execute;

import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.bluetooth.enums.HormoneWarningEnum;
import com.mira.bluetooth.service.manager.CacheManager;
import com.mira.core.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * 处理新数据上传后的告警逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NewHormoneWarnEventExecute {
    @Resource
    private CacheManager cacheManager;

    @Resource
    private IMessageProvider messageProvider;

    /**
     * 处理新数据上传后的告警逻辑
     */
    public void process(Long userId, String timeZone, Integer warningNo) {
        if (HormoneWarningEnum.HCG_QUALITATIVE.getWarningNo() == warningNo) {
            Long notificationDefineId = NotificationDefineEnum.UNSUPPORTED_TEST_WAND.getDefineId();
            PushTokenDTO pushToken = cacheManager.getPushToken(userId);
            if (StringUtils.isBlank(pushToken.getPushToken())) {
                log.error("hcg qualitative warn, user:{} push token is empty", userId);
                return;
            }
            CompletableFuture.runAsync(() -> messageProvider.sendNotification(new PushNotificationDTO()
                    .setUserId(userId)
                    .setTimeZone(timeZone)
                    .setPlatform(pushToken.getPlatform())
                    .setDefineId(notificationDefineId)
                    .setPushFirebase(Boolean.TRUE)
                    .setSaveRecord(Boolean.TRUE)
                    .setTokens(Collections.singletonList(pushToken.getPushToken()))), ThreadPoolUtil.getPool());
        }
    }
}
