package com.mira.bluetooth.handler.event.upload;

import com.mira.bluetooth.async.KlaviyoProducer;
import com.mira.bluetooth.dto.algorithm.request.NewHormoneDataRequest;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.handler.event.dto.NewHormoneEventDTO;
import com.mira.bluetooth.handler.event.upload.execute.NewHormoneEventExecute;
import com.mira.bluetooth.handler.event.upload.execute.NewHormoneWarnEventExecute;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 上传事件监听器
 *
 * <AUTHOR>
 */
@Component
public class DataUploadListener {
    @Resource
    private KlaviyoProducer klaviyoProducer;
    @Resource
    private NewHormoneEventExecute newHormoneEventExecute;
    @Resource
    private NewHormoneWarnEventExecute newHormoneWarnEventExecute;

    /**
     * 处理新数据上传后的告警逻辑
     */
    @EventListener(NewHormoneWarningEvent.class)
    public void handleNewHormoneWarning(NewHormoneWarningEvent newHormoneWarningEvent) {
        Long userId = newHormoneWarningEvent.getUserId();
        String timeZone = newHormoneWarningEvent.getTimeZone();
        Integer warningNo = newHormoneWarningEvent.getWarningNo();
        newHormoneWarnEventExecute.process(userId, timeZone, warningNo);
    }

    /**
     * 处理新数据上传后的业务逻辑
     */
    @EventListener(NewHormoneEvent.class)
    public void handleNewHormone(NewHormoneEvent newHormoneEvent) {
        NewHormoneEventDTO newHormoneEventDTO = (NewHormoneEventDTO) newHormoneEvent.getSource();
        newHormoneEventExecute.process(newHormoneEventDTO);
    }

    /**
     * 处理hcg怀孕
     */
    @EventListener(HandlePregnantEvent.class)
    public void handlePregnant(HandlePregnantEvent handlePregnantEvent) {
        // TODO 暂时注释hcg怀孕
        //        NewHormoneDataRequest newHormoneDataRequest = handlePregnantEvent.getNewHormoneDataRequest();
        //        AppUserAlgorithmResultEntity userAlgorithmResult = handlePregnantEvent.getUserAlgorithmResultEntity();
        //
        //        List<HormoneDTO> hcgHormoneDTOList = newHormoneDataRequest.getHormone_data_new().stream()
        //                .filter(data -> (WandTypeEnum.HCG.getInteger() == data.getTest_results().getWand_type()
        //                        || WandTypeEnum.HCG_QUALITATIVE.getInteger() == data.getTest_results().getWand_type()))
        //                .collect(Collectors.toList());
        //        if (CollectionUtils.isNotEmpty(hcgHormoneDTOList)) {
        //            // hcg测试是否怀孕需要同步到diary页面
        //            HormoneDTO hcgHormoneDTO = hcgHormoneDTOList.get(hcgHormoneDTOList.size() - 1);
        //            AlgorithmReturnDTO.ExtraResult extraResult = JsonUtil.toObject(userAlgorithmResult.getExtraResult(),
        //                    AlgorithmReturnDTO.ExtraResult.class);
        //            List<Integer> hcgResultList = extraResult.getHcg_result();
        //
        //            CompletableFuture.runAsync(() -> userProvider.updatePregnantDiary(new UpdatePregnantDiaryDTO()
        //                    .setUserId(newHormoneDataRequest.getUser_id())
        //                    .setTimeZone(newHormoneDataRequest.getTimeZone())
        //                    .setHcgHormoneDTO(JsonUtil.toJson(hcgHormoneDTO))
        //                    .setHcgResult(hcgResultList.get(hcgResultList.size() - 1))), ThreadPoolUtil.getPool());
        //        }
    }

    /**
     * 同步klaviyo
     */
    @EventListener(KlaviyoSyncEvent.class)
    public void handleKlaviyoSnyc(KlaviyoSyncEvent klaviyoSyncEvent) {
        NewHormoneDataRequest newHormoneDataRequest = klaviyoSyncEvent.getNewHormoneDataRequest();
        AlgorithmReturnDTO algorithmReturnDataDTO = klaviyoSyncEvent.getAlgorithmReturnDataDTO();
        klaviyoProducer.testRecord(klaviyoSyncEvent.getUserId(), newHormoneDataRequest, algorithmReturnDataDTO);
    }
}
