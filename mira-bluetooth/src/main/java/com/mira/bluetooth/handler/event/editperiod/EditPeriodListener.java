package com.mira.bluetooth.handler.event.editperiod;

import com.mira.bluetooth.handler.event.editperiod.execute.EditPeriodEventExecute;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-06-19
 **/
@Slf4j
@Component
public class EditPeriodListener {
    @Resource
    private EditPeriodEventExecute editPeriodEventExecute;

    @EventListener(EditPeriodEvent.class)
    public void handleEditPeriod(EditPeriodEvent editPeriodEvent) {
        editPeriodEventExecute.process(editPeriodEvent);
    }
}
