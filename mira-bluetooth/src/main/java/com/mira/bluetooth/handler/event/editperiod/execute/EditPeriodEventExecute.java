package com.mira.bluetooth.handler.event.editperiod.execute;

import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.handler.event.editperiod.EditPeriodEvent;
import com.mira.bluetooth.service.manager.CycleIrregularManager;
import com.mira.core.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-06-19
 **/
@Slf4j
@Component
public class EditPeriodEventExecute {
    @Resource
    private CycleIrregularManager cycleIrregularManager;

    public void process(EditPeriodEvent editPeriodEvent) {
        Long userId = editPeriodEvent.getUserId();
        String timeZone = editPeriodEvent.getTimeZone();
        AlgorithmReturnDTO algorithmReturnDTO = editPeriodEvent.getAlgorithmReturnDataDTO();

        // Mark users who have irregular cycles
        CompletableFuture.runAsync(() -> cycleIrregularManager.execuse(userId, timeZone, algorithmReturnDTO),
                                 ThreadPoolUtil.getPool())
                         .exceptionally(ex -> {
                             log.error("checkIrregularCycles error，userId：{}", userId, ex);
                             return null;
                         });
    }


}
