package com.mira.bluetooth.handler.event.upload;

import org.springframework.context.ApplicationEvent;

/**
 * 处理新数据上传的告警逻辑
 *
 * <AUTHOR>
 */
public class NewHormoneWarningEvent extends ApplicationEvent {
    private final Long userId;
    private final String timeZone;
    private final Integer warningNo;

    public NewHormoneWarningEvent(Long userId, String timeZone, Integer warningNo) {
        super("");
        this.userId = userId;
        this.timeZone = timeZone;
        this.warningNo = warningNo;
    }

    public Long getUserId() {
        return userId;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public Integer getWarningNo() {
        return warningNo;
    }
}
