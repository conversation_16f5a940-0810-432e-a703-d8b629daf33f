package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.core.consts.enums.WandTypeEnum;

import java.util.List;

/**
 * 试剂检查工具
 *
 * <AUTHOR>
 */
public class CheckWandsUtil {
    /**
     * 校验历史测试数据中是否存在Max试剂
     *
     * @param historyHormoneList 激素数据列表
     * @return true/false
     */
    public static boolean containMaxWands(List<HormoneDTO> historyHormoneList) {
        return historyHormoneList.stream()
                .filter(hormoneDTO -> WandTypeEnum.LH_E3G_PDG.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                .anyMatch(hormoneDTO -> hormoneDTO.getFlag() == 1);
    }

    /**
     * 校验历史测试数据中是否存在FSH试剂
     *
     * @param historyHormoneList 激素数据列表
     * @return true/false
     */
    public static boolean containFshWands(List<HormoneDTO> historyHormoneList) {
        return historyHormoneList.stream()
                .filter(hormoneDTO -> WandTypeEnum.FSH.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                .anyMatch(hormoneDTO -> hormoneDTO.getFlag() == 1);
    }
}
