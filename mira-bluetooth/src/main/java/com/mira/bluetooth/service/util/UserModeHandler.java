package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.TestingScheduleDTO;
import com.mira.api.user.enums.RemindStatusConst;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.dto.algorithm.request.*;
import com.mira.bluetooth.enums.algorithm.AlgorithmUserTypeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.AgeUtil;
import com.mira.core.util.StringListUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户类型处理
 *
 * <AUTHOR>
 */
@Component
public class UserModeHandler {
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private IUserProvider userProvider;

    /**
     * 编辑经期
     */
    public void process(EditPeriodRequest editPeriodRequest,
                        UserPeriodParamDTO userPeriodParamDTO,
                        Integer trialFlag) {
        editPeriodRequest.setUser_mode(userPeriodParamDTO.getUserMode());

        // boolean containFshWands = CheckWandsUtil.containFshWands(hormoneDTOS);
        Integer avgLenCycle = userPeriodParamDTO.getAvgLenCycle();
        Integer cycleFlag = userPeriodParamDTO.getCycleFlag();
        Integer periodFlag = userPeriodParamDTO.getPeriodFlag();
        // -1 I don't know ；-2 varies often ；-3  I don't know + varies often
        // 当前版本0，1交给算法自己判断
        Set<Integer> userTypes = buildUserTypesSet(cycleFlag, periodFlag);

        if (cycleFlag == -1 || cycleFlag == -3) {
            editPeriodRequest.setLen_cycle_0(null);
        } else {
            editPeriodRequest.setLen_cycle_0(avgLenCycle);
        }
        editPeriodRequest.setUser_type(new ArrayList<>(userTypes));

        editPeriodRequest.setAge(userPeriodParamDTO.getAge());
        Integer trackingMenopause = userPeriodParamDTO.getTrackingMenopause();
        editPeriodRequest.setTracking_menopause(trackingMenopause);
        editPeriodRequest.setTracking_menopause_date(userPeriodParamDTO.getTrackingMenopauseDate());

        List<Integer> user_conditions = StringListUtil.strToIntegerList(userPeriodParamDTO.getConditions(), ",");
        editPeriodRequest.setUser_conditions(user_conditions);
        Set<Integer> productFlags = this.buildProductFlags(editPeriodRequest.getUser_id(), trialFlag,
                trackingMenopause);
        editPeriodRequest.setProduct_flags(new ArrayList<>(productFlags));
    }

    private Set<Integer> buildProductFlags(Long userId, Integer trialFlag, Integer trackingMenopause) {
        Set<Integer> productFlags = new LinkedHashSet<>();
        if (trialFlag != null) {
            productFlags.add(trialFlag);
        }
        TestingScheduleDTO testingScheduleDTO = userProvider.getTestingSchedule(userId).getData();
        if (testingScheduleDTO == null) {
            testingScheduleDTO = new TestingScheduleDTO();
        }

        if (!productFlags.contains(WandTypeEnum.FSH.getInteger())) {
            if (RemindStatusConst.SCHEDULE_ON.equals(testingScheduleDTO.getOvum()) || (trackingMenopause != null && trackingMenopause == 1)) {
                productFlags.add(WandTypeEnum.FSH.getInteger());
            }
        }
        if (!productFlags.contains(WandTypeEnum.LH_E3G_PDG.getInteger())
                && RemindStatusConst.SCHEDULE_ON.equals(testingScheduleDTO.getMax())) {
            productFlags.add(WandTypeEnum.LH_E3G_PDG.getInteger());
        }
        if (!productFlags.contains(WandTypeEnum.PDG.getInteger())
                && RemindStatusConst.SCHEDULE_ON.equals(testingScheduleDTO.getConfirm())) {
            productFlags.add(WandTypeEnum.PDG.getInteger());
        }
        if (!productFlags.contains(WandTypeEnum.E3G_LH.getInteger())
                && RemindStatusConst.SCHEDULE_ON.equals(testingScheduleDTO.getPlus())) {
            productFlags.add(WandTypeEnum.E3G_LH.getInteger());
        }
        return productFlags;
    }

    // ==========================================================================================

    /**
     * 算法tips
     */
    public void process(TipsRequest tipsRequest, UserPeriodParamDTO userPeriodParamDTO, Integer trialFlag) {
        tipsRequest.setUser_mode(userPeriodParamDTO.getUserMode());

        Integer avgLenCycle = userPeriodParamDTO.getAvgLenCycle();
        Integer cycleFlag = userPeriodParamDTO.getCycleFlag();
        Integer periodFlag = userPeriodParamDTO.getPeriodFlag();
        // -1 I don't know ；-2 varies often ；-3  I don't know + varies often
        // 用户类型，可为空，默认0；0:正常用户;1:新用户; 2:没有经期的用户;3.经期变动频繁的用户;4.没有周期的用户;5.周期变动频繁的用户
        // 当前版本0，1交给算法自己判断
        Set<Integer> userTypes = buildUserTypesSet(cycleFlag, periodFlag);

        if (cycleFlag == -1 || cycleFlag == -3) {
            tipsRequest.setLen_cycle_0(null);
        } else {
            tipsRequest.setLen_cycle_0(avgLenCycle);
        }
        tipsRequest.setUser_type(new ArrayList<>(userTypes));

        tipsRequest.setAge(userPeriodParamDTO.getAge());
        Integer trackingMenopause = userPeriodParamDTO.getTrackingMenopause();
        tipsRequest.setTracking_menopause(trackingMenopause);
        tipsRequest.setTracking_menopause_date(userPeriodParamDTO.getTrackingMenopauseDate());

        List<Integer> user_conditions = StringListUtil.strToIntegerList(userPeriodParamDTO.getConditions(), ",");
        tipsRequest.setUser_conditions(user_conditions);
        Set<Integer> productFlags = this.buildProductFlags(tipsRequest.getUser_id(), trialFlag, trackingMenopause);
        tipsRequest.setProduct_flags(new ArrayList<>(productFlags));
    }

    // ==========================================================================================

    /**
     * 新数据上传
     */
    public void process(NewHormoneDataRequest newHormoneDataRequest, Integer trialFlag) {
        Long userId = newHormoneDataRequest.getUser_id();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer avgLenCycle = loginUserInfoDTO.getAvgLenCycle();
        Integer cycleFlag = loginUserInfoDTO.getCycleFlag();
        Integer periodFlag = loginUserInfoDTO.getPeriodFlag();
        newHormoneDataRequest.setAge(AgeUtil.calculateAge(loginUserInfoDTO.getBirthYear(), loginUserInfoDTO.getBirthMonth(), loginUserInfoDTO.getBirthDay()));
        // -1 I don't know ；-2 varies often ；-3  I don't know + varies often
        // 用户类型，可为空，默认0；0:正常用户;1:新用户; 2:没有经期的用户;3.经期变动频繁的用户;4.没有周期的用户;5.周期变动频繁的用户
        // 当前版本0，1交给算法自己判断
        Set<Integer> userTypes = buildUserTypesSet(cycleFlag, periodFlag);

        if (cycleFlag == -1 || cycleFlag == -3) {
            newHormoneDataRequest.setLen_cycle_0(null);
        } else {
            newHormoneDataRequest.setLen_cycle_0(avgLenCycle);
        }
        newHormoneDataRequest.setUser_type(new ArrayList<>(userTypes));

        Integer ttaSwitch = loginUserInfoDTO.getTtaSwitch();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        newHormoneDataRequest.setUser_mode(UserGoalEnum.getUserMode(ttaSwitch, goalStatus));
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        newHormoneDataRequest.setTracking_menopause(trackingMenopause);
        newHormoneDataRequest.setTracking_menopause_date(loginUserInfoDTO.getTrackingMenopauseDate());

        List<Integer> user_conditions = StringListUtil.strToIntegerList(loginUserInfoDTO.getConditions(), ",");
        newHormoneDataRequest.setUser_conditions(user_conditions);
        Set<Integer> productFlags = this.buildProductFlags(newHormoneDataRequest.getUser_id(), trialFlag, trackingMenopause);
        newHormoneDataRequest.setProduct_flags(new ArrayList<>(productFlags));
    }

    // ==========================================================================================

    /**
     * 长周期
     */
    public void process(LongerPeriodRequest longerPeriodRequest, Integer cycleFlag,
                        Integer periodFlag, Integer userMode) {
        // -1 I don't know ；-2 varies often ；-3  I don't know + varies often
        // 用户类型，可为空，默认0；0:正常用户;1:新用户; 2:没有经期的用户;3.经期变动频繁的用户;4.没有周期的用户;5.周期变动频繁的用户
        // 当前版本0，1交给算法自己判断
        Set<Integer> userTypes = buildUserTypesSet(cycleFlag, periodFlag);
        longerPeriodRequest.setUser_type(new ArrayList<>(userTypes));
        longerPeriodRequest.setUser_mode(userMode);
    }

    // ==========================================================================================

    /**
     * report
     */
    public void process(ReportRequest reportRequest, UserPeriodParamDTO userPeriodParamDTO) {
        Integer avgLenCycle = userPeriodParamDTO.getAvgLenCycle();
        Integer cycleFlag = userPeriodParamDTO.getCycleFlag();
        Integer periodFlag = userPeriodParamDTO.getPeriodFlag();
        // -1 I don't know ；-2 varies often ；-3  I don't know + varies often
        // 用户类型，可为空，默认0；0:正常用户;1:新用户; 2:没有经期的用户;3.经期变动频繁的用户;4.没有周期的用户;5.周期变动频繁的用户
        // 当前版本0，1交给算法自己判断
        Set<Integer> userTypes = buildUserTypesSet(cycleFlag, periodFlag);
        if (cycleFlag == -1 || cycleFlag == -3) {
            reportRequest.setLen_cycle_0(null);
        } else {
            reportRequest.setLen_cycle_0(avgLenCycle);
        }
        reportRequest.setUser_type(new ArrayList<>(userTypes));
        reportRequest.setAge(userPeriodParamDTO.getAge());
        reportRequest.setUser_mode(userPeriodParamDTO.getUserMode());

        reportRequest.setTracking_menopause(userPeriodParamDTO.getTrackingMenopause());
        reportRequest.setTracking_menopause_date(userPeriodParamDTO.getTrackingMenopauseDate());

        List<Integer> user_conditions = StringListUtil.strToIntegerList(userPeriodParamDTO.getConditions(), ",");
        reportRequest.setUser_conditions(user_conditions);
    }

    private Set<Integer> buildUserTypesSet(Integer cycleFlag, Integer periodFlag) {
        Set<Integer> userTypes = new LinkedHashSet<>();

        if (periodFlag == -1) {
            userTypes.add(AlgorithmUserTypeEnum.NO_PERIOD_USER.getType());
        }
        switch (cycleFlag) {
            case -1:
                userTypes.add(AlgorithmUserTypeEnum.NO_CYCLE_USER.getType());
                break;
            case -2:
                userTypes.add(AlgorithmUserTypeEnum.VARIES_OFTEN_CYCLE_USER.getType());
                break;
            case -3:
                userTypes.add(AlgorithmUserTypeEnum.NO_CYCLE_USER.getType());
                userTypes.add(AlgorithmUserTypeEnum.VARIES_OFTEN_CYCLE_USER.getType());
                break;
            default:
                break;
        }

        return userTypes;
    }
}
