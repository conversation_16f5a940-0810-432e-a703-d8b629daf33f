package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.consts.DefaultWandsParamConst;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.NumberFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 试剂数据前端显示处理
 */
@Slf4j
@Component
public class WandsParamDataVOHelper {
    @Resource
    private WandsParamRecordHelper wandsParamRecordHelper;

    /**
     * 转换成前端展示值
     */
    public String buildTestValueResult(WandTypeEnum wandTypeEnum, Float value1, Float value2, Float value3, String wandBatch3) {
        String uStripType = wandTypeEnum.getString();
        WandsParamRecordDTO wandsParamCache = wandsParamRecordHelper.getByWandBatch3(wandBatch3, uStripType);
        Float lhLowerLimit = DefaultWandsParamConst.LH_LOWER_LIMIT;
        Float lhUpperLimit = DefaultWandsParamConst.LH_UPPER_LIMIT;
        Float e3gLowerLimit = DefaultWandsParamConst.E3G_LOWER_LIMIT;
        Float e3gUpperLimit = DefaultWandsParamConst.E3G_UPPER_LIMIT;
        Float hcgLowerLimit = DefaultWandsParamConst.HCG_LOWER_LIMIT;
        Float hcgUpperLimit = DefaultWandsParamConst.HCG_UPPER_LIMIT;
        Float pdgLowerLimit = DefaultWandsParamConst.PDG_LOWER_LIMIT;
        Float pdgUpperLimit = DefaultWandsParamConst.PDG_UPPER_LIMIT;
        Float e3gHighRangeLowerLimit = DefaultWandsParamConst.E3G_HIGH_RANGE_LOWER_LIMIT;
        Float e3gHighRangeUpperLimit = DefaultWandsParamConst.E3G_HIGH_RANGE_UPPER_LIMIT;

        Float lhe3gpdg_e3g_LowerLimit = DefaultWandsParamConst.LHE3GPDG_E3G_LOWER_LIMIT;
        Float lhe3gpdg_e3g_UpperLimit = DefaultWandsParamConst.LHE3GPDG_E3G_UPPER_LIMIT;
        Float lhe3gpdg_lh_LowerLimit = DefaultWandsParamConst.LHE3GPDG_LH_LOWER_LIMIT;
        Float lhe3gpdg_lh_UpperLimit = DefaultWandsParamConst.LHE3GPDG_LH_UPPER_LIMIT;
        Float lhe3gpdg_pdg_LowerLimit = DefaultWandsParamConst.LHE3GPDG_PDG_LOWER_LIMIT;
        Float lhe3gpdg_pdg_UpperLimit = DefaultWandsParamConst.LHE3GPDG_PDG_UPPER_LIMIT;

        Float hcg_qualitative_lower_limit = DefaultWandsParamConst.HCG_QUALITATIVE_LOWER_LIMIT;
        Float hcg_qualitative_upper_limit = DefaultWandsParamConst.HCG_QUALITATIVE_UPPER_LIMIT;

        Float fsh_lower_limit = DefaultWandsParamConst.FSH_LOWER_LIMIT;
        Float fsh_upper_limit = DefaultWandsParamConst.FSH_UPPER_LIMIT;

        if (ObjectUtils.isNotEmpty(wandsParamCache)) {
            if (uStripType.equals(WandTypeEnum.LH.getString())) {
                lhLowerLimit = wandsParamCache.getFLowerLimit2();
                lhUpperLimit = wandsParamCache.getFUpperLimit2();
            } else if (uStripType.equals(WandTypeEnum.E3G_LH.getString())) {
                lhLowerLimit = wandsParamCache.getFLowerLimit3();
                lhUpperLimit = wandsParamCache.getFUpperLimit3();
                e3gLowerLimit = wandsParamCache.getFLowerLimit2();
                e3gUpperLimit = wandsParamCache.getFUpperLimit2();
            } else if (uStripType.equals(WandTypeEnum.HCG.getString())) {
                hcgUpperLimit = wandsParamCache.getFUpperLimit2();
                hcgLowerLimit = wandsParamCache.getFLowerLimit3();
            } else if (uStripType.equals(WandTypeEnum.E3G_HIGH_RANGE.getString())) {
                e3gHighRangeLowerLimit = wandsParamCache.getFLowerLimit2();
                e3gHighRangeUpperLimit = wandsParamCache.getFUpperLimit2();
            } else if (uStripType.equals(WandTypeEnum.PDG.getString())) {
                pdgLowerLimit = wandsParamCache.getFLowerLimit2();
                pdgUpperLimit = wandsParamCache.getFUpperLimit2();
            } else if (uStripType.equals(WandTypeEnum.LH_E3G_PDG.getString())) {
                lhe3gpdg_e3g_LowerLimit = wandsParamCache.getFLowerLimit1();
                lhe3gpdg_e3g_UpperLimit = wandsParamCache.getFUpperLimit1();
                lhe3gpdg_lh_LowerLimit = wandsParamCache.getFLowerLimit2();
                lhe3gpdg_lh_UpperLimit = wandsParamCache.getFUpperLimit2();
                lhe3gpdg_pdg_LowerLimit = wandsParamCache.getFLowerLimit3();
                lhe3gpdg_pdg_UpperLimit = wandsParamCache.getFUpperLimit3();
            }
        }
        String valueResult = "";

        if (WandTypeEnum.HCG.getString().equals(uStripType)) {
            if (hcgLowerLimit >= value1) {
                valueResult = "< " + NumberFormatUtil.format(hcgLowerLimit) + " ";
            } else if (hcgUpperLimit <= value1) {
                valueResult = "> " + NumberFormatUtil.format(hcgUpperLimit) + " ";
            } else {
                valueResult = NumberFormatUtil.format(value1) + " ";
            }
        } else if (WandTypeEnum.E3G_LH.getString().equals(uStripType)) {
            if (value1 == null) {
                //LH
                if (lhLowerLimit >= value2) {
                    valueResult = "< " + NumberFormatUtil.format(lhLowerLimit) + " ";
                } else if (lhUpperLimit <= value2) {
                    valueResult = "> " + NumberFormatUtil.format(lhUpperLimit) + " ";
                } else {
                    valueResult = NumberFormatUtil.format(value2) + " ";
                }
            } else {
                if (e3gLowerLimit >= value1) {
                    valueResult = "< " + NumberFormatUtil.format(e3gLowerLimit) + " ";
                } else if (e3gUpperLimit <= value1) {
                    valueResult = "> " + NumberFormatUtil.format(e3gUpperLimit) + " ";
                } else {
                    valueResult = NumberFormatUtil.format(value1) + " ";
                }
            }
        } else if (WandTypeEnum.LH.getString().equals(uStripType)) {
            if (lhLowerLimit >= value1) {
                valueResult = "< " + NumberFormatUtil.format(lhLowerLimit) + " ";
            } else if (lhUpperLimit <= value1) {
                valueResult = "> " + NumberFormatUtil.format(lhUpperLimit) + " ";
            } else {
                valueResult = NumberFormatUtil.format(value1) + " ";
            }
        } else if (WandTypeEnum.PDG.getString().equals(uStripType)) {
            if (pdgLowerLimit >= value1) {
                valueResult = "< " + NumberFormatUtil.format(pdgLowerLimit) + " ";
            } else if (pdgUpperLimit <= value1) {
                valueResult = "> " + NumberFormatUtil.format(pdgUpperLimit) + " ";
            } else {
                valueResult = NumberFormatUtil.format(value1) + " ";
            }
        } else if (WandTypeEnum.E3G_HIGH_RANGE.getString().equals(uStripType)) {
            if (e3gHighRangeLowerLimit >= value1) {
                valueResult = "< " + NumberFormatUtil.format(e3gHighRangeLowerLimit) + " ";
            } else if (e3gHighRangeUpperLimit <= value1) {
                valueResult = "> " + NumberFormatUtil.format(e3gHighRangeUpperLimit) + " ";
            } else {
                valueResult = NumberFormatUtil.format(value1) + " ";
            }
        } else if (WandTypeEnum.LH_E3G_PDG.getString().equals(uStripType)) {
            if (value1 != null) {
                if (lhe3gpdg_lh_LowerLimit >= value1) {
                    valueResult = "< " + NumberFormatUtil.format(lhe3gpdg_lh_LowerLimit) + " ";
                } else if (lhe3gpdg_lh_UpperLimit <= value1) {
                    valueResult = "> " + NumberFormatUtil.format(lhe3gpdg_lh_UpperLimit) + " ";
                } else {
                    valueResult = NumberFormatUtil.format(value1) + " ";
                }

            } else if (value2 != null) {
                if (lhe3gpdg_pdg_LowerLimit >= value2) {
                    valueResult = "< " + NumberFormatUtil.format(lhe3gpdg_pdg_LowerLimit) + " ";
                } else if (lhe3gpdg_pdg_UpperLimit <= value2) {
                    valueResult = "> " + NumberFormatUtil.format(lhe3gpdg_pdg_UpperLimit) + " ";
                } else {
                    valueResult = NumberFormatUtil.format(value2) + " ";
                }
            } else if (value3 != null) {
                if (lhe3gpdg_e3g_LowerLimit >= value3) {
                    valueResult = "< " + NumberFormatUtil.format(lhe3gpdg_e3g_LowerLimit) + " ";
                } else if (e3gHighRangeUpperLimit <= value3) {
                    valueResult = "> " + NumberFormatUtil.format(lhe3gpdg_e3g_UpperLimit) + " ";
                } else {
                    valueResult = NumberFormatUtil.format(value3) + " ";
                }
            }
        } else if (WandTypeEnum.HCG_QUALITATIVE.getString().equals(uStripType)) {
            if (hcg_qualitative_lower_limit >= value1) {
                valueResult = "< " + NumberFormatUtil.format(hcg_qualitative_lower_limit) + " ";
            } else if (hcg_qualitative_upper_limit <= value1) {
                valueResult = "> " + NumberFormatUtil.format(hcg_qualitative_upper_limit) + " ";
            } else {
                valueResult = NumberFormatUtil.format(value1) + " ";
            }
        } else if (WandTypeEnum.FSH.getString().equals(uStripType)) {
            if (fsh_lower_limit >= value1) {
                valueResult = "< " + NumberFormatUtil.format(fsh_lower_limit) + " ";
            } else if (fsh_upper_limit <= value1) {
                valueResult = "> " + NumberFormatUtil.format(fsh_upper_limit) + " ";
            } else {
                valueResult = NumberFormatUtil.format(value1) + " ";
            }
        }
        return valueResult;
    }
}
