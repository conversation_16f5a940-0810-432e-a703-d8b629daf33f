package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.consts.ResultVersionConst;
import com.mira.api.bluetooth.enums.BluetoothDataErrorEnum;
import com.mira.api.bluetooth.enums.BluetoothDataWarningEnum;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.dto.BluetoothDataDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.ZoneDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * 解析蓝牙数据工具类
 */
@Slf4j
public class BluetoothDataUtil {
    /**
     * 从蓝牙数据中解析DataEntity对象
     *
     * @param inBluetoothData 蓝牙数据
     * @return AppDataUploadEntity
     */
    public static AppDataUploadEntity parseDataEntity(BluetoothDataDTO inBluetoothData, Long userId, String timeZone) {
        AppDataUploadEntity dataUploadEntity = new AppDataUploadEntity();

        String data = inBluetoothData.getBluetoothData();
        // 完成时间
        long completeTime = Long.parseLong(data.substring(56, 64), 16) * 1000;
        String completeTimeStr = ZoneDateUtil.format(timeZone, completeTime, DatePatternConst.DATE_TIME_PATTERN);
        dataUploadEntity.setCompleteTime(completeTimeStr);
        dataUploadEntity.setCompleteTimestamp(completeTime);
        // 首次插入时间
        long firstTimeStamp = Long.parseLong(data.substring(48, 56), 16) * 1000;
        String firstTimeStampStr = ZoneDateUtil.format(timeZone, firstTimeStamp, DatePatternConst.DATE_TIME_PATTERN);
        dataUploadEntity.setFirstTimeStamp(firstTimeStampStr);
        // 试剂类型
        String testWandTypeIn = data.substring(64, 66);
        String testWandType = String.valueOf(Integer.parseInt(testWandTypeIn, 16));
        if (testWandType.length() == 1) {
            testWandType = "0" + testWandType;
        }
        dataUploadEntity.setTestWandType(testWandType);
        // 结果版本为03时，解析t1线第5个float的值
        String resultVersionFormat = data.substring(46, 48);
        String error = data.substring(392, 394);
        String warning = data.substring(394, 396);
        // 电池百分比
        double batteryPercentage = convert(decodeHEX(data.substring(388, 392)));

        dataUploadEntity.setUserId(userId);
        dataUploadEntity.setSn(getSn(data.substring(16, 32)));
        String firmwareVersion = data.substring(32, 40);
        dataUploadEntity.setFirmwareVersion(firmwareVersion);
        dataUploadEntity.setUpdateServerFlag(0);
        dataUploadEntity.setAnalyzerNumber(decodeHEX(data.substring(42, 46)));
        dataUploadEntity.setResultVersionFormat(data.substring(46, 48));

        dataUploadEntity.setTestWandBatch(getTestWandBatch(data.substring(66, 74)));
        dataUploadEntity.setBatchInNumber(String.valueOf(Long.parseLong(data.substring(74, 82), 16)));
        dataUploadEntity.setBackGroudBeforC(data.substring(378, 382));
        dataUploadEntity.setBackGroudEndT(data.substring(382, 386));
        dataUploadEntity.setBatteryPercentage(batteryPercentage);
        dataUploadEntity.setMCUTemperature((int) Long.parseLong(data.substring(386, 388), 16));
        dataUploadEntity.setError(error);
        dataUploadEntity.setWarning(warning);
        dataUploadEntity.setAutoFlag(0);

        // 解析t1
        analysisT1(data, resultVersionFormat, dataUploadEntity);
        // 解析t2
        analysisT2(data, resultVersionFormat, dataUploadEntity);
        // 解析t3
        analysisT3(data, resultVersionFormat, dataUploadEntity);
        // t4预留
        // analysisT4

        // t1 t2 t3 t4线和c1线的相对峰高
        relativelyPeakHeight(data, resultVersionFormat, firmwareVersion, dataUploadEntity);

        // 解析c1
        analysisC1(data, dataUploadEntity);
        // 解析c2
        analysisC2(data, dataUploadEntity);
        // 解析c3，测量环境值（白域均值，pd温度）
        analysisC3(data, error, dataUploadEntity);

        // 蓝牙原始数据
        dataUploadEntity.setRawResultData(data);
        // 数据是否有效
        if (BluetoothDataErrorEnum.CODE_0.getValue().equalsIgnoreCase(error)
                && BluetoothDataWarningEnum.CODE_0.getValue().equalsIgnoreCase(warning)) {
            dataUploadEntity.setAvlidFlage(1);
        } else {
            dataUploadEntity.setAvlidFlage(0);
        }

        log.info("finished time:{}, wand type:{}, t1ConValue:{}, t2ConValue:{}, t3ConValue:{}, errorCode:{}, warningCode:{}", completeTimeStr, dataUploadEntity.getTestWandType(),
                dataUploadEntity.getT1ConValue(), dataUploadEntity.getT2ConValue(), dataUploadEntity.getT3ConValue(), dataUploadEntity.getError(), dataUploadEntity.getWarning());
        return dataUploadEntity;
    }

    /**
     * 获取sn号
     */
    public static String getSn(String sn) {
        if (StringUtils.isNotBlank(sn)) {
            return ("E3" + StringUtils.substring(sn, -8)).toUpperCase();
        }
        return null;
    }

    /**
     * 解析T1 t2 t3公共方法
     */
    public static Map<String, Object> getTLineInfo(String tLine) {
        try {
            //43d10000 423deb96 42ca1c34 41a66489 00000201
            if (StringUtils.isNotBlank(tLine) && tLine.length() >= 32) {
                Map<String, Object> map = new HashMap<>();
                //峰位置
                map.put("peakHeightPosition", getParseValue(tLine.substring(0, 8)));
                //绝对峰高
                map.put("absolutePeakHeight", getParseValue(tLine.substring(8, 16)));
                //峰面积
                map.put("peakArea", getParseValue(tLine.substring(16, 24)));
                //浓度值T1_con
                map.put("conValue", getParseValue(tLine.substring(24, 32)));

                //第5个float
                String str = tLine.substring(32, 40);
                map.put("fifthFloatStr", str);

                return map;
            }
        } catch (Exception e) {
            log.error("getTLineInfo error", e);
        }
        return null;
    }

    /**
     * 从t5获取相对峰高（分别是C1,T1,T2,T3,T4 相对峰高）
     */
    public static Map<String, BigDecimal> getRelativePeakHeightByT5(String t5Line) {
        try {
            if (StringUtils.isNotBlank(t5Line) && t5Line.length() >= 32) {
                Map<String, BigDecimal> map = new HashMap<>();
                //c1 相对峰高
                map.put("c1RelativeFenggao", getParseValue(t5Line.substring(0, 8)));
                //t1 相对峰高
                map.put("t1RelativeFenggao", getParseValue(t5Line.substring(8, 16)));
                //t2 相对峰高
                map.put("t2RelativeFenggao", getParseValue(t5Line.substring(16, 24)));
                //t3 相对峰高
                map.put("t3RelativeFenggao", getParseValue(t5Line.substring(24, 32)));
                //t4 相对峰高
                map.put("t4RelativeFenggao", getParseValue(t5Line.substring(32, 40)));
                return map;
            }
        } catch (Exception e) {
            log.error("getRelativePeakHeightByT5 error", e);
        }
        return null;
    }

    /**
     * 解析C1  C2  c3线获取信息公共方法
     */
    public static Map<String, Object> getCLineInfo(String cLine) {
        try {
            //433d0000 433d0344 4390474e 3f 80 00 00
            if (StringUtils.isNotBlank(cLine) && cLine.length() >= 32) {
                Map<String, Object> map = new HashMap<>();
                //峰高位置 或白域均值
                map.put("peakHeightPosition", getParseValue(cLine.substring(0, 8)));
                //绝对峰高 或PD温度
                map.put("absolutePeakHeight", getParseValue(cLine.substring(8, 16)));
                //峰面积
                map.put("peakArea", getParseValue(cLine.substring(16, 24)));
                //第四个float 第三个1byte
                int wandValid = Integer.parseInt(cLine.substring(28, 30));
                map.put("wandValid", wandValid);
                return map;
            }
        } catch (Exception e) {
            log.error("getCLineInfo error", e);
        }
        return null;
    }

    /**
     * 将16进制的字符串转10进制的数字
     */
    public static int decodeHEX(String hexs) {
        BigInteger bigint = new BigInteger(hexs, 16);
        int numb = bigint.intValue();
        return numb;
    }

    /**
     * 试剂类型
     */
    public static String getTestWandBatch(String wandBatch3) {
        if (StringUtils.isBlank(wandBatch3)) {
            return null;
        }
        int year = Integer.parseInt(wandBatch3.substring(0, 2), 16);
        int month = Integer.parseInt(wandBatch3.substring(2, 4), 16);
        int code = Integer.parseInt(wandBatch3.substring(4), 16);
        String wandBatch = "20" + (year < 10 ? "0" + year : year) + (month < 10 ? "0" + month : month);

        if (code < 10) {
            wandBatch += "000" + code;
        } else if (code >= 10 && code < 100) {
            wandBatch += "00" + code;
        } else if (code >= 100 && code < 1000) {
            wandBatch += "0" + code;
        } else if (code >= 1000) {
            wandBatch += code;
        }

        return wandBatch;
    }

    /**
     * 获取解析的值
     *
     * @param str
     * @return
     */
    public static BigDecimal getParseValue(String str) {
        try {
            DecimalFormat df = new DecimalFormat("0.000");
            //峰位置
            Long sub = Long.parseLong(str, 16);
            Float f = Float.intBitsToFloat(sub.intValue());
            BigDecimal bd = new BigDecimal(new Double(df.format(f.doubleValue()))).setScale(2, BigDecimal.ROUND_HALF_UP);
            return bd;
        } catch (Exception e) {
            log.error("getParseValue error", e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据电压算出电量
     *
     * @param batteryVoltage
     * @return
     */
    public static double convert(int batteryVoltage) {
        if (batteryVoltage <= 3412) {
            return 0.0;
        }
        if (batteryVoltage > 4126) {
            return 1.0;
        }
        double a2 = -4.1211e-7;
        double a1 = 4.5045e-3;
        double a0 = -10.571;
        return a2 * batteryVoltage * batteryVoltage + a1 * batteryVoltage + a0;
    }

    public static void analysisT1(String data, String resultVersionFormat, AppDataUploadEntity dataUploadEntity) {
        Map<String, Object> t1Info = getTLineInfo(data.substring(82, 122));
        if (!CollectionUtils.isEmpty(t1Info)) {
            // t1峰位置
            BigDecimal t1PeakHeightPosition = new BigDecimal(t1Info.get("peakHeightPosition").toString());
            // 绝对峰高
            BigDecimal t1AbsolutePeakHeight = new BigDecimal(t1Info.get("absolutePeakHeight").toString());
            // 峰面积
            BigDecimal t1PeakArea = new BigDecimal(t1Info.get("peakArea").toString());
            // 浓度
            BigDecimal t1ConValue = new BigDecimal(t1Info.get("conValue").toString());

            dataUploadEntity.setT1PeakHeightPosition(t1PeakHeightPosition);
            dataUploadEntity.setT1AbsolutePeakHeight(t1AbsolutePeakHeight);
            dataUploadEntity.setT1PeakArea(t1PeakArea);
            dataUploadEntity.setT1ConValue(t1ConValue);

            // 第五个float
            String str = t1Info.get("fifthFloatStr").toString();
            // 根据结果版本解析t1线的第五个flaot
            if (ResultVersionConst.RESULTVERSIONFORMAT_01.equals(resultVersionFormat)) {
                // 结果版本01 解析定性判断值
                if ("00000000".equalsIgnoreCase(str)) {
                    // lh为none
                    dataUploadEntity.setT1Level("00");
                } else if ("3f800000".equalsIgnoreCase(str)) {
                    // low
                    dataUploadEntity.setT1Level("01");
                } else if ("40000000".equalsIgnoreCase(str)) {
                    // medium
                    dataUploadEntity.setT1Level("02");
                } else if ("40400000".equalsIgnoreCase(str)) {
                    // high
                    dataUploadEntity.setT1Level("03");
                }

            } else if (ResultVersionConst.RESULTVERSIONFORMAT_02.equals(resultVersionFormat)) {
                // 结果版本02
                // do something

            } else if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
                // 00 00 02 01
                String t1WarningCode = str.substring(0, 2);
                String t1ErrorCode = str.substring(2, 4);
                String t1Level = str.substring(4, 6);
                String t1WandTypeStr = str.substring(6, 8);
                dataUploadEntity.setT1WarningCode(t1WarningCode);
                dataUploadEntity.setT1ErrorCode(t1ErrorCode);
                dataUploadEntity.setT1Level(t1Level);

                String t1WandType = String.valueOf(Integer.parseInt(t1WandTypeStr, 16));
                if (t1WandType.length() == 1) {
                    t1WandType = "0" + t1WandType;
                }
                dataUploadEntity.setT1WandType(t1WandType);
            }

        }
    }

    public static void analysisT2(String data, String resultVersionFormat, AppDataUploadEntity dataUploadEntity) {
        Map<String, Object> t2Info = getTLineInfo(data.substring(122, 162));
        if (!CollectionUtils.isEmpty(t2Info)) {
            // t2峰位置
            BigDecimal t2PeakHeightPosition = new BigDecimal(t2Info.get("peakHeightPosition").toString());
            // t2绝对峰高
            BigDecimal t2AbsolutePeakHeight = new BigDecimal(t2Info.get("absolutePeakHeight").toString());
            // t2峰面积
            BigDecimal t2PeakArea = new BigDecimal(t2Info.get("peakArea").toString());
            // t2浓度
            BigDecimal t2ConValue = new BigDecimal(t2Info.get("conValue").toString());

            dataUploadEntity.setT2PeakHeightPosition(t2PeakHeightPosition);
            dataUploadEntity.setT2AbsolutePeakHeight(t2AbsolutePeakHeight);
            dataUploadEntity.setT2PeakArea(t2PeakArea);
            dataUploadEntity.setT2ConValue(t2ConValue);

            // t2线第五个float
            String str = t2Info.get("fifthFloatStr").toString();
            if (ResultVersionConst.RESULTVERSIONFORMAT_01.equals(resultVersionFormat)) {
                // 结果版本为01 不关心t2

            } else if (ResultVersionConst.RESULTVERSIONFORMAT_02.equals(resultVersionFormat)) {

            } else if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
                String t2WarningCode = str.substring(0, 2);
                String t2ErrorCode = str.substring(2, 4);
                String t2Level = str.substring(4, 6);
                String t2WandTypeStr = str.substring(6, 8);
                dataUploadEntity.setT2WarningCode(t2WarningCode);
                dataUploadEntity.setT2ErrorCode(t2ErrorCode);
                dataUploadEntity.setT2Level(t2Level);

                String t2WandType = String.valueOf(Integer.parseInt(t2WandTypeStr, 16));
                if (t2WandType.length() == 1) {
                    t2WandType = "0" + t2WandType;
                }
                dataUploadEntity.setT2WandType(t2WandType);
            }
        }
    }

    public static void analysisT3(String data, String resultVersionFormat, AppDataUploadEntity dataUploadEntity) {
        Map<String, Object> t3Info = getTLineInfo(data.substring(162, 202));
        if (!CollectionUtils.isEmpty(t3Info)) {
            // t3峰位置
            BigDecimal t3PeakHeightPosition = new BigDecimal(t3Info.get("peakHeightPosition").toString());
            // t3绝对峰高
            BigDecimal t3AbsolutePeakHeight = new BigDecimal(t3Info.get("absolutePeakHeight").toString());
            // t3峰面积
            BigDecimal t3PeakArea = new BigDecimal(t3Info.get("peakArea").toString());
            // t3浓度
            BigDecimal t3ConValue = new BigDecimal(t3Info.get("conValue").toString());

            dataUploadEntity.setT3PeakHeightPosition(t3PeakHeightPosition);
            dataUploadEntity.setT3AbsolutePeakHeight(t3AbsolutePeakHeight);
            dataUploadEntity.setT3PeakArea(t3PeakArea);
            dataUploadEntity.setT3ConValue(t3ConValue);

            // t3线第五个float
            String str = t3Info.get("fifthFloatStr").toString();
            if (ResultVersionConst.RESULTVERSIONFORMAT_01.equals(resultVersionFormat)) {
                // 结果版本为01 不关心t3

            } else if (ResultVersionConst.RESULTVERSIONFORMAT_02.equals(resultVersionFormat)) {

            } else if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
                String t3WarningCode = str.substring(0, 2);
                String t3ErrorCode = str.substring(2, 4);
                String t3Level = str.substring(4, 6);
                dataUploadEntity.setT3WarningCode(t3WarningCode);
                dataUploadEntity.setT3ErrorCode(t3ErrorCode);
                dataUploadEntity.setT3Level(t3Level);
            }
        }
    }

    public static void relativelyPeakHeight(String data, String resultVersionFormat, String firmwareVersion, AppDataUploadEntity dataUploadEntity) {
        if (ResultVersionConst.RESULTVERSIONFORMAT_01.equals(resultVersionFormat)) {
            // 结果版本为01 不关心
            if ("01810101".equals(firmwareVersion)) {
                Map<String, BigDecimal> relativeFenggaoByT5 = getRelativePeakHeightByT5(data.substring(242, 282));
                if (!CollectionUtils.isEmpty(relativeFenggaoByT5)) {
                    BigDecimal t1RelativelyPeakHeight = relativeFenggaoByT5.get("t1RelativeFenggao");
                    BigDecimal c1RelativelyPeakHeight = relativeFenggaoByT5.get("c1RelativeFenggao");
                    BigDecimal t2RelativelyPeakHeight = relativeFenggaoByT5.get("t2RelativeFenggao");
                    BigDecimal t3RelativelyPeakHeight = relativeFenggaoByT5.get("t3RelativeFenggao");
                    BigDecimal t4RelativelyPeakHeight = relativeFenggaoByT5.get("t4RelativeFenggao");

                    dataUploadEntity.setT1RelativelyPeakHeight(t1RelativelyPeakHeight);
                    dataUploadEntity.setC1RelativelyPeakHeight(c1RelativelyPeakHeight);
                    dataUploadEntity.setT2RelativelyPeakHeight(t2RelativelyPeakHeight);
                    dataUploadEntity.setT3RelativelyPeakHeight(t3RelativelyPeakHeight);
                    dataUploadEntity.setT4RelativelyPeakHeight(t4RelativelyPeakHeight);
                }
            }

        } else if (ResultVersionConst.RESULTVERSIONFORMAT_02.equals(resultVersionFormat)) {

        } else if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
            // 03 版本的相对峰高
            Map<String, BigDecimal> relativeFenggaoByT5 = getRelativePeakHeightByT5(data.substring(242, 282));
            if (!CollectionUtils.isEmpty(relativeFenggaoByT5)) {
                BigDecimal t1RelativelyPeakHeight = relativeFenggaoByT5.get("t1RelativeFenggao");
                BigDecimal c1RelativelyPeakHeight = relativeFenggaoByT5.get("c1RelativeFenggao");
                BigDecimal t2RelativelyPeakHeight = relativeFenggaoByT5.get("t2RelativeFenggao");
                BigDecimal t3RelativelyPeakHeight = relativeFenggaoByT5.get("t3RelativeFenggao");
                BigDecimal t4RelativelyPeakHeight = relativeFenggaoByT5.get("t4RelativeFenggao");

                dataUploadEntity.setT1RelativelyPeakHeight(t1RelativelyPeakHeight);
                dataUploadEntity.setC1RelativelyPeakHeight(c1RelativelyPeakHeight);
                dataUploadEntity.setT2RelativelyPeakHeight(t2RelativelyPeakHeight);
                dataUploadEntity.setT3RelativelyPeakHeight(t3RelativelyPeakHeight);
                dataUploadEntity.setT4RelativelyPeakHeight(t4RelativelyPeakHeight);
            }
        }
    }

    public static void analysisC1(String data, AppDataUploadEntity dataUploadEntity) {
        Map<String, Object> c1LineInfo = getCLineInfo(data.substring(282, 314));
        if (!CollectionUtils.isEmpty(c1LineInfo)) {
            BigDecimal c1PeakHeightPosition = new BigDecimal(c1LineInfo.get("peakHeightPosition").toString());
            BigDecimal c1AbsolutePeakHeight = new BigDecimal(c1LineInfo.get("absolutePeakHeight").toString());
            BigDecimal c1PeakArea = new BigDecimal(c1LineInfo.get("peakArea").toString());
            int c1WandValid = Integer.parseInt(c1LineInfo.get("wandValid").toString());

            dataUploadEntity.setC1PeakHeightPosition(c1PeakHeightPosition);
            dataUploadEntity.setC1AbsolutePeakHeight(c1AbsolutePeakHeight);
            dataUploadEntity.setC1PeakArea(c1PeakArea);
            dataUploadEntity.setC1WandValid(c1WandValid);

        }
    }

    public static void analysisC2(String data, AppDataUploadEntity dataUploadEntity) {
        Map<String, Object> c2LineInfo = getCLineInfo(data.substring(314, 346));
        if (!CollectionUtils.isEmpty(c2LineInfo)) {
            BigDecimal c2PeakHeightPosition = new BigDecimal(c2LineInfo.get("peakHeightPosition").toString());
            BigDecimal c2AbsolutePeakHeight = new BigDecimal(c2LineInfo.get("absolutePeakHeight").toString());
            BigDecimal c2PeakArea = new BigDecimal(c2LineInfo.get("peakArea").toString());
            int c2WandValid = Integer.parseInt(c2LineInfo.get("wandValid").toString());

            dataUploadEntity.setC2PeakHeightPosition(c2PeakHeightPosition);
            dataUploadEntity.setC2AbsolutePeakHeight(c2AbsolutePeakHeight);
            dataUploadEntity.setC2PeakArea(c2PeakArea);
            dataUploadEntity.setC2WandValid(c2WandValid);
        }
    }

    public static void analysisC3(String data, String error, AppDataUploadEntity dataUploadEntity) {
        String c3 = data.substring(346, 378);
        BigDecimal whiteFieldAvg = getParseValue(c3.substring(0, 8));
        BigDecimal PDTemperature = getParseValue(c3.substring(8, 16));
        dataUploadEntity.setWhiteFieldAvg(whiteFieldAvg);

        if (BluetoothDataErrorEnum.CODE_2.getValue().equals(error) && PDTemperature.floatValue() >= 20) {
            dataUploadEntity.setError(BluetoothDataErrorEnum.CODE_2_H.getValue());
        } else if (BluetoothDataErrorEnum.CODE_2.getValue().equals(error) && PDTemperature.floatValue() < 20) {
            dataUploadEntity.setError(BluetoothDataErrorEnum.CODE_2_L.getValue());
        }
        dataUploadEntity.setPDTemperature(PDTemperature);
    }

}
