package com.mira.bluetooth.service.provider;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mira.api.bluetooth.consts.ErrorAndWarningCodeConst;
import com.mira.api.bluetooth.dto.backend.*;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.api.bluetooth.enums.BluetoothDataWarningEnum;
import com.mira.api.bluetooth.enums.RunBoardFlagEnum;
import com.mira.api.bluetooth.provider.IDataUploadProvider;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.dal.mapper.AppDataUploadMapper;
import com.mira.bluetooth.dto.backend.BaseLineResultDTO;
import com.mira.bluetooth.exception.BluetoothException;
import com.mira.bluetooth.service.util.RunBoardCheckDataHelper;
import com.mira.bluetooth.service.util.WandsParamRecordHelper;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.response.CommonResult;
import com.mira.core.util.ZoneDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 测试数据接口实现
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-08-08
 **/
@Slf4j
@RestController
public class DataUploadProvider implements IDataUploadProvider {
    @Resource
    private AppDataUploadMapper appDataUploadMapper;
    @Resource
    private WandsParamRecordHelper wandsParamRecordHelper;
    @Resource
    private RunBoardCheckDataHelper runBoardCheckDataHelper;
    @Resource
    private IUserProvider userProvider;

    @Override
    public CommonResult<DataUploadResponseDTO> dataUploadPage(DataUploadPageRequestDTO dataUploadPageRequestDTO) {
        DataUploadResponseDTO dataUploadResponseDTO = new DataUploadResponseDTO();

        Long userId = dataUploadPageRequestDTO.getUserId();
        String testWandType = dataUploadPageRequestDTO.getTestWandType();
        Integer currPage = dataUploadPageRequestDTO.getCurrent();
        Integer pageSize = dataUploadPageRequestDTO.getSize();

        int currIndex = (currPage - 1) * pageSize;

        Integer count = appDataUploadMapper.countWithDeleted(testWandType, userId);
        if (count == 0) {
            dataUploadResponseDTO.setTotal(0);
            return CommonResult.OK(dataUploadResponseDTO);
        }
        List<AppDataUploadEntity> records = appDataUploadMapper.pageWithDeleted(currIndex, pageSize, testWandType, userId);

        List<AppDataUploadListDTO> appDataUploadListDTOS = new ArrayList<>();
        for (AppDataUploadEntity dataUploadEntity : records) {
            AppDataUploadListDTO appDataUploadListDTO = new AppDataUploadListDTO();
            BeanUtil.copyProperties(dataUploadEntity, appDataUploadListDTO);
            String dbTestWandType = dataUploadEntity.getTestWandType();
            String testWandBatch = dataUploadEntity.getTestWandBatch();
            WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(testWandBatch,
                    testWandType.startsWith("0") ? dbTestWandType.substring(1) : dbTestWandType);
            if (wandsParamRecordDTO != null) {
                appDataUploadListDTO.setUHalfBaseWidth1(wandsParamRecordDTO.getUHalfBaseWidth1());
                appDataUploadListDTO.setUHalfBaseWidth2(wandsParamRecordDTO.getUHalfBaseWidth2());
                appDataUploadListDTO.setUHalfBaseWidth3(wandsParamRecordDTO.getUHalfBaseWidth3());
            }
            BaseLineResultDTO baseLineResult = new BaseLineResultDTO();
            String warning = dataUploadEntity.getWarning();
            String error = dataUploadEntity.getError();
            List<String> displayEcodeList = Arrays.asList("00");
            List<String> displayWcodeList = Arrays.asList("00", "05", "09");
            if (displayEcodeList.contains(error) && displayWcodeList.contains(warning) && !"00".equals(testWandType)) {
                boolean checkDataResult = runBoardCheckDataHelper.checkData(dataUploadEntity, wandsParamRecordDTO);
                appDataUploadListDTO.setCheckDataResult(checkDataResult);
                baseLineResult = runBoardCheckDataHelper.getBaseLineResult(dataUploadEntity);
            } else {
                appDataUploadListDTO.setCheckDataResult(false);
            }
            appDataUploadListDTO.setAvgBaseLine(baseLineResult.getAvgBaseLine() == 0 ? -1 : baseLineResult.getAvgBaseLine());
            appDataUploadListDTO.setT1cBaseLinexielv(baseLineResult.getT1cBaseLinexielv() == 0 ? -1 : baseLineResult.getT1cBaseLinexielv());
            appDataUploadListDTO.setT2cBaseLinexielv(baseLineResult.getT2cBaseLinexielv() == 0 ? -1 : baseLineResult.getT2cBaseLinexielv());
            appDataUploadListDTOS.add(appDataUploadListDTO);
        }
        dataUploadResponseDTO.setTotal(count);
        dataUploadResponseDTO.setAppDataUploadListDTOS(appDataUploadListDTOS);
        return CommonResult.OK(dataUploadResponseDTO);
    }

    @Override
    public CommonResult<AppDataUploadDTO> getByIdWithDeleted(Long id) {
        AppDataUploadEntity dataUploadEntity = appDataUploadMapper.getByIdWithDeleted(id);
        if (dataUploadEntity == null) {
            throw new BluetoothException("测试数据不存在");
        }
        AppDataUploadDTO appDataUploadDTO = new AppDataUploadDTO();
        BeanUtil.copyProperties(dataUploadEntity, appDataUploadDTO);
        return CommonResult.OK(appDataUploadDTO);
    }

    @Override
    public CommonResult<Void> changeDeleteStatus(Long id, Long userId, Integer deleted, String sysNote) {
        Integer runBoardFlag;
        if (deleted == 1) {
            runBoardFlag = RunBoardFlagEnum.DELETED.getCode();
        } else {
            AppDataUploadEntity dataUploadEntity = appDataUploadMapper.getByIdWithDeleted(id);
            Integer autoFlag = dataUploadEntity.getAutoFlag();
            if (autoFlag == 1) {
                runBoardFlag = RunBoardFlagEnum.MANUAL_ADD_DATA.getCode();
            } else {
                String testWandType = dataUploadEntity.getTestWandType();
                WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(dataUploadEntity.getTestWandBatch(),
                        testWandType.startsWith("0") ? testWandType.substring(1) : testWandType);

                String error = dataUploadEntity.getError();
                String warning = dataUploadEntity.getWarning();
                // 前端不显示的数据
                if (!ErrorAndWarningCodeConst.MEANINGFUL_ERROR_CODE.contains(error)
                        || !ErrorAndWarningCodeConst.MEANINGFUL_WARN_CODE.contains(warning)) {
                    log.warn("setRunBoardFlag data error, userId:{}, error:{}, warning:{}", dataUploadEntity.getUserId(), error,
                            warning);
                    runBoardFlag = RunBoardFlagEnum.ERROR_DATA.getCode();
                    // 前端显示异常的数据
                } else if (ErrorAndWarningCodeConst.FRONT_DISPLAY_ERROR_CODE.contains(error)
                        || ErrorAndWarningCodeConst.FRONT_DISPLAY_WARN_CODE.contains(warning)) {
                    log.warn("setRunBoardFlag data error, userId:{}, error:{}, warning:{}", dataUploadEntity.getUserId(), error, warning);
                    runBoardFlag = RunBoardFlagEnum.ERROR_DATA.getCode();
                } else {
                    boolean checkDataBool = runBoardCheckDataHelper.checkData(dataUploadEntity, wandsParamRecordDTO);
                    if (checkDataBool) {
                        runBoardFlag = RunBoardFlagEnum.PASS.getCode();
                    } else {
                        runBoardFlag = RunBoardFlagEnum.FAIL.getCode();
                    }
                }
            }
        }

        appDataUploadMapper.changeDeleteStatus(id, deleted, sysNote, runBoardFlag);
        userProvider.systemEditPeriod(userId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Void> changeCompleteTime(Long id, String dataTimeZone, String newCompleteTime, String sysNote) {
        try {
            Long completeTimestamp = ZoneDateUtil.timestamp(dataTimeZone, newCompleteTime, DatePatternConst.DATE_TIME_PATTERN);
            AppDataUploadEntity dataUploadEntity = appDataUploadMapper.getByIdWithDeleted(id);
            appDataUploadMapper.changeCompleteTime(id, newCompleteTime, completeTimestamp, sysNote);
            userProvider.systemEditPeriod(dataUploadEntity.getUserId());
        } catch (Exception e) {
            throw new RuntimeException("Mira Desk change complete time occur error", e);
        }
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Void> changeWandBatch(ChangeWandBatchDTO changeWandBatchDTO) {
        Long id = changeWandBatchDTO.getId();
        AppDataUploadEntity dataUploadEntity = appDataUploadMapper.getByIdWithDeleted(id);
        if (dataUploadEntity == null) {
            throw new BluetoothException("测试数据不存在");
        }
        if (dataUploadEntity.getAutoFlag() == 0) {
            throw new BluetoothException("not manual added data, without permission to change wand batch");
        }
        dataUploadEntity.setTestWandBatch(changeWandBatchDTO.getTestWandBatch());
        appDataUploadMapper.updateById(dataUploadEntity);
        userProvider.systemEditPeriod(dataUploadEntity.getUserId());
        return CommonResult.OK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> updateTestDataValid(Long id, Long userId, String username) {
        AppDataUploadEntity dataUploadEntity = appDataUploadMapper.getByIdWithDeleted(id);
        dataUploadEntity.setWarning(BluetoothDataWarningEnum.CODE_0.getValue());
        dataUploadEntity.setSysNote("user id:" + userId + ", name:"
                + username + ", 数据改为有效");
        dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.MANUAL_ADD_DATA.getCode());
        dataUploadEntity.setAutoFlag(1);
        appDataUploadMapper.updateById(dataUploadEntity);

        // 编辑经期
        userProvider.systemEditPeriod(dataUploadEntity.getUserId());
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Integer> countMaxWands(Long userId) {
        long maxWandsCount = appDataUploadMapper.selectCount(Wrappers.<AppDataUploadEntity>lambdaQuery()
                .eq(AppDataUploadEntity::getUserId, userId)
                .eq(AppDataUploadEntity::getTestWandType, WandTypeEnum.LH_E3G_PDG.getString()));
        return CommonResult.OK(Integer.valueOf(maxWandsCount + ""));
    }
}
