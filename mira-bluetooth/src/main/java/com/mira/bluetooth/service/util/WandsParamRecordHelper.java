package com.mira.bluetooth.service.util;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.bluetooth.dal.dao.WandsParamRecordDAO;
import com.mira.bluetooth.dal.entity.WandsParamRecordEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@SuppressWarnings("all")
@Component
public class WandsParamRecordHelper {
    @Resource
    private WandsParamRecordDAO wandsParamRecordDAO;

    private static final Cache<String, WandsParamRecordDTO> wandsParamRecordDTOCache;

    static {
        // 当天有效
        long expire = Duration.between(LocalDateTime.now(),
                LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0)).toSeconds();
        wandsParamRecordDTOCache = CacheBuilder.newBuilder()
                .expireAfterWrite(expire, TimeUnit.SECONDS)
                .build();
    }

    private void init() {
        synchronized (WandsParamRecordHelper.class) {
            if (wandsParamRecordDTOCache.asMap().isEmpty()) {
                wandsParamRecordDTOCache.invalidateAll();
                for (WandsParamRecordDTO wandsParamRecordDTO : getAll()) {
                    String key = wandsParamRecordDTO.getWandBatch3() + ":" + wandsParamRecordDTO.getUStripType();
                    wandsParamRecordDTOCache.put(key, wandsParamRecordDTO);
                }
            }
        }
    }

    public WandsParamRecordDTO getByWandBatch3(String wandBatch3, String uStripType) {
        wandsParamRecordDTOCache.cleanUp();
        ConcurrentMap<String, WandsParamRecordDTO> cacheMap = wandsParamRecordDTOCache.asMap();
        if (cacheMap.isEmpty()) {
            init();
        }
        return cacheMap.get(wandBatch3 + ":" + uStripType);
    }

    private List<WandsParamRecordDTO> getAll() {
        List<WandsParamRecordEntity> wandsParamRecordEntities = wandsParamRecordDAO.listAll();
        List<WandsParamRecordDTO> wandsParamRecordDTOS = new ArrayList<>();
        for (WandsParamRecordEntity wandsParamRecordEntity : wandsParamRecordEntities) {
            WandsParamRecordDTO wandsParamRecordDTO = new WandsParamRecordDTO();
            BeanUtil.copyProperties(wandsParamRecordEntity, wandsParamRecordDTO);
            // 异常数据
            if ("0000-00-00 00:00:00".equals(wandsParamRecordEntity.getUIncubateTime())) {
                wandsParamRecordDTO.setUIncubateTime("0");
            }
            wandsParamRecordDTO.setFLowerLimit1(Float.valueOf(wandsParamRecordEntity.getFLowerLimit1()));
            wandsParamRecordDTO.setFUpperLimit1(Float.valueOf(wandsParamRecordEntity.getFUpperLimit1()));
            wandsParamRecordDTO.setFLowerLimit2(Float.valueOf(wandsParamRecordEntity.getFLowerLimit2()));
            wandsParamRecordDTO.setFUpperLimit2(Float.valueOf(wandsParamRecordEntity.getFUpperLimit2()));
            wandsParamRecordDTO.setFLowerLimit3(Float.valueOf(wandsParamRecordEntity.getFLowerLimit3()));
            wandsParamRecordDTO.setFUpperLimit3(Float.valueOf(wandsParamRecordEntity.getFUpperLimit3()));
            wandsParamRecordDTO.setSlopeT1c(wandsParamRecordDTO.getSlopeT1c());
            wandsParamRecordDTO.setSlopeT2c(wandsParamRecordDTO.getSlopeT2c());
            wandsParamRecordDTO.setAvgBaseLine(wandsParamRecordDTO.getAvgBaseLine());
            wandsParamRecordDTOS.add(wandsParamRecordDTO);
        }
        return wandsParamRecordDTOS;
    }
}
