package com.mira.bluetooth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.enums.BBTErrorCodeEnum;
import com.mira.api.bluetooth.enums.BBTModeErrorCodeEnum;
import com.mira.api.user.dto.user.SysTipsDTO;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.TemperatureResultDTO;
import com.mira.api.user.dto.user.UserTemperatureDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.consts.BluetoothDataConst;
import com.mira.bluetooth.controller.vo.DataHistoryVO;
import com.mira.bluetooth.controller.vo.DataUploadVO;
import com.mira.bluetooth.dal.dao.AppDataTemperatureDAO;
import com.mira.bluetooth.dal.dao.AppUserBbtBindDAO;
import com.mira.bluetooth.dal.entity.AppDataTemperatureEntity;
import com.mira.bluetooth.dal.entity.AppUserBbtBindEntity;
import com.mira.bluetooth.dto.BluetoothDataDTO;
import com.mira.bluetooth.dto.DataUploadDTO;
import com.mira.api.bluetooth.enums.BarTipEnum;
import com.mira.bluetooth.service.IBBTService;
import com.mira.bluetooth.service.manager.CacheManager;
import com.mira.bluetooth.service.util.BBTBiomarkerHelper;
import com.mira.bluetooth.service.util.BluetoothTemperatureUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BiomarkerEnum;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * BBT接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("bbtService")
public class BBTServiceImpl implements IBBTService {
    @Resource
    private AppDataTemperatureDAO appDataTemperatureDAO;
    @Resource
    private AppUserBbtBindDAO appUserBbtBindDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private BBTBiomarkerHelper bbtBiomarkerHelper;

    @Resource
    private IUserProvider userProvider;

    @Override
    public DataUploadVO upload(DataUploadDTO dataUploadDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();

        // 返回的蓝牙数据
        List<BluetoothDataDTO> returnBluetoothDatas = new ArrayList<>();

        List<AppDataTemperatureEntity> toTemperatureAlgorithms = new ArrayList<>();
        for (BluetoothDataDTO inBluetoothData : dataUploadDTO.getBluetoothDatas()) {
            BluetoothDataDTO returnBluetoothData = new BluetoothDataDTO();
            returnBluetoothData.setCompleteTimeStamp(inBluetoothData.getCompleteTimeStamp());

            // 解析蓝牙数据
            AppDataTemperatureEntity dataTemperatureEntity;
            try {
                dataTemperatureEntity = BluetoothTemperatureUtil.parseTemperature(inBluetoothData, userId, timeZone);
                UpdateEntityTimeUtil.setBaseEntityTime(timeZone, dataTemperatureEntity);
            } catch (Exception ex) {
                log.info("user:{}，analysis temperature data occur error:{}", userId, ex);
                returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_PARSE_ERROR);
                returnBluetoothDatas.add(returnBluetoothData);
                continue;
            }
            // 检查重复数据
            long dataUploadExistCount = appDataTemperatureDAO.getCountByUserIdAndCompleteTime(userId, dataTemperatureEntity.getCompleteTimestamp());
            if (dataUploadExistCount > 0) {
                log.info("userId:{} temperature data already exist", loginInfo.getId());
                returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_ALREADY_EXIST);
                returnBluetoothDatas.add(returnBluetoothData);
                continue;
            }
            // save
            try {
                appDataTemperatureDAO.save(dataTemperatureEntity);
            } catch (Exception e) {
                log.info("userId:{} completeTime: {} MySQLIntegrityConstraintViolationException.", userId, dataTemperatureEntity.getCompleteTime());
                returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_ALREADY_EXIST);
                returnBluetoothDatas.add(returnBluetoothData);
                continue;
            }

            returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_SUCCESS);
            returnBluetoothData.setBluetoothData(inBluetoothData.getBluetoothData());
            returnBluetoothDatas.add(returnBluetoothData);

            toTemperatureAlgorithms.add(dataTemperatureEntity);
        }

        // 处理并分析蓝牙数据
        DataUploadVO result = handleBluetoothData(userId, timeZone, toTemperatureAlgorithms, returnBluetoothDatas);
        cacheManager.deleteTipsAlgorithmResult(userId);

        try {
            SysTipsDTO sysTipsDTO = userProvider.buildTipsResult(userId).getData();
            sysTipsDTO.setTips(sysTipsDTO.getTips().stream()
                    .filter(tip -> BiomarkerEnum.BBT.getBiomarker().equals(tip.getBiomarker()))
                    .collect(Collectors.toList()));
            result.setTips(sysTipsDTO);
        } catch (Exception e) {
            log.error("BBT upload tips error", e);
        }

        return result;
    }

    @Override
    public Integer bindInfo() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        Integer bindStatus = 0;
        AppUserBbtBindEntity appUserBbtBindEntity = appUserBbtBindDAO.getByUserId(userId);
        if (ObjectUtils.isNotEmpty(appUserBbtBindEntity)) {
            bindStatus = appUserBbtBindEntity.getBindStatus();
        }
        return bindStatus;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editBindInfo(Integer bindStatus) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        AppUserBbtBindEntity appUserBbtBindEntity = appUserBbtBindDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(appUserBbtBindEntity)) {
            appUserBbtBindEntity = new AppUserBbtBindEntity();
            appUserBbtBindEntity.setUserId(userId);
            appUserBbtBindEntity.setTimeZone(timeZone);
            appUserBbtBindEntity.setBindStatus(bindStatus);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserBbtBindEntity);
            appUserBbtBindDAO.save(appUserBbtBindEntity);
            return;
        }

        appUserBbtBindEntity.setBindStatus(bindStatus);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserBbtBindEntity);
        appUserBbtBindDAO.updateById(appUserBbtBindEntity);
    }

    private DataUploadVO handleBluetoothData(Long userId, String timeZone,
                                             List<AppDataTemperatureEntity> toTemperatureAlgorithms,
                                             List<BluetoothDataDTO> returnBluetoothDatas) {
        DataUploadVO resultVO = new DataUploadVO();

        if (toTemperatureAlgorithms.isEmpty()) {
            log.info("userId:{} toTemperatureAlgorithms empty", userId);
            resultVO.setBluetoothDatas(returnBluetoothDatas);
            return resultVO;
        }

        for (AppDataTemperatureEntity dataTemperatureEntity : toTemperatureAlgorithms) {
            String error = dataTemperatureEntity.getError();
            if (BBTErrorCodeEnum.DATA_FORMAT_ERROR_1.getCode().equals(error)
                    || BBTErrorCodeEnum.DATA_FORMAT_ERROR_2.getCode().equals(error)) {
                continue;
            }

            // 保存用户体温数据
            TemperatureResultDTO temperatureResultDTO = userProvider.addTemperature(userId, BeanUtil.toBean(dataTemperatureEntity, UserTemperatureDTO.class)).getData();
            // 用户当天是否已经有有效数据
            List<TemperatureDTO> todayTemperatureList = userProvider.getTemperatureList(
                    userId, ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN)).getData();
            long validCount = todayTemperatureList.stream()
                    .filter(temperature -> temperature.getEcode() == null
                            && temperature.getTempF() != null
                            && !Objects.equals(temperature.getTempF(), BigDecimal.ONE))
                    .count();

            List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = new ArrayList<>();
            String modeError = dataTemperatureEntity.getModeError();
            if (BBTModeErrorCodeEnum.NORMAL.getCode().equals(modeError)) {
                modeError = null;
            } else {
                modeError = "T" + modeError;
            }
            TemperatureDTO temperatureDTO = new TemperatureDTO();
            temperatureDTO.setTempTime(dataTemperatureEntity.getCompleteTime());
            temperatureDTO.setTempC(temperatureResultDTO.getTempC());
            temperatureDTO.setTempF(temperatureResultDTO.getTempF());
            temperatureDTO.setEcode(modeError);

            // 单位，1A为°C unit=1；15为°F unit=2
            Integer unit = dataTemperatureEntity.getUnit();
            // 更新用户温度单位
            String tempUnit = unit == 1 ? TempUnitEnum.C.getValue() : TempUnitEnum.F.getValue();
            userProvider.editTemperatureUnit(userId, tempUnit);
            WandTestBiomarkerDTO wandTestBiomarkerDTO = bbtBiomarkerHelper.buildBBTBiomarkerDTO(userId, temperatureDTO, tempUnit);
            wandTestBiomarkerDTOS.add(wandTestBiomarkerDTO);

            DataHistoryVO dataHistoryVO = new DataHistoryVO();
            dataHistoryVO.setBarTip(getBarTip(validCount));
            dataHistoryVO.setTestingDTOS(new ArrayList<>());
            dataHistoryVO.setWandTestDataList(wandTestBiomarkerDTOS);
            resultVO.setDataHistoryDTO(dataHistoryVO);
        }

        resultVO.setBluetoothDatas(returnBluetoothDatas);
        return resultVO;
    }

    private Integer getBarTip(long validCount) {
        if (validCount > 0) {
            return -1;
        }

        return BarTipEnum.BBT.getCode();
    }
}
