package com.mira.bluetooth.service;

import com.mira.api.user.dto.user.UserBindDTO;
import com.mira.api.user.dto.user.UserBindVersionDTO;
import com.mira.bluetooth.controller.vo.DataHistoryVO;
import com.mira.bluetooth.controller.vo.DataUploadVO;
import com.mira.bluetooth.dto.DataUploadDTO;

/**
 * 蓝牙数据接口
 *
 * <AUTHOR>
 */
public interface IBluetoothService {
    /**
     * 设备上传蓝牙数据
     *
     * @param dataUploadDTO 蓝牙数据
     * @return DataUploadVO
     */
    DataUploadVO upload(DataUploadDTO dataUploadDTO);

    /**
     * 获取用户最新上传的历史时间
     *
     * @return Long
     */
    Long getUserMaxCompleteTime();

    /**
     * 获取某一天的测试历史
     *
     * @param dateStr 日期
     * @return DataHistoryVO
     */
    DataHistoryVO getHistory(String dateStr);

    /**
     * 绑定或者解绑仪器
     *
     * @param userBindDTO 绑定信息
     * @return String
     */
    Integer bind(UserBindDTO userBindDTO);

    /**
     * 修改绑定的firmware版本
     *
     * @param userBindVersionDTO 绑定信息
     */
    void editBindVersion(UserBindVersionDTO userBindVersionDTO);

    /**
     * 增加wait-shipping标记
     */
    void waitShipping();
}
