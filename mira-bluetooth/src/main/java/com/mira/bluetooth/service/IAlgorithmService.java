package com.mira.bluetooth.service;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.report.ReportReturnDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.dto.example.AlgorithmExampleDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmLongerPeriodDTO;
import com.mira.api.bluetooth.dto.report.AlgorithmReportDataDTO;
import com.mira.api.bluetooth.dto.tips.AlgorithmGetTipsDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;

/**
 * 算法服务接口
 *
 * <AUTHOR>
 */
public interface IAlgorithmService {

    /**
     * 更新阈值
     *
     * @param userId    用户id
     * @param threshold 阈值
     * @return String
     */
    String updateAlgorithmThreshold(Long userId, Integer threshold);

    /**
     * 编辑经期
     *
     * @param algorithmEditPeriodDTO 参数
     * @return 0-默认正常LH阈值，1-需要用户确认将LH阈值变低的状态，2-LH阈值变低的状态
     */
    Integer algorithmEditPeriod(AlgorithmEditPeriodDTO algorithmEditPeriodDTO);

    /**
     * 编辑经期，不入库
     *
     * @param algorithmEditPeriodDTO 参数
     * @return AlgorithmResultDTO
     */
    AlgorithmResultDTO algorithmEditPeriodNotUpdateDB(AlgorithmEditPeriodDTO algorithmEditPeriodDTO);

    /**
     * 长周期
     *
     * @param algorithmLongerPeriodDTO 参数
     */
    void algorithmLongerPeriod(AlgorithmLongerPeriodDTO algorithmLongerPeriodDTO);

    /**
     * 测试样例数据
     *
     * @return AlgorithmExampleDataDTO
     */
    AlgorithmExampleDTO algorithmExampleData();

    /**
     * Tips数据
     *
     * @param algorithmGetTipsDTO 参数
     * @return GetTipsReturnDTO
     */
    GetTipsReturnDTO algorithmTipsData(AlgorithmGetTipsDTO algorithmGetTipsDTO);

    /**
     * Report数据
     *
     * @param algorithmReportDataDTO 参数
     * @return ReportReturnDTO
     */
    ReportReturnDTO algorithmReportData(AlgorithmReportDataDTO algorithmReportDataDTO);
}
