package com.mira.bluetooth.service.manager;

import com.mira.api.bluetooth.consts.DefaultWandsParamConst;
import com.mira.api.bluetooth.consts.ErrorAndWarningCodeConst;
import com.mira.api.bluetooth.consts.ResultVersionConst;
import com.mira.api.bluetooth.enums.BluetoothDataErrorEnum;
import com.mira.api.bluetooth.enums.BluetoothDataWarningEnum;
import com.mira.api.bluetooth.enums.TWarningCodeEnum;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.api.bluetooth.enums.DeviceErrorCodeEnum;
import com.mira.bluetooth.handler.ITestResultHandler;
import com.mira.bluetooth.handler.testresult.TestResultHandler;
import com.mira.bluetooth.service.util.RunBoardCheckDataHelper;
import com.mira.bluetooth.service.util.TLineCheckHelper;
import com.mira.bluetooth.service.util.WandsParamRecordHelper;
import com.mira.core.consts.enums.WandTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 蓝牙数据接口通用层
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class BluetoothManager {
    @Resource
    private RunBoardCheckDataHelper runBoardCheckDataHelper;
    @Resource
    private TLineCheckHelper tLineCheckHelper;
    @Resource
    private WandsParamRecordHelper wandsParamRecordHelper;

    /**
     * 构建激素数据
     *
     * @param dataUploadEntity 上传数据的表记录
     * @param trialFlag        beta标记
     * @return HormoneDTO null表示忽略该条数据
     */
    public HormoneDTO convertToHormoneDTO(AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO, Integer trialFlag) {
        Long userId = dataUploadEntity.getUserId();
        String error = dataUploadEntity.getError();
        String warning = dataUploadEntity.getWarning();
        String testWandType = dataUploadEntity.getTestWandType();

        // step1:过滤前端不显示的数据
        if (!ErrorAndWarningCodeConst.MEANINGFUL_ERROR_CODE.contains(error) || !ErrorAndWarningCodeConst.MEANINGFUL_WARN_CODE.contains(warning)) {
            log.warn("dataUploadEntity data error, userId:{}, error:{}, warning:{}", userId, error, warning);
            return null;
        }
        HormoneDTO hormoneDTO = buildHormoneDTO(dataUploadEntity, wandsParamRecordDTO, trialFlag);

        // step2:处理前端显示异常的数据
        if (ErrorAndWarningCodeConst.FRONT_DISPLAY_ERROR_CODE.contains(error) || ErrorAndWarningCodeConst.FRONT_DISPLAY_WARN_CODE.contains(warning)) {
            log.warn("dataUploadEntity data error, userId:{}, error:{}, warning:{}", userId, error, warning);
            hormoneDTO.setFlag(0);
            return hormoneDTO;
        }

        // step3:跑板异常校验，跑板失败数据需要展示(加入到dailydata)，但不调用算法(flag==0)
        boolean checkDataBool = runBoardCheckDataHelper.checkData(dataUploadEntity, wandsParamRecordDTO);
        if (!checkDataBool) {
            hormoneDTO.setFlag(0);
            hormoneDTO.getTest_results().setEcode(DeviceErrorCodeEnum.B01.getValue());
            return hormoneDTO;
        }

        // step4:T线校验，调算法，但会将数值合理化(将超过范围的数值修改为UpperLimit or LowerLimit)，T线校验有问题的数据，Ecode设置为B03
        if (testWandType.equals("0" + WandTypeEnum.LH.getString())
                || testWandType.equals("0" + WandTypeEnum.E3G_LH.getString())) {
            String t1WarningCode = dataUploadEntity.getT1WarningCode();
            String t2WarningCode = dataUploadEntity.getT2WarningCode();
            String testWandBatch = dataUploadEntity.getTestWandBatch();
            String resultVersionFormat = dataUploadEntity.getResultVersionFormat();
            if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
                boolean tLineWarningCheck = tLineCheckHelper.tLineWarningCheck(userId, hormoneDTO, dataUploadEntity, wandsParamRecordDTO);
                if (!tLineWarningCheck) {
                    hormoneDTO.setFlag(1);
                    hormoneDTO.getTest_results().setEcode(DeviceErrorCodeEnum.B03.getValue());
                    log.info("tLineWarningCheck:t1WarningCode:{}, t2WarningCode:{}", t1WarningCode, t2WarningCode);
                    return hormoneDTO;
                }
            }
        }
        return hormoneDTO;
    }

    /**
     * 构建激素数据
     *
     * @param dataUploadEntity    上传数据的表记录
     * @param wandsParamRecordDTO 试剂参数
     * @param trialFlag           beta标记
     * @return HormoneDTO null表示忽略该条数据
     */
    public HormoneDTO buildHormoneDTO(AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO, Integer trialFlag) {
        // 错误码
        String error = dataUploadEntity.getError();
        // 警告码
        String warning = dataUploadEntity.getWarning();
        // t1浓度值
        BigDecimal t1ConValue = dataUploadEntity.getT1ConValue();
        // t2浓度值
        BigDecimal t2ConValue = dataUploadEntity.getT2ConValue();
        // t3浓度值
        BigDecimal t3ConValue = dataUploadEntity.getT3ConValue();
        // 试剂完成时间
        String completeTime = dataUploadEntity.getCompleteTime();
        // 试剂类型
        String testWandType = dataUploadEntity.getTestWandType();

        HormoneDTO hormoneDTO = new HormoneDTO();
        hormoneDTO.setTest_time(completeTime);
        hormoneDTO.setWandBatch3(dataUploadEntity.getTestWandBatch());
        hormoneDTO.setId(dataUploadEntity.getId());
        HormoneDTO.TestResult testResult = new HormoneDTO.TestResult();
        setEcode(error, warning, testResult);

        // 处理 test result
        ITestResultHandler testResultHandler = TestResultHandler.get(dataUploadEntity.getTestWandType());
        if (Objects.nonNull(testResultHandler)) {
            testResultHandler.handle(dataUploadEntity, testResult);
        }

        if (StringUtils.isNotBlank(testResult.getEcode())) {
            hormoneDTO.setFlag(0);
        }

        if (trialFlag == null || (!trialFlag.equals(WandTypeEnum.HCG_QUALITATIVE.getInteger()) && trialFlag != -1)) {
            if (testWandType.equals(WandTypeEnum.HCG_QUALITATIVE.getString())) {
                testResult.setEcode(DeviceErrorCodeEnum.EB04.getValue());
                hormoneDTO.setFlag(0);
            }
        }
        hormoneDTO.setTest_results(testResult);
        HormoneDTO.ExtraInfo extraInfo = new HormoneDTO.ExtraInfo();
        if (testWandType.equals("0" + WandTypeEnum.HCG.getString())) {
            if (Objects.isNull(wandsParamRecordDTO)) {
                // 默认值
                extraInfo.setHcg_limitupper2(75000F);
            } else {
                extraInfo.setHcg_limitupper2(wandsParamRecordDTO.getFUpperLimit2());
            }
        }
        hormoneDTO.setExtra_info(extraInfo);
        return hormoneDTO;
    }

    private static void setEcode(String error, String warning, HormoneDTO.TestResult testResult) {
        if (BluetoothDataErrorEnum.CODE_1.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E01.getValue());
            return;
        }
        if (BluetoothDataErrorEnum.CODE_2_L.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E02L.getValue());
            return;
        }
        if (BluetoothDataErrorEnum.CODE_2_H.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E02H.getValue());
            return;
        }
        if (BluetoothDataErrorEnum.CODE_3.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E03.getValue());
            return;
        }
        if (BluetoothDataErrorEnum.CODE_4.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E04.getValue());
            return;
        }
        if (BluetoothDataErrorEnum.CODE_5.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E05.getValue());
            return;
        }
        if (BluetoothDataErrorEnum.CODE_6.getValue().equals(error)) {
            testResult.setEcode(DeviceErrorCodeEnum.E06.getValue());
            return;
        }
        if (BluetoothDataWarningEnum.CODE_6.getValue().equals(warning)) {
            testResult.setEcode(DeviceErrorCodeEnum.W06.getValue());
            return;
        }
        if (BluetoothDataWarningEnum.CODE_1.getValue().equals(warning)) {
            testResult.setEcode(DeviceErrorCodeEnum.W01.getValue());
            return;
        }
        if (BluetoothDataWarningEnum.CODE_7.getValue().equals(warning)) {
            testResult.setEcode(DeviceErrorCodeEnum.W07.getValue());
        }
    }

    public List<HormoneDTO> convertToHormoneDTOList(List<AppDataUploadEntity> dataUploadEntityList, Integer trialFlag) {
        // step1:过滤前端不显示的数据,不会添加到hormoneData
        List<HormoneDTO> hormoneDTOS = new ArrayList<>();

        Float lhLowerLimit = DefaultWandsParamConst.LH_LOWER_LIMIT;
        Float lhUpperLimit = DefaultWandsParamConst.LH_UPPER_LIMIT;
        Float e3gLowerLimit = DefaultWandsParamConst.E3G_LOWER_LIMIT;
        Float e3gUpperLimit = DefaultWandsParamConst.E3G_UPPER_LIMIT;

        for (AppDataUploadEntity dataUploadEntity : dataUploadEntityList) {
            Long userId = dataUploadEntity.getUserId();
            String error = dataUploadEntity.getError();
            String warning = dataUploadEntity.getWarning();
            String testWandType = dataUploadEntity.getTestWandType();
            // 检查支持的试剂类型
            if (StringUtils.isBlank(testWandType) || !WandTypeEnum.getAllowWandTypes().contains(testWandType)) {
                continue;
            }
            WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(dataUploadEntity.getTestWandBatch(),
                    testWandType.startsWith("0") ? testWandType.substring(1) : testWandType);
            HormoneDTO hormoneDTO = buildHormoneDTO(dataUploadEntity, wandsParamRecordDTO, trialFlag);

            // step2:处理前端显示异常的数据
            if (ErrorAndWarningCodeConst.FRONT_DISPLAY_ERROR_CODE.contains(error) || ErrorAndWarningCodeConst.FRONT_DISPLAY_WARN_CODE.contains(warning)) {
                log.warn("dataUploadEntity data error, userId:{}, error:{}, warning:{}", userId, error, warning);
                hormoneDTO.setFlag(0);
                hormoneDTOS.add(hormoneDTO);
                continue;
            }

            // step3:跑板异常校验，跑板失败数据需要展示(加入到dailydata)，但不调用算法(flag==0)
            boolean checkDataBool = runBoardCheckDataHelper.checkData(dataUploadEntity, wandsParamRecordDTO);
            if (!checkDataBool) {
                hormoneDTO.setFlag(0);
                hormoneDTO.getTest_results().setEcode(DeviceErrorCodeEnum.B01.getValue());
                hormoneDTOS.add(hormoneDTO);
                continue;
            }

            // step4:T线校验，调算法，但会将数值合理化(将超过范围的数值修改为UpperLimit or LowerLimit)，T线校验有问题的数据，Ecode设置为B03
            if (testWandType.equals("0" + WandTypeEnum.LH.getString())
                    || testWandType.equals("0" + WandTypeEnum.E3G_LH.getString())) {
                String t1WarningCode = dataUploadEntity.getT1WarningCode();
                String t2WarningCode = dataUploadEntity.getT2WarningCode();
                String testWandBatch = dataUploadEntity.getTestWandBatch();
                String resultVersionFormat = dataUploadEntity.getResultVersionFormat();
                // 批次校验
                String uStripType = testWandType.substring(1);
                if (ObjectUtils.isNotEmpty(wandsParamRecordDTO)) {
                    if (WandTypeEnum.LH.getString().equals(uStripType)) {
                        lhLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                        lhUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
                    } else if (WandTypeEnum.E3G_LH.getString().equals(uStripType)) {
                        lhLowerLimit = wandsParamRecordDTO.getFLowerLimit3();
                        lhUpperLimit = wandsParamRecordDTO.getFUpperLimit3();
                        e3gLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                        e3gUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
                    }
                }

                if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
                    // 某些数据 userHormoneDTO.setFlag(0);
                    HormoneDTO.TestResult test_results = hormoneDTO.getTest_results();
                    if (uStripType.equals(WandTypeEnum.LH.getString())) {
                        // t1对应lh
                        test_results.setWand_type(WandTypeEnum.LH.getInteger());
                        if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                            // LH >T1_con 不调算法
                            if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode)) {
                                // 传当前批次的lower值
                                test_results.setValue1(lhUpperLimit);
                            } else if (TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                                // LH <T1_con  不调算法
                                test_results.setValue1(lhLowerLimit);
                            }
                            test_results.setEcode(DeviceErrorCodeEnum.B03.getValue());
                        }

                    } else if (uStripType.equals(WandTypeEnum.E3G_LH.getString())) {
                        test_results.setWand_type(WandTypeEnum.E3G_LH.getInteger());
                        // t1对应e3g
                        if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                            if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode)) {
                                // 传当前批次的lower值
                                test_results.setValue1(e3gLowerLimit);
                            } else if (TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                                test_results.setValue1(e3gUpperLimit);
                            }
                        }
                        // t2对应lh
                        if (TWarningCodeEnum.CODE_1.getValue().equals(t2WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t2WarningCode)) {
                            if (TWarningCodeEnum.CODE_1.getValue().equals(t2WarningCode)) {
                                // 传当前批次的lower值
                                test_results.setValue2(lhUpperLimit);
                            } else if (TWarningCodeEnum.CODE_4.getValue().equals(t2WarningCode)) {
                                test_results.setValue2(lhLowerLimit);
                            }
                        }
                        if (!TWarningCodeEnum.CODE_0.getValue().equals(t1WarningCode) || !TWarningCodeEnum.CODE_0.getValue().equals(t2WarningCode)) {
                            test_results.setEcode(DeviceErrorCodeEnum.B03.getValue());
                        }
                    }
                    hormoneDTO.setTest_results(test_results);
                    hormoneDTO.setFlag(1);
                }
            }
            hormoneDTOS.add(hormoneDTO);
        }

        return hormoneDTOS;
    }
}
