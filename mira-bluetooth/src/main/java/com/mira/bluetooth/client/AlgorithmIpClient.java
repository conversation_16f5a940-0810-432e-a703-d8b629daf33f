package com.mira.bluetooth.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.mira.api.bluetooth.dto.example.AlgorithmExampleDTO;
import com.mira.api.bluetooth.dto.report.ReportReturnDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.bluetooth.dto.algorithm.request.*;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmMenopauseResultDTO;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.dto.algorithm.response.LongerPeriodReturnDTO;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * 算法服务调用
 *
 * <AUTHOR>
 */
@RetrofitClient(baseUrl = "${algorithm.host}", readTimeoutMs = 60000, connectTimeoutMs = 30000)
public interface AlgorithmIpClient extends CallClient {
    /**
     * 获取测试样例数据
     *
     * @param body 请求参数
     * @return String
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraSampleHormone")
    AlgorithmResult<AlgorithmExampleDTO> getExampleData(@Body String body);

    /**
     * 新测试数据
     *
     * @param newHormoneDataRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraNewHormone")
    AlgorithmResult<AlgorithmReturnDTO> sendNewHormoneData(@Body NewHormoneDataRequest newHormoneDataRequest);

    /**
     * 编辑经期
     *
     * @param editPeriodRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraEditPeriod")
    AlgorithmResult<AlgorithmReturnDTO> sendEditPeriodData(@Body EditPeriodRequest editPeriodRequest);

    /**
     * 获取Tips
     *
     * @param tipsRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraTips")
    AlgorithmResult<GetTipsReturnDTO> sendTipsData(@Body TipsRequest tipsRequest);

    /**
     * 长周期确认
     *
     * @param longerPeriodRequest 请求参数
     * @return LongerPeriodReturnDTO
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraLongerEdit")
    AlgorithmResult<LongerPeriodReturnDTO> sendLongerEditData(@Body LongerPeriodRequest longerPeriodRequest);

    /**
     * Report
     *
     * @param reportRequest 请求参数
     * @return ReportReturnDTO
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraReport")
    AlgorithmResult<ReportReturnDTO> sendReportData(@Body ReportRequest reportRequest);

    /**
     * 更年期算法返回结果
     *
     * @param newHormoneDataRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @Headers({"Connection: close"})
    @POST("/algorithm/miraMenopause")
    AlgorithmResult<AlgorithmMenopauseResultDTO> getMenopauseResult(@Body NewHormoneDataRequest newHormoneDataRequest);

}