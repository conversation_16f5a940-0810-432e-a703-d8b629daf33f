package com.mira.bluetooth.client;

import com.mira.api.bluetooth.dto.example.AlgorithmExampleDTO;
import com.mira.api.bluetooth.dto.report.ReportReturnDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.bluetooth.dto.algorithm.request.*;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmMenopauseResultDTO;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.dto.algorithm.response.LongerPeriodReturnDTO;
import retrofit2.http.Body;

/**
 * call client
 *
 * <AUTHOR>
 */
public interface CallClient {
    /**
     * 获取测试样例数据
     *
     * @param body 请求参数
     * @return String
     */
    AlgorithmResult<AlgorithmExampleDTO> getExampleData(String body);

    /**
     * 新测试数据
     *
     * @param newHormoneDataRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    AlgorithmResult<AlgorithmReturnDTO> sendNewHormoneData(NewHormoneDataRequest newHormoneDataRequest);

    /**
     * 编辑经期
     *
     * @param editPeriodRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    AlgorithmResult<AlgorithmReturnDTO> sendEditPeriodData(EditPeriodRequest editPeriodRequest);

    /**
     * 获取Tips
     *
     * @param tipsRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    AlgorithmResult<GetTipsReturnDTO> sendTipsData(TipsRequest tipsRequest);

    /**
     * 长周期确认
     *
     * @param longerPeriodRequest 请求参数
     * @return LongerPeriodReturnDTO
     */
    AlgorithmResult<LongerPeriodReturnDTO> sendLongerEditData(LongerPeriodRequest longerPeriodRequest);

    /**
     * Report
     *
     * @param reportRequest 请求参数
     * @return ReportReturnDTO
     */
    AlgorithmResult<ReportReturnDTO> sendReportData(ReportRequest reportRequest);

    /**
     * 更年期算法返回结果
     *
     * @param newHormoneDataRequest
     * @return AlgorithmMenopauseResultDTO
     */
    AlgorithmResult<AlgorithmMenopauseResultDTO> getMenopauseResult(@Body NewHormoneDataRequest newHormoneDataRequest);
}
