package com.mira.bluetooth.client;

import com.mira.api.bluetooth.dto.example.AlgorithmExampleDTO;
import com.mira.api.bluetooth.dto.report.ReportReturnDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.bluetooth.dto.algorithm.request.*;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmMenopauseResultDTO;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.dto.algorithm.response.LongerPeriodReturnDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * python provider
 *
 * <AUTHOR>
 */
@FeignClient(name = "algorithm-server", contextId = "python")
public interface AlgorithmNacosClient extends CallClient {
    /**
     * 获取测试样例数据
     *
     * @param body 请求参数
     * @return String
     */
    @PostMapping("/algorithm/miraSampleHormone")
    AlgorithmResult<AlgorithmExampleDTO> getExampleData(String body);

    /**
     * 新测试数据
     *
     * @param newHormoneDataRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @PostMapping("/algorithm/miraNewHormone")
    AlgorithmResult<AlgorithmReturnDTO> sendNewHormoneData(@RequestBody NewHormoneDataRequest newHormoneDataRequest);

    /**
     * 编辑经期
     *
     * @param editPeriodRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @PostMapping("/algorithm/miraEditPeriod")
    AlgorithmResult<AlgorithmReturnDTO> sendEditPeriodData(@RequestBody EditPeriodRequest editPeriodRequest);

    /**
     * 获取Tips
     *
     * @param tipsRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @PostMapping("/algorithm/miraTips")
    AlgorithmResult<GetTipsReturnDTO> sendTipsData(@RequestBody TipsRequest tipsRequest);

    /**
     * 长周期确认
     *
     * @param longerPeriodRequest 请求参数
     * @return LongerPeriodReturnDTO
     */
    @PostMapping("/algorithm/miraLongerEdit")
    AlgorithmResult<LongerPeriodReturnDTO> sendLongerEditData(@RequestBody LongerPeriodRequest longerPeriodRequest);

    /**
     * Report
     *
     * @param reportRequest 请求参数
     * @return ReportReturnDTO
     */
    @PostMapping("/algorithm/miraReport")
    AlgorithmResult<ReportReturnDTO> sendReportData(@RequestBody ReportRequest reportRequest);

    /**
     * 更年期算法返回结果
     *
     * @param newHormoneDataRequest 请求参数
     * @return AlgorithmReturnDataDTO
     */
    @PostMapping("/algorithm/miraMenopause")
    AlgorithmResult<AlgorithmMenopauseResultDTO> getMenopauseResult(@RequestBody NewHormoneDataRequest newHormoneDataRequest);

}
