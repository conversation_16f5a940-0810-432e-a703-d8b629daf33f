package com.mira.bluetooth;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@RetrofitScan(basePackages = "com.mira.bluetooth.client")
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = "com.mira")
@EnableDiscoveryClient
@ComponentScan(basePackages = "com.mira")
@SpringBootApplication
public class BluetoothApplication {

    public static void main(String[] args) {
        SpringApplication.run(BluetoothApplication.class, args);
    }

}
