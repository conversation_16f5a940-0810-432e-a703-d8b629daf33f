package com.mira.bluetooth.enums.algorithm;

import lombok.Getter;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AlgorithmUserTypeEnum {
    NORMAL_USER(0, "正常用户"),
    NEW_USER(1, "新用户"),
    NO_PERIOD_USER(2, "没有经期的用户"),
    VARIES_OFTEN_PERIOD_USER(3, "经期变动频繁的用户"),
    NO_CYCLE_USER(4, "没有周期的用户"),
    VARIES_OFTEN_CYCLE_USER(5, "周期变动频繁的用户");

    private final int type;
    private final String desc;

    AlgorithmUserTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
