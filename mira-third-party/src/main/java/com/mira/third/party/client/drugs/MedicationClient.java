package com.mira.third.party.client.drugs;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-04
 **/
@RetrofitClient(baseUrl = "https://www.drugs.com", readTimeoutMs = 60000, connectTimeoutMs = 15000)
public interface MedicationClient {
    @GET("/js/search/?id=livesearch-main")
    String search(@Query("s") String keyword);

    @GET("/api/autocomplete/?id=livesearch-main&data-autocomplete=main")
    DrugsResponse searchV2(@Query("s") String keyword);
}
