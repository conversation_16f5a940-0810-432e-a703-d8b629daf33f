package com.mira.third.party.client.kustomer;

import lombok.Getter;
import lombok.Setter;

/**
 * kustomer 响应接收类
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class KustomerResult {
    private Data data;

    @Getter
    @Setter
    public static class Data {
        private String id;
        private Attributes attributes;
    }

    @Getter
    @Setter
    public static class Attributes {
        private String name;
        // e.g. 2022-10-24T07:58:44.188Z
        private String createdAt;
        private String expireAt;
        private String token;
    }
}
