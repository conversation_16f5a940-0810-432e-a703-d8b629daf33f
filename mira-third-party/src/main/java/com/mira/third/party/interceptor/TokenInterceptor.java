package com.mira.third.party.interceptor;

import com.mira.api.user.provider.IUserProvider;
import com.mira.core.annotation.Anonymous;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JwtUtil;
import com.mira.third.party.exception.ThirdPartyException;
import com.mira.web.properties.JwtProperties;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * oauth token interceptor
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
public class TokenInterceptor implements HandlerInterceptor {
    @Resource
    private JwtProperties jwtProperties;

    @Resource
    private IUserProvider userProvider;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!handler.getClass().isAssignableFrom(HandlerMethod.class)) {
            return true;
        }

        // 匿名接口放行
        Method method = ((HandlerMethod) handler).getMethod();
        if (method.isAnnotationPresent(Anonymous.class)) {
            return true;
        }
        if (Objects.nonNull(request.getHeader(HeaderConst.ANONYMOUS))) {
            return true;
        }

        // check
        String authorization = request.getHeader(HeaderConst.AUTHORIZATION);
        if (StringUtils.isNotEmpty(authorization)) {
            Claims claims = JwtUtil.parseClaims(authorization, jwtProperties.getSignKey());
            if (Objects.isNull(claims)) {
                throw new ThirdPartyException(500, "authentication failure.");
            }
            return true;
        }

        throw new ThirdPartyException(500, "authentication failure.");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ContextHolder.removeAll();
    }
}
