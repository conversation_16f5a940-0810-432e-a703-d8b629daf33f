package com.mira.sso.controller;

import com.mira.api.user.consts.OperateLogConst;
import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.Idempotent;
import com.mira.core.annotation.IgnoreLog;
import com.mira.core.annotation.OperateLog;
import com.mira.sso.consts.UserChannelConst;
import com.mira.sso.consts.UserFlagConst;
import com.mira.sso.controller.vo.RegisterResultVO;
import com.mira.sso.service.IRegisterService;
import com.mira.sso.service.dto.UserRegisterDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 注册相关控制器
 * <p>app/v4前缀的url是兼容老版本
 *
 * <AUTHOR>
 */
@Api(tags = "02.用户注册")
@RestController
public class RegisterController {
    @Resource
    private IRegisterService registerService;

    @Anonymous
    @Idempotent(returnValue = "RegisterResultVO")
    @OperateLog(operate = OperateLogConst.PRIVACY_AGREEMENT_UPDATE)
    @ApiOperation("用户注册")
    @PostMapping("sso/user/mail/register")
    public RegisterResultVO register(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        return registerService.register(userRegisterDTO, UserFlagConst.REGISTER_FLAG, UserChannelConst.APP);
    }

    @Anonymous
    @Idempotent(returnValue = "RegisterResultVO")
    @OperateLog(operate = OperateLogConst.PRIVACY_AGREEMENT_UPDATE)
    @ApiOperation("用户注册再次发送")
    @PostMapping("sso/user/mail/register/resend")
    public RegisterResultVO registerResend(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        return registerService.register(userRegisterDTO, UserFlagConst.REGISTER_RESEND_FLAG, UserChannelConst.APP);
    }

    @Anonymous
    @Idempotent
    @ApiOperation("确认注册")
    @PostMapping("sso/user/mail/activation")
    public String confirmRegister(@RequestParam String hash) {
        return registerService.confirmRegister(hash);
    }

    @Anonymous
    @IgnoreLog
    @ApiOperation("获取用户激活状态")
    @PostMapping("sso/user/mail/enable-status")
    public String queryEnableStatus(@RequestParam String email) {
        return registerService.queryEnableStatus(email);
    }

    // ---------------------------------------------

    @Anonymous
    @OperateLog(operate = OperateLogConst.PRIVACY_AGREEMENT_UPDATE)
    @ApiOperation("用户注册（兼容）")
    @PostMapping("app/v4/mail/register")
    public RegisterResultVO CompatibleRegister(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        return registerService.register(userRegisterDTO, UserFlagConst.REGISTER_FLAG, UserChannelConst.APP);
    }

    @Anonymous
    @OperateLog(operate = OperateLogConst.PRIVACY_AGREEMENT_UPDATE)
    @ApiOperation("用户注册再次发送（兼容）")
    @PostMapping("app/v4/mail/register/resend")
    public RegisterResultVO CompatibleRegisterResend(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        return registerService.register(userRegisterDTO, UserFlagConst.REGISTER_RESEND_FLAG, UserChannelConst.APP);
    }

    @Anonymous
    @ApiOperation("确认注册（兼容）")
    @PostMapping("app/v4/mail/activation")
    public String CompatibleConfirmRegister(@RequestParam String hash) {
        return registerService.confirmRegister(hash);
    }

    @Anonymous
    @IgnoreLog
    @ApiOperation("获取用户激活状态（兼容）")
    @PostMapping("app/v4/mail/enable-status")
    public String CompatibleQueryEnableStatus(@RequestParam String email) {
        return registerService.queryEnableStatus(email);
    }

}
