package com.mira.backend.controller;

import com.mira.api.clinic.dto.*;
import com.mira.backend.config.logs.AdminLog;
import com.mira.backend.dto.LoginAdminUserDTO;
import com.mira.backend.exception.BackendException;
import com.mira.backend.service.IAdminLoginService;
import com.mira.backend.service.IClinicService;
import com.mira.backend.util.PasswordGenerator;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 诊所管理
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-04
 **/
@Api(tags = "12.诊所管理")
@RestController
@RequestMapping("clinic")
@Slf4j
public class ClinicController {
    @Resource
    private IClinicService clinicService;
    @Resource
    private IAdminLoginService adminLoginService;

    @ApiOperation("1.init create clinic")
    @PostMapping("/create-clinic")
    public void initCreateClinic(@Valid @RequestBody InitCreateClinicDTO initCreateClinicDTO) {
        clinicService.initCreateClinic(initCreateClinicDTO);
    }

    @ApiOperation("2.Password Generator")
    @PostMapping("/password-generator")
    public String passwordGenerator() {
        String password = PasswordGenerator.generatePassword();
        log.info("Password: {}", password);
        return password;
    }

    @ApiOperation("3.clinic分页列表")
    @PostMapping("/clinic-page")
    public PageResult<ClinicListDTO> dataPage(@RequestBody ClinicPageRequestDTO pageParam) {
        ClinicPageResponseDTO clinicPageResponseDTO = clinicService.clinicPage(pageParam);
        return new PageResult<>(clinicPageResponseDTO.getClinicListDTOS(), clinicPageResponseDTO.getTotal(), pageParam.getSize(),
                pageParam.getCurrent());
    }

    @ApiOperation("4.edit clinic info")
    @PostMapping("/edit-clinic")
    public void editClinicInfo(@Valid @RequestBody EditClinicDTO editClinicDTO) {
        clinicService.editClinicInfo(editClinicDTO);
    }

    @ApiOperation("5.delete clinic info")
    @PostMapping("/delete-clinic")
    public void deleteClinicInfo(@RequestParam("id") Long id) {
        clinicService.deleteClinicInfo(id);
    }

    @ApiOperation("5.reset clinic password")
    @PostMapping("/reset-pw")
    @AdminLog("reset clinic password")
    public void resetClinicPassword(@RequestParam("email") String email) {
        LoginAdminUserDTO adminUserDTO = adminLoginService.adminInfo();
        Set<String> permissionCodes = adminUserDTO.getPermissionCodes();
        if (!permissionCodes.contains("menu:clinicResetPassword")) {
            throw new BackendException("without permission: clinicResetPassword");
        }
        clinicService.resetClinicPassword(email);
    }
}
