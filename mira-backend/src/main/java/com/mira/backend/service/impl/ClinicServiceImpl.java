package com.mira.backend.service.impl;

import com.mira.api.clinic.dto.*;
import com.mira.api.clinic.provider.IClinicProvider;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.api.user.dto.backend.HomeBannerDTO;
import com.mira.backend.dal.dao.admin.AdminNotificationJobDAO;
import com.mira.backend.dal.dao.user.SysHomeBannerDAO;
import com.mira.backend.dal.entity.admin.AdminNotificationJobEntity;
import com.mira.backend.dal.entity.user.SysHomeBannerEntity;
import com.mira.backend.exception.BackendException;
import com.mira.backend.service.IClinicService;
import com.mira.core.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desk clinic service impl
 *
 * <AUTHOR>
 */
@Service
public class ClinicServiceImpl implements IClinicService {
    @Resource
    private SysHomeBannerDAO sysHomeBannerDAO;
    @Resource
    private AdminNotificationJobDAO adminNotificationJobDAO;
    @Resource
    private IClinicProvider clinicProvider;

    @Override
    public void initCreateClinic(InitCreateClinicDTO initCreateClinicDTO) {
        clinicProvider.initCreateClinic(initCreateClinicDTO);
    }

    @Override
    public ClinicPageResponseDTO clinicPage(ClinicPageRequestDTO clinicPageRequestDTO) {
        Integer current = clinicPageRequestDTO.getCurrent();
        Integer size = clinicPageRequestDTO.getSize();

        //bannerGroupUid 给mira desk custom log使用
        String bannerGroupUid = clinicPageRequestDTO.getBannerGroupUid();
        if (StringUtils.isNotBlank(bannerGroupUid)) {
            List<ClinicListDTO> allClinicList = clinicProvider.allClinic().getData();
            List<ClinicListDTO> result = sortClinicPageByBanner(bannerGroupUid, allClinicList);
            ClinicPageResponseDTO clinicPageResponseDTO = new ClinicPageResponseDTO();
            clinicPageResponseDTO.setTotal(allClinicList.size());
            clinicPageResponseDTO.setClinicListDTOS(clinicListByPage(result, current, size));
            return clinicPageResponseDTO;
        }
        //taskId 给mira desk custom log使用
        String taskId = clinicPageRequestDTO.getTaskId();
        if (StringUtils.isNotBlank(taskId)) {
            List<ClinicListDTO> allClinicList = clinicProvider.allClinic().getData();
            List<ClinicListDTO> result = sortClinicPageByNotification(taskId, allClinicList);
            ClinicPageResponseDTO clinicPageResponseDTO = new ClinicPageResponseDTO();
            clinicPageResponseDTO.setTotal(allClinicList.size());
            clinicPageResponseDTO.setClinicListDTOS(clinicListByPage(result, current, size));
            return clinicPageResponseDTO;
        }

        return clinicProvider.clinicPage(clinicPageRequestDTO).getData();
    }

    private List<ClinicListDTO> sortClinicPageByBanner(String bannerGroupUid,
                                                       List<ClinicListDTO> allClinicList) {
        List<ClinicListDTO> result = new ArrayList<>();

        // query banner entity
        List<SysHomeBannerEntity> bannerEntityList = sysHomeBannerDAO.findByGroupId(bannerGroupUid);
        if (CollectionUtils.isEmpty(bannerEntityList)) {
            return allClinicList;
        }
        SysHomeBannerEntity bannerEntity = bannerEntityList.get(0);
        String clinics = bannerEntity.getClinics();
        if (StringUtils.isBlank(clinics)) {
            return allClinicList;
        }

        // convert to map
        Map<Long, ClinicListDTO> idToClinicMap = allClinicList.stream()
                .collect(Collectors.toMap(ClinicListDTO::getId, Function.identity()));

        // add to the result and in clinicIdList
        List<Long> clinicIdList = JsonUtil.toArray(clinics, HomeBannerDTO.ClinicDTO.class)
                .stream().map(HomeBannerDTO.ClinicDTO::getId).collect(Collectors.toList());
        for (Long clinicId : clinicIdList) {
            ClinicListDTO clinicListDTO = idToClinicMap.get(clinicId);
            if (clinicListDTO != null) {
                result.add(clinicListDTO);
            }
        }

        // add to the result and not in clinicIdList
        Set<Long> clinicIdSet = new HashSet<>(clinicIdList);
        for (ClinicListDTO clinicListDTO : allClinicList) {
            if (!clinicIdSet.contains(clinicListDTO.getId())) {
                result.add(clinicListDTO);
            }
        }

        return result;
    }

    private List<ClinicListDTO> sortClinicPageByNotification(String taskId,
                                                             List<ClinicListDTO> allClinicList) {
        List<ClinicListDTO> result = new ArrayList<>();

        // query admin job entity
        AdminNotificationJobEntity notificationJobEntity = adminNotificationJobDAO.getByTaskId(taskId);
        String jobJson = notificationJobEntity.getJobJson();
        if (StringUtils.isBlank(jobJson)) {
            return allClinicList;
        }

        // convert to NotificationPushCreateDTO
        NotificationPushCreateDTO pushCreateDTO = JsonUtil.toObject(jobJson, NotificationPushCreateDTO.class);
        List<NotificationPushCreateDTO.ClinicDTO> clinics = pushCreateDTO.getClinics();
        if (CollectionUtils.isEmpty(clinics)) {
            return allClinicList;
        }
        Long[] clinicIdArr = clinics.stream().map(NotificationPushCreateDTO.ClinicDTO::getId).distinct().toArray(Long[]::new);

        // clinic id list
        List<Long> clinicIdList = List.of(clinicIdArr);

        // convert to map
        Map<Long, ClinicListDTO> idToClinicMap = allClinicList.stream()
                .collect(Collectors.toMap(ClinicListDTO::getId, Function.identity()));

        // add to the result and in clinicIdList
        for (Long clinicId : clinicIdList) {
            ClinicListDTO clinicListDTO = idToClinicMap.get(clinicId);
            if (clinicListDTO != null) {
                result.add(clinicListDTO);
            }
        }

        // add to the result and not in clinicIdList
        for (ClinicListDTO clinicListDTO : allClinicList) {
            if (!clinicIdList.contains(clinicListDTO.getId())) {
                result.add(clinicListDTO);
            }
        }

        return result;
    }

    private List<ClinicListDTO> clinicListByPage(List<ClinicListDTO> clinicList,
                                                 Integer current, Integer size) {
        // 传入的参数约定为从1开始
        current = current - 1;
        int offset = (current == 0) ? 0 : current * size;

        if (current < 0 && size <= 0) {
            return Collections.emptyList();
        }
        if (offset >= clinicList.size()) {
            return Collections.emptyList();
        }

        int endIndex = Math.min(offset + size, clinicList.size());

        return clinicList.subList(offset, endIndex);
    }

    @Override
    public void editClinicInfo(EditClinicDTO editClinicDTO) {
        clinicProvider.editClinicInfo(editClinicDTO);
    }

    @Override
    public void deleteClinicInfo(Long id) {
        clinicProvider.deleteClinicInfo(id);
    }

    @Override
    public void resetClinicPassword(String email) {
        String errMessage = clinicProvider.resetClinicPassword(email).getData();
        if (StringUtils.isNotBlank(errMessage)) {
            throw new BackendException(errMessage);
        }
    }
}
