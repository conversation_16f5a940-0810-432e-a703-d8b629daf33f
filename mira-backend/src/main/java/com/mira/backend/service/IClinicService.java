package com.mira.backend.service;

import com.mira.api.clinic.dto.ClinicPageRequestDTO;
import com.mira.api.clinic.dto.ClinicPageResponseDTO;
import com.mira.api.clinic.dto.EditClinicDTO;
import com.mira.api.clinic.dto.InitCreateClinicDTO;

/**
 * desk clinic service
 *
 * <AUTHOR>
 */
public interface IClinicService {
    void initCreateClinic(InitCreateClinicDTO initCreateClinicDTO);

    ClinicPageResponseDTO clinicPage(ClinicPageRequestDTO clinicPageRequestDTO);

    void editClinicInfo(EditClinicDTO editClinicDTO);

    void deleteClinicInfo(Long id);

    void resetClinicPassword(String email);
}
