package com.mira.message.service.provider;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.message.dto.*;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.dto.user.AppPartnerDTO;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.response.CommonResult;
import com.mira.message.service.IEmailService;
import com.mira.message.service.INotificationRecordService;
import com.mira.message.service.IRateMenopauseLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 消息通知服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class MessageProvider implements IMessageProvider {
    @Resource
    private IEmailService emailService;
    @Resource
    private INotificationRecordService notificationRecordService;
    @Resource
    private IRateMenopauseLogService rateMenopauseLogService;

    @Override
    public CommonResult<Boolean> sendEmail(SendEmailDTO<?> sendEmailDTO) {
        if (StringUtils.isBlank(sendEmailDTO.getUserType())) {
            CommonEmailDTO commonEmailDTO = BeanUtil.toBean(sendEmailDTO.getEmailDTO(), CommonEmailDTO.class);
            return CommonResult.OK(emailService.sendEmail(commonEmailDTO));
        }

        UserTypeEnum userTypeEnum = UserTypeEnum.get(sendEmailDTO.getUserType());
        boolean result = false;
        switch (userTypeEnum) {
            case APP_USER:
                AppUserEmailDTO appUserEmailDTO = BeanUtil.toBean(sendEmailDTO.getEmailDTO(), AppUserEmailDTO.class);
                AppUserDTO appUserDTO = appUserEmailDTO.getAppUserDTO();
                result = emailService.sendEmail(appUserDTO, appUserEmailDTO.getEmailTypeEnum(), appUserEmailDTO.getEmailVariable());
                break;
            case PARTNER_USER:
                PartnerEmailDTO partnerEmailDTO = BeanUtil.toBean(sendEmailDTO.getEmailDTO(), PartnerEmailDTO.class);
                AppPartnerDTO partnerDTO = partnerEmailDTO.getAppPartnerDTO();
                result = emailService.sendEmail(partnerDTO, partnerEmailDTO.getEmailTypeEnum(), partnerEmailDTO.getEmailVariable());
                break;
            case CLINIC_USER:
                CommonEmailDTO commonEmailDTO = BeanUtil.toBean(sendEmailDTO.getEmailDTO(), CommonEmailDTO.class);
                result = emailService.sendEmail(commonEmailDTO);
                break;
        }

        return CommonResult.OK(result);
    }

    @Override
    public CommonResult<Boolean> sendNotification(PushNotificationDTO pushNotificationDTO) {
        notificationRecordService.firebaseAndRecord(pushNotificationDTO);
        return CommonResult.OK(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> updateNotificationStatistics(StatisticsNotificationDTO statisticsNotificationDTO) {
        notificationRecordService.statistics(statisticsNotificationDTO);
        return CommonResult.OK(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> isAllRead(Long userId) {
        return CommonResult.OK(notificationRecordService.isAllRead(userId));
    }

    @Override
    public CommonResult<PageNotificationRecordDTO> getNotificationList(Long userId, Integer current, Integer size) {
        return CommonResult.OK(notificationRecordService.getNotificationList(userId, current, size));
    }

    @Override
    public CommonResult<PageNotificationRecordDTO> getNotificationList(QueryNotificationDTO queryNotificationDTO) {
        return CommonResult.OK(notificationRecordService.getNotificationList(queryNotificationDTO));
    }

    @Override
    public CommonResult<ClinicPageNotificationRecordDTO> getNotificationListByClinic(Long userId, String tenantCode, Integer current, Integer size) {
        return CommonResult.OK(notificationRecordService.getNotificationListByClinic(userId, tenantCode, current, size));
    }

    @Override
    public CommonResult<SysNotificationRecordDTO> getSilent(Long userId) {
        return CommonResult.OK(notificationRecordService.getSilent(userId));
    }

    @Override
    public CommonResult<String> updateAllRead(Long userId) {
        notificationRecordService.updateAllRead(userId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> updateRead(Long userId, Long recordId) {
        notificationRecordService.updateRead(userId, recordId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> updateUnRead(Long userId, Long recordId) {
        notificationRecordService.updateUnRead(userId, recordId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> updateReadByDefinedId(Long userId, Long definedId) {
        notificationRecordService.updateReadByDefineId(userId, definedId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> rateTheApp(Long userId, Integer score) {
        notificationRecordService.rateTheApp(userId, score);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> expireNotification(Long userId, Long definedId) {
        notificationRecordService.expireNotification(userId, definedId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> delete(Long recordId) {
        notificationRecordService.delete(recordId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<SysNotificationRecordDTO> supply(Long userId, Integer aggregateType, Long recordId) {
        return CommonResult.OK(notificationRecordService.supply(userId, aggregateType, recordId));
    }

    @Override
    public CommonResult<String> rateMenopause(Long userId, Integer score) {
        rateMenopauseLogService.rateMenopause(userId, score);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Integer> getRateMenopauseScore(Long userId) {
        return CommonResult.OK(rateMenopauseLogService.getRateMenopauseScore(userId));
    }

    @Override
    public CommonResult<String> rateByNotification(NotificationRateApiDTO notificationRateApiDTO) {
        notificationRecordService.rateByNotification(notificationRateApiDTO);
        return CommonResult.OK();
    }
}
