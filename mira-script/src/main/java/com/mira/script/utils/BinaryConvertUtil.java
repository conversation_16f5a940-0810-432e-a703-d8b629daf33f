package com.mira.script.utils;

import org.apache.commons.lang3.StringUtils;

public class BinaryConvertUtil {
    public static String toWandBatch2(String uBatch) {
        int value = Integer.parseInt(uBatch);
        return Integer.toHexString(value);
    }

    public static String toWandBatch3(String wandBatch2) {
        if (StringUtils.isBlank(wandBatch2)) {
            return null;
        }
        int year = Integer.parseInt(wandBatch2.substring(0, 2), 16);
        int month = Integer.parseInt(wandBatch2.substring(2, 4), 16);
        int code = Integer.parseInt(wandBatch2.substring(4), 16);
        String wandBatch3 = "20" + (year < 10 ? "0" + year : year) + (month < 10 ? "0" + month : month);

        if (code < 10) {
            wandBatch3 += "000" + code;
        } else if (code < 100) {
            wandBatch3 += "00" + code;
        } else if (code < 1000) {
            wandBatch3 += "0" + code;
        } else {
            wandBatch3 += code;
        }

        return wandBatch3;
    }
}
