package com.mira.script.controller;

import com.mira.script.service.hub.HubDuquesneUserAlgorithmService;
import com.mira.script.service.hub.HubDuquesneUserDiaryLogService;
import com.mira.script.service.hub.HubDuquesneUserInfoService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-22
 **/
@Api(tags = "02.Duquesne hub info")
@RestController
@RequestMapping("script/duquesne")
@Slf4j
public class HubDuquesneController {
    @Resource
    private HubDuquesneUserInfoService hubDuquesneUserInfoService;
    @Resource
    private HubDuquesneUserDiaryLogService hubDuquesneUserDiaryLogService;
    @Resource
    private HubDuquesneUserAlgorithmService hubDuquesneUserAlgorithmService;

    @PostMapping("/transfer-hub-info")
    public void transferHubInfo() {
        hubDuquesneUserInfoService.transferHubInfo();
    }

    @PostMapping("/transfer-hub-diary-log")
    public void transferHubDiaryLog() {
        hubDuquesneUserDiaryLogService.transferHubDiaryLog();
    }

    @PostMapping("/transfer-hub-hormone-and-cycle")
    public void transferHubHormoneAndCycle() {
        hubDuquesneUserAlgorithmService.transferHubHormoneAndCycle();
    }
}
