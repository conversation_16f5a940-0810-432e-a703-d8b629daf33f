package com.mira.script.service.app.impl;

import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.script.dal.dao.app.AppDataUploadDAO;
import com.mira.script.dal.entity.app.AppDataUploadEntity;
import com.mira.script.service.app.IDataUploadService;
import com.mira.script.service.app.helper.RunBoardCheckDataHelper;
import com.mira.script.service.app.helper.WandsParamRecordHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-14
 **/
@Slf4j
@Service
public class DataUploadServiceImpl implements IDataUploadService {
    @Resource
    private AppDataUploadDAO appDataUploadDAO;
    @Resource
    private WandsParamRecordHelper wandsParamRecordHelper;
    @Resource
    private RunBoardCheckDataHelper runBoardCheckDataHelper;

    @Override
    public void processRunBoardCheck() {
        long cycle = 1; // 第几次查询
        long pageSize = 0; // 分页参数
        long testingDataCount = appDataUploadDAO.countRunBoardCheckEntity();
        // 分批查询
        int queryBatch = 1000;
        long cdCount = testingDataCount % queryBatch == 0 ? testingDataCount / queryBatch : testingDataCount / queryBatch + 1;
        log.info("查询到的需要跑板异常校验的数据条数:【{}】，分批:【{}】", testingDataCount, cdCount);
        CountDownLatch cd = new CountDownLatch((int) cdCount);
        while (testingDataCount > 0) {
            // app_data_upload
            String sql = "select a.* " +
                    "from app_data_upload a " +
                    "join (select id from app_data_upload where deleted=0 and error='00' " +
                    "and warning in ('00', '05','09') and auto_flag=0 and run_board_flag is null " +
                    "limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";

            long finalCycle = cycle;
            long finalTestingDataCount = testingDataCount;
            CompletableFuture.runAsync(
                    () -> {
                        List<AppDataUploadEntity> dataUploadList = appDataUploadDAO.listBySql(sql, finalCycle, finalTestingDataCount);
                        try {
                            if (CollectionUtils.isNotEmpty(dataUploadList)) {
                                processDataUploadList(dataUploadList, finalCycle);
                            }
                        } finally {
                            cd.countDown();
                        }
                    },
                    ThreadPoolUtil.getPool()
            ).exceptionally(ex -> {
                log.error("getAllUser error occurred, sql:{}", sql, ex);
                return null;
            });
            cycle++;
            pageSize += queryBatch;
            testingDataCount -= queryBatch;
        }

        // wait future
        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }
        log.info("结束执行processRunBoardCheck,共执行【{}】条数据", testingDataCount);
    }

    private void processDataUploadList(List<AppDataUploadEntity> dataUploadList, long cycle) {
        log.info("开始处理第【{}】批测试数据的跑板异常校验,共【{}】条", cycle, dataUploadList.size());
        List<AppDataUploadEntity> updateList = new ArrayList<>();
        for (AppDataUploadEntity dataUploadEntity : dataUploadList) {
            int runBoardFlag;
            String testWandType = dataUploadEntity.getTestWandType();
            WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(dataUploadEntity.getTestWandBatch(),
                    testWandType.startsWith("0") ? testWandType.substring(1) : testWandType);
            // step3:跑板异常校验，跑板失败数据需要展示(加入到daily data)，但不调用算法(flag==0)
            //进入方法的数据一定是warning 00 05   error 00
            boolean checkDataBool = runBoardCheckDataHelper.checkData(dataUploadEntity, wandsParamRecordDTO);
            if (checkDataBool) {
                runBoardFlag = 1;
            } else {
                runBoardFlag = 2;
            }
            dataUploadEntity.setRunBoardFlag(runBoardFlag);
            updateList.add(dataUploadEntity);
        }
        appDataUploadDAO.updateBatchById(updateList);
        log.info("结束处理第【{}】批测试数据的跑板异常校验,共【{}】条", cycle, dataUploadList.size());
    }
}
