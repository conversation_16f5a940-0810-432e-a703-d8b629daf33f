package com.mira.script.service.app.wand;


import com.mira.script.dal.dao.app.WandsParamRecordDAO;
import com.mira.script.dal.dao.factory.WandsParamDAO;
import com.mira.script.dal.entity.app.WandsParamRecordEntity;
import com.mira.script.dal.entity.factory.WandsParamEntity;
import com.mira.script.utils.BinaryConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

@SuppressWarnings("all")
@Slf4j
@Component
public class WandsParamSchedule {
    @Resource
    private WandsParamDAO wandsParamDAO;
    @Resource
    private WandsParamRecordDAO wandsParamRecordDAO;

    public void execute() {
        log.info("开始执行。");
        run();
        log.info("执行成功。");
    }


    private void run() {
        // 取出最新时间
        Long maxId = wandsParamRecordDAO.getMaxId();
        if (maxId == null) {
            maxId = 0L;
        }
        // factory datasoucre
        List<WandsParamEntity> distinctWandsParamEntities = wandsParamDAO.listDistinctUBatchByMaxId(maxId);
        log.info("distinctWandsParamEntities.size:{}", distinctWandsParamEntities.size());

        if (!CollectionUtils.isEmpty(distinctWandsParamEntities)) {
            for (WandsParamEntity distinctWandsParamEntity : distinctWandsParamEntities) {
                String uBatch = distinctWandsParamEntity.getUBatch();
                String uStripType = distinctWandsParamEntity.getUStripType();
                log.info("开始执行: uBatch:{}, uStripType:{}", uBatch, uStripType);
                List<WandsParamEntity> wandsParamEntities = wandsParamDAO.listByUBatchAndType(uBatch, uStripType);
                WandsParamEntity wandsParamEntity = wandsParamEntities.stream().max(Comparator.comparing(WandsParamEntity::getUStampManufacture)).get();
                // mira datasource
                WandsParamRecordEntity wandsParamRecordEntity = wandsParamRecordDAO.getByUBatchAndType(uBatch, uStripType);
                if (wandsParamRecordEntity == null) {
                    wandsParamRecordEntity = new WandsParamRecordEntity();
                    BeanUtils.copyProperties(wandsParamEntity, wandsParamRecordEntity);
                    wandsParamRecordEntity.setMaxId(wandsParamEntity.getId());
                    wandsParamRecordEntity.setBatchSize(wandsParamEntities.size());
                    String uStampManufacture = wandsParamEntity.getUStampManufacture();
                    String uStampManufactureStr = getDtfChinaTime(Long.parseLong(uStampManufacture + "000"));
                    wandsParamRecordEntity.setUStampManufactureStr(uStampManufactureStr);
                    wandsParamRecordEntity.setWandBatch2(BinaryConvertUtil.toWandBatch2(uBatch));
                    wandsParamRecordEntity.setWandBatch3(BinaryConvertUtil.toWandBatch3(wandsParamRecordEntity.getWandBatch2()));
                    wandsParamRecordEntity.setAsyncTime(new Date());
                    wandsParamRecordDAO.save(wandsParamRecordEntity);
                } else {
                    BeanUtils.copyProperties(wandsParamEntity, wandsParamRecordEntity);
                    wandsParamRecordEntity.setMaxId(wandsParamEntity.getId());
                    wandsParamRecordEntity.setBatchSize(wandsParamEntities.size());
                    String uStampManufacture = wandsParamEntity.getUStampManufacture();
                    String uStampManufactureStr = getDtfChinaTime(Long.parseLong(uStampManufacture + "000"));
                    wandsParamRecordEntity.setUStampManufactureStr(uStampManufactureStr);
                    wandsParamRecordEntity.setWandBatch2(BinaryConvertUtil.toWandBatch2(uBatch));
                    wandsParamRecordEntity.setWandBatch3(BinaryConvertUtil.toWandBatch3(wandsParamRecordEntity.getWandBatch2()));
                    wandsParamRecordEntity.setAsyncTime(new Date());
                    wandsParamRecordDAO.update(wandsParamRecordEntity);
                }
            }
        }
    }

    public static String getDtfChinaTime(long localTime) {
        TimeZone chinaZone = TimeZone.getTimeZone("Asia/Shanghai");
        SimpleDateFormat dtf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dtf.setTimeZone(chinaZone);
        String localtimeStr = dtf.format(localTime);
        return localtimeStr;
    }
}

