package com.mira.script.service.app.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.script.dal.dao.app.AppPregnantModeInfoDAO;
import com.mira.script.dal.dao.app.AppPregnantModeInfoV2DAO;
import com.mira.script.dal.entity.app.AppPregnantModeInfoEntity;
import com.mira.script.dal.entity.app.AppPregnantModeInfoV2Entity;
import com.mira.script.service.app.IPregnantModeDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-01
 **/
@Service
@Slf4j
public class PregnantModeDataServiceImpl implements IPregnantModeDataService {
    @Resource
    private AppPregnantModeInfoDAO appPregnantModeInfoDAO;
    @Resource
    private AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;

    @Override
    public void transferPregnantModeData() {
        List<AppPregnantModeInfoEntity> pregnantModeInfoEntities = appPregnantModeInfoDAO.list();
        log.info("pregnantModeInfoEntities size:{}", pregnantModeInfoEntities.size());

        List<AppPregnantModeInfoV2Entity> appPregnantModeInfoV2Entities = new ArrayList<AppPregnantModeInfoV2Entity>();

        pregnantModeInfoEntities.forEach(pregnantModeInfoEntity -> {
            AppPregnantModeInfoV2Entity appPregnantModeInfoV2Entity = new AppPregnantModeInfoV2Entity();
            BeanUtil.copyProperties(pregnantModeInfoEntity, appPregnantModeInfoV2Entity);
            appPregnantModeInfoV2Entities.add(appPregnantModeInfoV2Entity);
        });
        if (appPregnantModeInfoV2Entities.isEmpty()) {
            return;
        }
        log.info("开始插入数据");
        appPregnantModeInfoV2DAO.saveBatch(appPregnantModeInfoV2Entities);
        log.info("结束插入数据");
    }
}
