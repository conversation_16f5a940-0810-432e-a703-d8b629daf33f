package com.mira.script.dal.dao.factory;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.script.dal.entity.factory.WandsParamEntity;
import com.mira.script.dal.mapper.DataSourceName;
import com.mira.script.dal.mapper.factory.WandsParamMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@DS(DataSourceName.FACTORY)
@Repository
public class WandsParamDAO extends ServiceImpl<WandsParamMapper, WandsParamEntity> {
    public List<WandsParamEntity> listDistinctUBatch(String latestUStempTime) {
        return baseMapper.listDistinctUBatch(latestUStempTime);
    }

    public List<WandsParamEntity> listDistinctUBatchByMaxId(Long maxId) {
        return baseMapper.listDistinctUBatchByMaxId(maxId);
    }

    public List<WandsParamEntity> listByUBatchAndType(String uBatch, String uStripType) {
        return list(Wrappers.<WandsParamEntity>lambdaQuery()
                .eq(WandsParamEntity::getUBatch, uBatch)
                .eq(WandsParamEntity::getUStripType, uStripType));
    }
}
