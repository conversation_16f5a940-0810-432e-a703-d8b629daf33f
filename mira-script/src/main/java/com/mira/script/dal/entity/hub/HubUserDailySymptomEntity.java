package com.mira.script.dal.entity.hub;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.api.user.enums.daily.DailySymptomEnum;
import com.mira.api.user.enums.daily.DailySymptomLevelEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 用户每日身体异常状况记录
 * <p>
 * from app_user_diary_symptoms
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("hub_user_daily_symptom")
public class HubUserDailySymptomEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String hubId;


    /**
     * 日记时间（年月日）
     */
    private String dateStr;

    /**
     * @see DailySymptomEnum
     * @see DailySymptomLevelEnum
     */
    private String symptoms;

    /**
     * 修改时间
     */
    private Long modifyTime;


    /**
     * 同步时间
     */
    private Date syncTime;

    private Integer deleted;

}
