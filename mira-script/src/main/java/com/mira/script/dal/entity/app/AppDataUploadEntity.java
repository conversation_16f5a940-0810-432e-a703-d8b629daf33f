package com.mira.script.dal.entity.app;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.api.bluetooth.enums.RunBoardFlagEnum;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 蓝牙上传数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_data_upload")
public class AppDataUploadEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 序列号
     */
    @TableField("sn")
    private String sn;

    /**
     * 固件版本
     */
    @TableField("firmware_version")
    private String firmwareVersion;

    /**
     * 是否同步app(预留)
     */
    @TableField("update_server_flag")
    private Integer updateServerFlag;

    /**
     * 仪器第几次检测统计
     */
    @TableField("analyzer_number")
    private Integer analyzerNumber;

    /**
     * 结果版本
     */
    @TableField("result_version_format")
    private String resultVersionFormat;

    /**
     * 首次插入时间戳
     */
    @TableField("first_time_stamp")
    private String firstTimeStamp;

    /**
     * 试剂完成时间
     */
    @TableField("complete_time")
    private String completeTime;

    /**
     * 试剂完成时间戳
     */
    @TableField("complete_timestamp")
    private Long completeTimestamp;

    /**
     * 试剂类型
     */
    @TableField("test_wand_type")
    private String testWandType;

    /**
     * 试剂批次
     */
    @TableField("test_wand_batch")
    private String testWandBatch;

    /**
     * 批次编号
     */
    @TableField("batch_in_number")
    private String batchInNumber;

    /**
     * t1线峰高位置
     */
    @TableField("t1_peak_height_position")
    private BigDecimal t1PeakHeightPosition;

    /**
     * t1线绝对峰高
     */
    @TableField("t1_absolute_peak_height")
    private BigDecimal t1AbsolutePeakHeight;

    /**
     * t1线峰面积
     */
    @TableField("t1_peak_area")
    private BigDecimal t1PeakArea;

    /**
     * t1浓度值
     */
    @TableField("t1_con_value")
    private BigDecimal t1ConValue;

    /**
     * t1警告代码
     */
    @TableField("t1_warning_code")
    private String t1WarningCode;

    /**
     * t1错误代码
     */
    @TableField("t1_error_code")
    private String t1ErrorCode;

    /**
     * t1定性判断值(只针对LH： None/Low/Medium/High 依次为 0/1/2/3)
     */
    @TableField("t1_level")
    private String t1Level;

    /**
     * t1测量物类型
     */
    @TableField("t1_wand_type")
    private String t1WandType;

    /**
     * t2线峰高位置
     */
    @TableField("t2_peak_height_position")
    private BigDecimal t2PeakHeightPosition;

    /**
     * t2线绝对峰高
     */
    @TableField("t2_absolute_peak_height")
    private BigDecimal t2AbsolutePeakHeight;

    /**
     * t2线峰面积
     */
    @TableField("t2_peak_area")
    private BigDecimal t2PeakArea;

    /**
     * t2浓度值
     */
    @TableField("t2_con_value")
    private BigDecimal t2ConValue;

    /**
     * t2警告代码
     */
    @TableField("t2_warning_code")
    private String t2WarningCode;

    /**
     * t2错误代码
     */
    @TableField("t2_error_code")
    private String t2ErrorCode;

    /**
     * t2定性判断值(只针对LH： None/Low/Medium/High 依次为 0/1/2/3)
     */
    @TableField("t2_level")
    private String t2Level;

    /**
     * t2测量物类型(试剂类型为HCG+LH时： 判断是否显示HCGLH_Flag的标志位 (0不显示LH ，1显示LH)',
     */
    @TableField("t2_wand_type")
    private String t2WandType;

    /**
     * t3线峰高位置
     */
    @TableField("t3_peak_height_position")
    private BigDecimal t3PeakHeightPosition;

    /**
     * t3线绝对峰高
     */
    @TableField("t3_absolute_peak_height")
    private BigDecimal t3AbsolutePeakHeight;

    /**
     * t3线峰面积
     */
    @TableField("t3_peak_area")
    private BigDecimal t3PeakArea;

    /**
     * t3浓度值
     */
    @TableField("t3_con_value")
    private BigDecimal t3ConValue;

    /**
     * t3警告代码
     */
    @TableField("t3_warning_code")
    private String t3WarningCode;

    /**
     * t3错误代码
     */
    @TableField("t3_error_code")
    private String t3ErrorCode;

    /**
     * t3定性判断值(只针对LH： None/Low/Medium/High 依次为 0/1/2/3)
     */
    @TableField("t3_level")
    private String t3Level;

    /**
     * t1相对峰高
     */
    @TableField("t1_relatively_peak_height")
    private BigDecimal t1RelativelyPeakHeight;

    /**
     * t2相对峰高
     */
    @TableField("t2_relatively_peak_height")
    private BigDecimal t2RelativelyPeakHeight;

    /**
     * t3相对峰高
     */
    @TableField("t3_relatively_peak_height")
    private BigDecimal t3RelativelyPeakHeight;

    /**
     * t4相对峰高
     */
    @TableField("t4_relatively_peak_height")
    private BigDecimal t4RelativelyPeakHeight;

    /**
     * c1相对峰高
     */
    @TableField("c1_relatively_peak_height")
    private BigDecimal c1RelativelyPeakHeight;

    /**
     * c1线峰高位置
     */
    @TableField("c1_peak_height_position")
    private BigDecimal c1PeakHeightPosition;

    /**
     * c1线绝对峰高
     */
    @TableField("c1_absolute_peak_height")
    private BigDecimal c1AbsolutePeakHeight;

    /**
     * c1线峰面积
     */
    @TableField("c1_peak_area")
    private BigDecimal c1PeakArea;

    /**
     * 试剂棒结果C1_Flag是否有效（1 显示结果，0不显示结果）
     */
    @TableField("c1_wand_valid")
    private Integer c1WandValid;

    /**
     * c2线峰高位置
     */
    @TableField("c2_peak_height_position")
    private BigDecimal c2PeakHeightPosition;

    /**
     * c2线绝对峰高
     */
    @TableField("c2_absolute_peak_height")
    private BigDecimal c2AbsolutePeakHeight;

    /**
     * c2线峰面积
     */
    @TableField("c2_peak_area")
    private BigDecimal c2PeakArea;

    /**
     * 试剂棒结果C2_Flag是否有效（1 显示结果，0不显示结果）
     */
    @TableField("c2_wand_valid")
    private Integer c2WandValid;

    /**
     * 白域均值
     */
    @TableField("white_field_avg")
    private BigDecimal whiteFieldAvg;

    /**
     * PD板上温度
     */
    @TableField("PD_temperature")
    private BigDecimal PDTemperature;

    /**
     * back_groud_befor_c
     */
    @TableField("back_groud_befor_c")
    private String backGroudBeforC;

    /**
     * back_groud_end_t
     */
    @TableField("back_groud_end_t")
    private String backGroudEndT;

    /**
     * MCU温度
     */
    @TableField("MCU_temperature")
    private Integer MCUTemperature;

    /**
     * 电池电压
     */
    @TableField("battery_percentage")
    private Double batteryPercentage;

    /**
     * 错误代码
     */
    @TableField("error")
    private String error;

    /**
     * 警告代码
     */
    @TableField("warning")
    private String warning;

    /**
     * 原始数据
     */
    @TableField("raw_result_data")
    private String rawResultData;

    /**
     * 是否是有效数据
     */
    @TableField("avlid_flag")
    private Integer avlidFlage;

    /**
     * 数据来源，参考DataSourceEnum
     */
    @TableField("source")
    private Integer source;

    /**
     * 数据类型:0:auto;1manual(default0)
     */
    @TableField("auto_flag")
    private Integer autoFlag;

    /**
     * 系统操作备注
     */
    @TableField("sys_note")
    private String sysNote;

    /**
     * 跑板异常校验：
     * null表示尚未执行校验；
     * -1: 数据被删除，无需校验；
     * 0: 数据异常等原因无需校验；
     * 1: 跑板异常校验通过；
     * 2:跑板异常校验不通过；
     * 3:手动添加的数据不经过校验
     * @see RunBoardFlagEnum
     */
    @TableField("run_board_flag")
    private Integer runBoardFlag;
}
