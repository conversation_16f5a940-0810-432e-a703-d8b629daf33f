package com.mira.script.dal.entity.hub_duquesne;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.api.user.enums.UserGoalEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息详情
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("hub_user_info")
public class HubDuquesneUserInfoEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String hubId;

    /**
     * 出生年份
     */
    private Integer birthYear;

    /**
     * 出生月份
     */
    private Integer birthMonth;

    /**
     * 目标
     *
     * @see UserGoalEnum
     */
    private Integer goalStatus;
    /**
     * 目标
     */
    private String conditions;

    private Integer deleted;

}
