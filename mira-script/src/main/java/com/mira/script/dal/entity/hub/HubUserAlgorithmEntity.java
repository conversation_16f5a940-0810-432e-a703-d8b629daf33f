package com.mira.script.dal.entity.hub;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 用户算法结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("hub_user_algorithm")
public class HubUserAlgorithmEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String hubId;

    /**
     * 周期数据json
     */
    private String cycleData;

    /**
     * 测试数据json
     */
    private String hormoneData;

    /**
     * 周期分析数据
     */
    private String cycleAnalysis;

    /**
     * 额外分析数据
     */
    private String extraResult;


    /**
     * 阈值，参考ThresholdModeEnum
     */
    private Integer thresholdMode;
    /**
     * 修改时间
     */
    private Long modifyTime;


    /**
     * 同步时间
     */
    private Date syncTime;

    private Integer deleted;
}
