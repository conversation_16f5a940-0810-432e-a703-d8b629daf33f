package com.mira.mongo.service.impl;

import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.mongo.dto.AlgorithmRequestDTO;
import com.mira.mongo.domain.UserAlgorithmRequestLog;
import com.mira.mongo.repository.UserAlgorithmRequestLogRepository;
import com.mira.mongo.service.IUserAlgorithmRequestLogService;
import com.mira.mongo.util.DomainTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-01-23
 **/
@Slf4j
@Service
public class UserAlgorithmRequestLogServiceImpl implements IUserAlgorithmRequestLogService {
    @Resource
    private UserAlgorithmRequestLogRepository userAlgorithmRequestLogRepository;

    @Override
    public void save(AlgorithmRequestDTO algorithmRequestDTO) {
        UserAlgorithmRequestLog userAlgorithmRequestLog = new UserAlgorithmRequestLog();
        userAlgorithmRequestLog.setUserId(algorithmRequestDTO.getUserId());
        userAlgorithmRequestLog.setType(algorithmRequestDTO.getAlgorithmRequestTypeEnum().getValue());
        userAlgorithmRequestLog.setRequest(algorithmRequestDTO.getRequest());
        if (StringUtils.isNotBlank(algorithmRequestDTO.getErrorMsg())) {
            userAlgorithmRequestLog.setErrorMsg(algorithmRequestDTO.getErrorMsg());
        }
        String timeZone = algorithmRequestDTO.getTimeZone();
        DomainTimeUtil.setEntityTime(timeZone, userAlgorithmRequestLog);
        userAlgorithmRequestLogRepository.save(userAlgorithmRequestLog);
    }

    @Override
    public List<UserAlgorithmRequestLog> listByUserId(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        List<UserAlgorithmRequestLog> userAlgorithmRequestLogs;
        //获取的是第一页（从0开始计数），每页最多10条结果，并且按照createTime字段降序排序
        Pageable pageable = PageRequest.of(0, 10, Sort.by("createTime").descending());
        if (algorithmRequestTypeEnum == null) {
            userAlgorithmRequestLogs = userAlgorithmRequestLogRepository.listByUserId(userId, pageable);
        } else {
            userAlgorithmRequestLogs = userAlgorithmRequestLogRepository.listByUserIdAndType(userId,
                    algorithmRequestTypeEnum.getValue(), pageable);
        }
        return userAlgorithmRequestLogs;
    }
}
