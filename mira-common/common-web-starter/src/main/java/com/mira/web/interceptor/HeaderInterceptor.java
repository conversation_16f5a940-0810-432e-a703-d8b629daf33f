package com.mira.web.interceptor;

import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.CurrencyEnum;
import com.mira.core.consts.enums.LocalEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.IpUtils;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * header interceptor
 *
 * <AUTHOR>
 */
@Slf4j
public class HeaderInterceptor implements HandlerInterceptor {
    @Resource
    private SysDictProperties sysDictProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // check header
        String timeZone = request.getHeader(HeaderConst.TIME_ZONE);
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = sysDictProperties.getTimeZone();
        }
        String localLanguage = request.getHeader(HeaderConst.LOCAL_LANGUAGE);
        if (StringUtils.isEmpty(localLanguage)) {
            localLanguage = LocalEnum.LOCAL_US.getValue();
        }
        // TODO 测试环境，上线时去掉注释
        String currency = CurrencyEnum.TEST.getCode();
        String countryFlag = request.getHeader(HeaderConst.COUNTRY_FLAG);
        // save context
        ContextHolder.put(HeaderConst.TIME_ZONE, timeZone);
        ContextHolder.put(HeaderConst.LOCAL_LANGUAGE, localLanguage);
        ContextHolder.put(HeaderConst.IP, IpUtils.getIp(request));
        ContextHolder.put(HeaderConst.CURRENCY, currency);
        ContextHolder.put(HeaderConst.COUNTRY_FLAG, countryFlag);

        return true;
    }
}
