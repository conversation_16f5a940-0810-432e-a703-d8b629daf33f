package com.mira.core.consts.enums;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 试纸类型枚举
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
public enum WandTypeEnum {
    ERROR_DATA(0, "0", "错误试纸数据", Lists.newArrayList()),
    LH(1, "1", "LH试纸", Lists.newArrayList(BiomarkerEnum.LH.getBiomarker())),
    HCG(2, "2", "HCG试纸", Lists.newArrayList(BiomarkerEnum.HCG.getBiomarker())),
    E3G_LH(3, "3", "E3G+LH试纸", Lists.newArrayList(BiomarkerEnum.LH.getBiomarker(), BiomarkerEnum.E3G.getBiomarker())),
    CALIBRATE(4, "4", "校准试纸(内部使用)", Lists.newArrayList()),
    QC(5, "5", "QC试纸(内部使用)", Lists.newArrayList()),
    QC_REPEAT(6, "6", "QC重复性测试试纸(内部使用)", Lists.newArrayList()),
    HCG_LH(7, "7", "HCG+LH试纸(未发布)", Lists.newArrayList()),
    WHITE_AREA(8, "8", "白域测试试纸(内部使用)", Lists.newArrayList()),
    PDG(9, "9", "PDG试纸", Lists.newArrayList(BiomarkerEnum.PDG.getBiomarker())),
    KERATIN(10, "10", "角蛋白试纸", Lists.newArrayList()),
    SILK_PROTEIN(11, "11", "丝蛋白试纸", Lists.newArrayList()),
    LH_E3G_PDG(12, "12", "MAX试纸", Lists.newArrayList(BiomarkerEnum.LH.getBiomarker(), BiomarkerEnum.E3G.getBiomarker(), BiomarkerEnum.PDG.getBiomarker())),
    E3G_HIGH_RANGE(13, "13", "E3G高量程试纸", Lists.newArrayList(BiomarkerEnum.E3G_HIGH_RANGE.getBiomarker())),
    HCG_QUALITATIVE(14, "14", "hCG定性试纸", Lists.newArrayList(BiomarkerEnum.HCG_QUALITATIVE.getBiomarker())),
    LH_SENSITIVE(15, "15", "LH高灵敏度试纸", Lists.newArrayList()),
    FSH(16, "16", "FSH试纸", Lists.newArrayList(BiomarkerEnum.FSH.getBiomarker())),
    BBT(99, "99", "温度测试", Lists.newArrayList()),
    UNKNOWN(-99, "-99", "未知", Lists.newArrayList());

    private final Integer integer;
    private final String string;
    private final String desc;
    private final List<Integer> biomarker;

    WandTypeEnum(Integer integer, String string, String desc, List<Integer> biomarker) {
        this.integer = integer;
        this.string = string;
        this.desc = desc;
        this.biomarker = biomarker;
    }

    public static WandTypeEnum get(Integer value) {
        for (WandTypeEnum wandTypeEnum : WandTypeEnum.values()) {
            if (wandTypeEnum.getInteger().equals(value)) {
                return wandTypeEnum;
            }
        }
        return WandTypeEnum.UNKNOWN;
    }

    public static WandTypeEnum get(String value) {
        for (WandTypeEnum wandTypeEnum : WandTypeEnum.values()) {
            if (wandTypeEnum.getString().equals(value)) {
                return wandTypeEnum;
            }
        }
        return WandTypeEnum.UNKNOWN;
    }

    public static List<String> getAllowWandTypes() {
        List<String> allowTestWandTypes = new ArrayList<>();
        Arrays.stream(WandTypeEnum.values()).forEach(wandTypeEnum -> {
            if (wandTypeEnum.getString().length() == 1) {
                allowTestWandTypes.add("0" + wandTypeEnum.getString());
            } else {
                allowTestWandTypes.add(wandTypeEnum.getString());
            }
        });
        return allowTestWandTypes;
    }
}
