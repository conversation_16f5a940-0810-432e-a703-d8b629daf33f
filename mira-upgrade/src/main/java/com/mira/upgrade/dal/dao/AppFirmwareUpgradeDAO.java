package com.mira.upgrade.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.upgrade.dal.entity.AppFirmwareUpgradeEntity;
import com.mira.upgrade.dal.mapper.AppFirmwareUpgradeMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppFirmwareUpgradeDAO extends ServiceImpl<AppFirmwareUpgradeMapper, AppFirmwareUpgradeEntity> {
    public List<AppFirmwareUpgradeEntity> listByNoBeta() {
        return list(Wrappers.<AppFirmwareUpgradeEntity>lambdaQuery()
                .eq(AppFirmwareUpgradeEntity::getState, 1)
                .ne(AppFirmwareUpgradeEntity::getBataFlag, 1));
    }

    public List<AppFirmwareUpgradeEntity> listByBeta() {
        return list(Wrappers.<AppFirmwareUpgradeEntity>lambdaQuery()
                .eq(AppFirmwareUpgradeEntity::getState, 1)
                .eq(AppFirmwareUpgradeEntity::getBataFlag, 1));
    }
}
