package com.mira.upgrade.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("APP版本信息")
public class AppVersionVO {
    @ApiModelProperty("升级方式")
    private Integer upgrade;

    @ApiModelProperty("APP版本")
    private String appVersion;

    @ApiModelProperty("更新文案")
    private String content;
}
