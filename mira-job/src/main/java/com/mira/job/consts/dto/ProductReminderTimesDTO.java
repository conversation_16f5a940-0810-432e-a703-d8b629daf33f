package com.mira.job.consts.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述 构建product推送对象
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-06-09
 **/
@Getter
@Setter
public class ProductReminderTimesDTO {
    List<String> product03ReminderTimes = new ArrayList<>();
    List<String> product02ReminderTimes = new ArrayList<>();
    List<String> product09ReminderTimes = new ArrayList<>();
    List<String> product12ReminderTimes = new ArrayList<>();
    List<String> product14ReminderTimes = new ArrayList<>();
    List<String> product16ReminderTimes = new ArrayList<>();
}
