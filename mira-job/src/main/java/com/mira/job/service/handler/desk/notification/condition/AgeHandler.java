package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.core.util.AgeUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Age
 * <br/>
 * 1:(18 - 25), 2:(26 - 30), 3:(31 - 35), 4:(36 - 40), 5:(>40)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgeHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer[] agesCondition = dto.getAge();
        if (agesCondition == null || agesCondition.length == 0) {
            return;
        }
        List<Integer> agesConditionList = List.of(agesCondition);

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            Integer birthYear = userInfo.getBirthYear();
            Integer birthMonth = userInfo.getBirthMonth();
            Integer birthOfDay = userInfo.getBirthOfDay();
            try {
                Integer age = AgeUtil.calculateAge(birthYear, birthMonth, birthOfDay);
                if (!agesConditionList.contains(getConditionNumber(age))) {
                    waitDeleteList.add(userInfo);
                }
            } catch (Exception e) {
                log.error("user:{}, cal age error", userInfo.getUserId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }

    private int getConditionNumber(Integer age) {
        if (age == null) {
            return -1;
        }

        if (age >= 18 && age <= 25) {
            return 1;
        }
        if (age >= 26 && age <= 30) {
            return 2;
        }
        if (age >= 31 && age <= 35) {
            return 3;
        }
        if (age >= 36 && age <= 40) {
            return 4;
        }
        if (age > 40 && age <= 45) {
            return 5;
        }
        if (age > 45 && age <= 50) {
            return 6;
        }
        if (age > 50) {
            return 7;
        }
        //        if (age > 40) {
        //            return 5;
        //        }

        return -1;
    }
}
