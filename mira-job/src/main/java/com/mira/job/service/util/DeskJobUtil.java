package com.mira.job.service.util;

import com.mira.job.consts.dto.desk.XxlJobInfo;
import com.mira.job.exception.JobException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.XxlJobRemotingUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * xxljob util
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
public class DeskJobUtil {
    private static final String ADD_JOB_URL = "/jobinfo/dynamicAdd";
    private static final String UPDATE_JOB_URL = "/jobinfo/dynamicUpdate";
    private static final String REMOVE_JOB_URL = "/jobinfo/dynamicRemove";
    private static final String START_JOB_URL = "/jobinfo/dynamicStart";
    private static final String STOP_JOB_URL = "/jobinfo/dynamicStop";

    public static String addJob(String serverPath, String accessToken,
                                XxlJobInfo xxlJobInfo, String taskId) {
        try {
            ReturnT returnT = XxlJobRemotingUtil.postBody(serverPath + ADD_JOB_URL, accessToken, 6, xxlJobInfo, String.class);
            if (returnT == null || returnT.getCode() == 500) {
                log.error("Add job occur error, taskId:{}", taskId, returnT.getMsg());
                throw new JobException("Add job occur error");
            }
            return String.valueOf(returnT.getContent());
        } catch (Exception e) {
            log.error("Add job occur error, taskId:{}", taskId);
            throw new JobException("Add job occur error");
        }
    }

    public static String updateJob(String serverPath, String accessToken,
                                   XxlJobInfo xxlJobInfo, String taskId) {
        try {
            ReturnT returnT = XxlJobRemotingUtil.postBody(serverPath + UPDATE_JOB_URL, accessToken, 6, xxlJobInfo, String.class);
            if (returnT == null || returnT.getCode() == 500) {
                log.error("Update job occur error, taskId:{}", taskId, returnT.getMsg());
                throw new JobException("Update job occur error");
            }
            return String.valueOf(returnT.getContent());
        } catch (Exception e) {
            log.error("Update job occur error, taskId:{}", taskId);
            throw new JobException("Update job occur error");
        }
    }

    public static String removeJob(String serverPath, String accessToken,
                                   int jobId, String taskId) {
        try {
            String url = serverPath + REMOVE_JOB_URL + "/" + jobId;
            ReturnT returnT = XxlJobRemotingUtil.postBody(url, accessToken, 6, null, String.class);
            if (returnT == null || returnT.getCode() == 500) {
                log.error("Remove job occur error, taskId:{}", taskId, returnT.getMsg());
                throw new JobException("Remove job occur error");
            }
            return String.valueOf(returnT.getContent());
        } catch (Exception e) {
            log.error("Remove job occur error, taskId:{}", taskId);
            throw new JobException("Remove job occur error");
        }
    }

    public static String startJob(String serverPath, String accessToken,
                                  int jobId, String taskId) {
        try {
            String url = serverPath + START_JOB_URL + "/" + jobId;
            ReturnT returnT = XxlJobRemotingUtil.postBody(url, accessToken, 6, null, String.class);
            if (returnT == null || returnT.getCode() == 500) {
                log.error("Start job occur error, taskId:{}", taskId, returnT.getMsg());
                throw new JobException("Start job occur error");
            }
            return String.valueOf(returnT.getContent());
        } catch (Exception e) {
            log.error("Start job occur error, taskId:{}", taskId);
            throw new JobException("Start job occur error");
        }
    }

    public static String stopJob(String serverPath, String accessToken,
                                 int jobId, String taskId) {
        try {
            String url = serverPath + STOP_JOB_URL + "/" + jobId;
            ReturnT returnT = XxlJobRemotingUtil.postBody(url, accessToken, 6, null, String.class);
            if (returnT == null || returnT.getCode() == 500) {
                log.error("Stop job occur error, taskId:{}", taskId, returnT.getMsg());
                throw new JobException("Stop job occur error");
            }
            return String.valueOf(returnT.getContent());
        } catch (Exception e) {
            log.error("Stop job occur error, taskId:{}", taskId);
            throw new JobException("Stop job occur error");
        }
    }
}
