package com.mira.job.service.manager;

import com.mira.api.bluetooth.consts.DefaultWandsParamConst;
import com.mira.api.bluetooth.consts.ResultVersionConst;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.api.bluetooth.enums.TWarningCodeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.job.consts.dto.HubHormoneDTO;
import com.mira.job.dal.entity.master.AppDataUploadEntity;
import com.mira.job.service.handler.testresult.ITestResultHandler;
import com.mira.job.service.handler.testresult.TestResultHandler;
import com.mira.job.service.util.RunBoardCheckDataHelper;
import com.mira.job.service.util.WandsParamRecordHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 蓝牙数据接口通用层
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class HormoneManager {
    @Resource
    private RunBoardCheckDataHelper runBoardCheckDataHelper;
    @Resource
    private WandsParamRecordHelper wandsParamRecordHelper;

    /**
     * 构建激素数据 调用算法数据有效：仅当error=00 warning in (00,05,09)，入参需提前校验
     *
     * @param dataUploadEntity 上传数据的表记录
     * @param trialFlag        beta标记
     * @return HormoneDTO null表示忽略该条数据
     * @see com.mira.bluetooth.service.impl.BluetoothServiceImpl line 186
     */
    public HubHormoneDTO convertToHubHormoneDTO(AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO) {
        Long userId = dataUploadEntity.getUserId();
        String error = dataUploadEntity.getError();
        String warning = dataUploadEntity.getWarning();
        String testWandType = dataUploadEntity.getTestWandType();

        // step1:过滤算法异常的数据
        List<String> allowEcodeList = Arrays.asList("00");
        List<String> allowWcodeList = Arrays.asList("00", "05", "09");
        if (!allowEcodeList.contains(error) || !allowWcodeList.contains(warning)) {
            return null;
        }
        HubHormoneDTO hubHormoneDTO = buildHubHormoneDTO(dataUploadEntity, wandsParamRecordDTO);

        // step3:跑板异常校验，跑板失败数据需要展示(加入到dailydata)，但不调用算法(flag==0)
        boolean checkDataBool = runBoardCheckDataHelper.checkData(userId, dataUploadEntity, wandsParamRecordDTO);
        if (!checkDataBool) {
            return null;
        }

        // step4:T线校验，调算法，但会将数值合理化(将超过范围的数值修改为UpperLimit or LowerLimit)，T线校验有问题的数据，Ecode设置为B03
        Float lhLowerLimit = DefaultWandsParamConst.LH_LOWER_LIMIT;
        Float lhUpperLimit = DefaultWandsParamConst.LH_UPPER_LIMIT;
        Float e3gLowerLimit = DefaultWandsParamConst.E3G_LOWER_LIMIT;
        Float e3gUpperLimit = DefaultWandsParamConst.E3G_UPPER_LIMIT;

        if (testWandType.equals("0" + WandTypeEnum.LH.getString())
                || testWandType.equals("0" + WandTypeEnum.E3G_LH.getString())) {
            String t1WarningCode = dataUploadEntity.getT1WarningCode();
            String t2WarningCode = dataUploadEntity.getT2WarningCode();
            String testWandBatch = dataUploadEntity.getTestWandBatch();
            String resultVersionFormat = dataUploadEntity.getResultVersionFormat();

            // 批次校验
            String uStripType = testWandType.substring(1);
            if (ObjectUtils.isNotEmpty(wandsParamRecordDTO)) {
                if (WandTypeEnum.LH.getString().equals(uStripType)) {
                    lhLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                    lhUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
                } else if (WandTypeEnum.E3G_LH.getString().equals(uStripType)) {
                    lhLowerLimit = wandsParamRecordDTO.getFLowerLimit3();
                    lhUpperLimit = wandsParamRecordDTO.getFUpperLimit3();
                    e3gLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                    e3gUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
                }
            }

            if (ResultVersionConst.RESULTVERSIONFORMAT_03.equals(resultVersionFormat)) {
                // 某些数据 userHormoneDTO.setFlag(0);
                if (uStripType.equals(WandTypeEnum.LH.getString())) {
                    // t1对应lh
                    if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                        // LH >T1_con 不调算法
                        if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode)) {
                            // 传当前批次的lower值
                            hubHormoneDTO.setValue1(new BigDecimal(lhUpperLimit));
                        } else if (TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                            // LH <T1_con  不调算法
                            hubHormoneDTO.setValue1(new BigDecimal(lhLowerLimit));
                        }
                    }

                } else if (uStripType.equals(WandTypeEnum.E3G_LH.getString())) {
                    // t1对应e3g
                    if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                        if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode)) {
                            // 传当前批次的lower值
                            hubHormoneDTO.setValue1(new BigDecimal(e3gLowerLimit));
                        } else if (TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                            hubHormoneDTO.setValue1(new BigDecimal(e3gUpperLimit));
                        }
                    }
                    // t2对应lh
                    if (TWarningCodeEnum.CODE_1.getValue().equals(t2WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t2WarningCode)) {
                        if (TWarningCodeEnum.CODE_1.getValue().equals(t2WarningCode)) {
                            // 传当前批次的lower值
                            hubHormoneDTO.setValue2(new BigDecimal(lhUpperLimit));
                        } else if (TWarningCodeEnum.CODE_4.getValue().equals(t2WarningCode)) {
                            hubHormoneDTO.setValue2(new BigDecimal(lhLowerLimit));
                        }
                    }
                }
            }
        }
        return hubHormoneDTO;
    }

    /**
     * 构建激素数据
     *
     * @param dataUploadEntity    上传数据的表记录
     * @param wandsParamRecordDTO 试剂参数
     * @return HormoneDTO null表示忽略该条数据
     */
    private HubHormoneDTO buildHubHormoneDTO(AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO) {
        HubHormoneDTO hubHormoneDTO = new HubHormoneDTO();
        BeanUtils.copyProperties(dataUploadEntity, hubHormoneDTO);
        hubHormoneDTO.setDataId(dataUploadEntity.getId());
        hubHormoneDTO.setUserId(dataUploadEntity.getUserId());
        hubHormoneDTO.setWandBatch3(dataUploadEntity.getTestWandBatch());

        // t1浓度值
        BigDecimal t1ConValue = dataUploadEntity.getT1ConValue();
        // t2浓度值
        BigDecimal t2ConValue = dataUploadEntity.getT2ConValue();
        // t3浓度值
        BigDecimal t3ConValue = dataUploadEntity.getT3ConValue();
        // 试剂完成时间
        String completeTime = dataUploadEntity.getCompleteTime();
        // 试剂类型
        String testWandType = dataUploadEntity.getTestWandType();

        ITestResultHandler testResultHandler = TestResultHandler.get(dataUploadEntity.getTestWandType());
        if (Objects.nonNull(testResultHandler)) {
            testResultHandler.handle(dataUploadEntity, hubHormoneDTO);
        }

        return hubHormoneDTO;
    }


}
