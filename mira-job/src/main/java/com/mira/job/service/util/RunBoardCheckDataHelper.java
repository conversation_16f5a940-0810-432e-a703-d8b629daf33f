package com.mira.job.service.util;

import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.job.dal.entity.master.AppDataUploadEntity;
import com.mira.web.properties.SysDictProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 跑版异常校验
 * <AUTHOR>
 */
@Slf4j
@Component
public class RunBoardCheckDataHelper {
    @Resource
    private SysDictProperties sysDictProperties;

    /**
     * 跑板异常校验
     * <p>
     * 跑板失败数据需要展示，但不调用算法，db记录跑板记录
     * <p>
     * 进入方法的数据一定是warning 00 05   error 00
     * <p>
     *
     * @return false表示跑板异常
     */
    public boolean checkData(Long userId, AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO) {
        if (dataUploadEntity.getAutoFlag() == 1) {
            return true;
        }
        boolean checkResult = false;
        BaseResultDTO baseResultDTO = buildBaseResultDTO(dataUploadEntity);
        Long dataId = dataUploadEntity.getId();
        String testWandBatch = dataUploadEntity.getTestWandBatch();
        String testWandType = dataUploadEntity.getTestWandType();
        String completeTime = dataUploadEntity.getCompleteTime();
        String wandType = testWandType.startsWith("0") ? testWandType.substring(1) : testWandType;
        WandTypeEnum wandTypeEnum = WandTypeEnum.get(wandType);
        RunBoardCheckMarkDTO runBoardCheckMarkDTO = buildRunBoardCheckMarkDTO(wandTypeEnum, testWandBatch, wandsParamRecordDTO, completeTime);
        switch (wandTypeEnum) {
            case LH:
            case PDG:
            case E3G_HIGH_RANGE:
            case FSH:
                checkResult = checkDataWithT1C(runBoardCheckMarkDTO, baseResultDTO.getT1BaseLineGao(), baseResultDTO.getCBaseLineGao(),
                        baseResultDTO.getC1_pos(), baseResultDTO.getT1_pos());
                break;
            case E3G_LH:
            case HCG:
            case LH_E3G_PDG:
            case HCG_QUALITATIVE:
                checkResult = checkDataWithT1T2C(runBoardCheckMarkDTO, baseResultDTO.getT1BaseLineGao(), baseResultDTO.getT2BaseLineGao(), baseResultDTO.getCBaseLineGao(),
                        baseResultDTO.getC1_pos(), baseResultDTO.getT1_pos(), baseResultDTO.getT2_pos());
                break;
            default:
                break;
        }

        return checkResult;
    }

    private BaseResultDTO buildBaseResultDTO(AppDataUploadEntity dataUploadEntity) {
        BaseResultDTO baseResultDTO = new BaseResultDTO();
        String firmwareVersion = dataUploadEntity.getFirmwareVersion();
        // t1线绝对峰高
        double t1AbsolutePeakHeight = (dataUploadEntity.getT1AbsolutePeakHeight() == null)
                ? 0d : dataUploadEntity.getT1AbsolutePeakHeight().doubleValue();
        // t1线峰位置
        double t1_pos = (dataUploadEntity.getT1PeakHeightPosition()) == null
                ? 0d : dataUploadEntity.getT1PeakHeightPosition().doubleValue();
        // t1相对峰高
        double t1RelativelyPeakHeight = (dataUploadEntity.getT1RelativelyPeakHeight()) == null
                ? 0d : dataUploadEntity.getT1RelativelyPeakHeight().doubleValue();

        // t2线绝对峰高
        double t2AbsolutePeakHeight = (dataUploadEntity.getT2AbsolutePeakHeight()) == null
                ? 0d : dataUploadEntity.getT2AbsolutePeakHeight().doubleValue();
        // t2线峰位置
        double t2_pos = (dataUploadEntity.getT2PeakHeightPosition()) == null
                ? 0d : dataUploadEntity.getT2PeakHeightPosition().doubleValue();
        // t2相对峰高
        double t2RelativelyPeakHeight = (dataUploadEntity.getT2RelativelyPeakHeight()) == null
                ? 0d : dataUploadEntity.getT2RelativelyPeakHeight().doubleValue();

        // c1相对峰高
        double c1RelativelyPeakHeight = (dataUploadEntity.getC1RelativelyPeakHeight()) == null
                ? 0d : dataUploadEntity.getC1RelativelyPeakHeight().doubleValue();
        // c1线绝对峰高
        double c1AbsolutePeakHeight = (dataUploadEntity.getC1AbsolutePeakHeight()) == null
                ? 0d : dataUploadEntity.getC1AbsolutePeakHeight().doubleValue();
        // c1线峰位置
        double c1_pos = (dataUploadEntity.getC1PeakHeightPosition()) == null
                ? 0d : dataUploadEntity.getC1PeakHeightPosition().doubleValue();

        // t1线基线高
        double t1BaseLineGao = 0d;
        // t2线基线高
        double t2BaseLineGao = 0d;
        // c线基线高
        double cBaseLineGao = 0d;

        if (t1RelativelyPeakHeight != 0) {
            // firmware在106之前的不做跑版判断
            if (106 <= Integer.parseInt(firmwareVersion.substring(0, 4))) {
                // t1线基线高 = t1绝对峰高（峰高对应值）- t1相对峰高;
                t1BaseLineGao = Math.abs(t1AbsolutePeakHeight - t1RelativelyPeakHeight);
            } else {
                // t1线基线高 = t1相对峰高
                t1BaseLineGao = Math.abs(t1RelativelyPeakHeight);
            }
        }
        if (c1RelativelyPeakHeight != 0) {
            // C线基线高 = 绝对峰高 - 相对峰高
            cBaseLineGao = Math.abs(c1AbsolutePeakHeight - c1RelativelyPeakHeight);
        }
        if (t2RelativelyPeakHeight != 0) {
            if (106 <= Integer.parseInt(firmwareVersion.substring(0, 4))) {
                // t2线基线高 = t2绝对峰高（峰高对应值）- t2相对峰高;
                t2BaseLineGao = Math.abs(t2AbsolutePeakHeight - t2RelativelyPeakHeight);
            } else {
                // t2线基线高 = t2相对峰高
                t2BaseLineGao = Math.abs(t2RelativelyPeakHeight);
            }
        }
        baseResultDTO.setT1BaseLineGao(t1BaseLineGao);
        baseResultDTO.setT2BaseLineGao(t2BaseLineGao);
        baseResultDTO.setCBaseLineGao(cBaseLineGao);
        baseResultDTO.setT1_pos(t1_pos);
        baseResultDTO.setT2_pos(t2_pos);
        baseResultDTO.setC1_pos(c1_pos);
        return baseResultDTO;
    }

    @Getter
    @Setter
    public static class BaseResultDTO {
        private Double t1BaseLineGao;
        private Double t2BaseLineGao;
        private Double cBaseLineGao;
        private Double c1_pos;
        private Double t1_pos;
        private Double t2_pos;
    }

    /**
     * 校验LH试剂跑版异常
     */
    private boolean checkDataWithT1C(RunBoardCheckMarkDTO runBoardCheckMarkDTO, Double t1BaseLineGao, Double cBaseLineGao, Double c1_pos, Double t1_pos) {
        // 计算t1c基线斜率
        double t1cBaseLineSlope;
        if (t1_pos.equals(c1_pos)) {
            t1cBaseLineSlope = 0d;
        } else {
            t1cBaseLineSlope = Math.abs((cBaseLineGao - t1BaseLineGao) / (t1_pos - c1_pos));
        }

        double avgBaseLine = (t1BaseLineGao + cBaseLineGao) / 2;
        if (avgBaseLine > runBoardCheckMarkDTO.getAvgBaseLineMark()
                || t1cBaseLineSlope > runBoardCheckMarkDTO.getSlopeT1cMark()) {
            return false;
        }
        return true;
    }

    /**
     * 校验E3G+LH试剂、HCG试剂跑版异常
     */
    private boolean checkDataWithT1T2C(RunBoardCheckMarkDTO runBoardCheckMarkDTO, Double t1BaseLineGao, Double t2BaseLineGao, Double cBaseLineGao,
                                       Double c1_pos, Double t1_pos, Double t2_pos) {
        // 计算t1c基线斜率
        double t1cBaseLineSlope;
        if (t1_pos.equals(c1_pos)) {
            t1cBaseLineSlope = 0d;
        } else {
            t1cBaseLineSlope = Math.abs((cBaseLineGao - t1BaseLineGao) / (t1_pos - c1_pos));
        }
        // 计算t2c基线斜率
        double t2cBaseLineSlope;
        if (t2_pos.equals(c1_pos)) {
            t2cBaseLineSlope = 0d;
        } else {
            t2cBaseLineSlope = Math.abs((cBaseLineGao - t2BaseLineGao) / (t2_pos - c1_pos));
        }

        if (t1cBaseLineSlope > runBoardCheckMarkDTO.getSlopeT1cMark() || t2cBaseLineSlope > runBoardCheckMarkDTO.getSlopeT2cMark()) {
            return false;
        }

        // 计算三条线的CV
        double avgBaseLine = (t1BaseLineGao + t2BaseLineGao + cBaseLineGao) / 3;
        double t1Pow = Math.pow((t1BaseLineGao - avgBaseLine), 2);
        double t2Pow = Math.pow((t2BaseLineGao - avgBaseLine), 2);
        double cPow = Math.pow((cBaseLineGao - avgBaseLine), 2);
        double sqrt = Math.sqrt((t1Pow + t2Pow + cPow) / 2);
        double cv = 0;
        if (avgBaseLine > 0) {
            double divide = sqrt / avgBaseLine;
            if (sqrt / avgBaseLine >= 0) {
                cv = divide;
            }
        }
        if (avgBaseLine > runBoardCheckMarkDTO.getAvgBaseLineMark()
                && cv > runBoardCheckMarkDTO.getDb_t1t2c_cv()) {
            return false;
        }

        return true;
    }

    private RunBoardCheckMarkDTO buildRunBoardCheckMarkDTO(WandTypeEnum wandTypeEnum, String testWandBatch,
                                                           WandsParamRecordDTO wandsParamRecordDTO, String completeTime) {
        RunBoardCheckMarkDTO runBoardCheckMarkDTO = null;
        switch (wandTypeEnum) {
            case LH:
            case PDG:
            case E3G_HIGH_RANGE:
            case FSH:
                runBoardCheckMarkDTO = getT1CRunBoardCheckMarkDTO(testWandBatch, wandsParamRecordDTO, completeTime);
                break;
            case E3G_LH:
            case HCG:
            case LH_E3G_PDG:
            case HCG_QUALITATIVE:
                runBoardCheckMarkDTO = getT1T2CRunBoardCheckMarkDTO(testWandBatch, wandsParamRecordDTO, completeTime);
                break;
            default:
                break;
        }
        return runBoardCheckMarkDTO;
    }

    private RunBoardCheckMarkDTO getT1T2CRunBoardCheckMarkDTO(String testWandBatch, WandsParamRecordDTO wandsParamRecordDTO, String completeTime) {
        float db_t1c_slope = Float.parseFloat(sysDictProperties.getT1cSlope());
        float db_t2c_slope = Float.parseFloat(sysDictProperties.getT2cSlope());
        float db_t1t2c_cv = Float.parseFloat(sysDictProperties.getT1t2cCv());
        float db_t1c_slope_new = Float.parseFloat(sysDictProperties.getT1cSlopeNew());

        long wandBatch = Long.parseLong(testWandBatch);
        Float avgBaseLineMark = 100f;
        Float slopeT1cMark = null;
        Float slopeT2cMark = db_t2c_slope;
        if (wandBatch >= 2021050304L) {
            slopeT1cMark = db_t1c_slope_new;
        } else {
            slopeT1cMark = db_t1c_slope;
        }
        if (wandsParamRecordDTO != null) {
            avgBaseLineMark = wandsParamRecordDTO.getAvgBaseLine();
            slopeT1cMark = wandsParamRecordDTO.getSlopeT1c();
            slopeT2cMark = wandsParamRecordDTO.getSlopeT2c();
            if (avgBaseLineMark == null || avgBaseLineMark == 0) {
                avgBaseLineMark = 100f;
            }
            if (slopeT1cMark == null || slopeT1cMark == 0) {
                if (wandBatch >= 2021050304L) {
                    slopeT1cMark = db_t1c_slope_new;
                } else {
                    slopeT1cMark = db_t1c_slope;
                }
            }
            if (slopeT2cMark == null || slopeT2cMark == 0) {
                slopeT2cMark = db_t2c_slope;
            }
        }
        // 2022年5月19日异常批次处理，平均基线高：100，t1c:0.33，t2c:0.34
        // 背景：这些批次使用的吸水棒有问题，造成层析时间很久
        List<String> error05Batch = List.of(sysDictProperties.getError05Batch().split(","));
        if (error05Batch.contains(testWandBatch)
                && completeTime.compareTo("2022-05-19 23:59:59") >= 1) {
            avgBaseLineMark = 100f;
            slopeT1cMark = 0.33f;
            slopeT2cMark = 0.34f;
        }
        return new RunBoardCheckMarkDTO(avgBaseLineMark, slopeT1cMark, slopeT2cMark, db_t1t2c_cv);
    }

    private RunBoardCheckMarkDTO getT1CRunBoardCheckMarkDTO(String testWandBatch, WandsParamRecordDTO wandsParamRecordDTO, String completeTime) {
        Float avgBaseLineMark = 100f;
        Float slopeT1cMark = 0.25f;
        if (wandsParamRecordDTO != null) {
            avgBaseLineMark = wandsParamRecordDTO.getAvgBaseLine();
            slopeT1cMark = wandsParamRecordDTO.getSlopeT1c();
            if (avgBaseLineMark == null || avgBaseLineMark == 0) {
                avgBaseLineMark = 100f;
            }
            if (slopeT1cMark == null || slopeT1cMark == 0) {
                slopeT1cMark = 0.25f;
            }
        }

        List<String> error05Batch = List.of(sysDictProperties.getError05Batch().split(","));
        if (error05Batch.contains(testWandBatch)
                && completeTime.compareTo("2022-05-24 23:59:59") >= 1) {
            avgBaseLineMark = 100f;
            slopeT1cMark = 0.33f;
        }
        return new RunBoardCheckMarkDTO(avgBaseLineMark, slopeT1cMark);
    }

    @Getter
    @Setter
    public static class RunBoardCheckMarkDTO {
        private Float avgBaseLineMark;
        private Float slopeT1cMark;
        private Float slopeT2cMark;
        private Float db_t1t2c_cv;

        public RunBoardCheckMarkDTO(Float avgBaseLineMark, Float slopeT1cMark) {
            this.avgBaseLineMark = avgBaseLineMark;
            this.slopeT1cMark = slopeT1cMark;
        }

        public RunBoardCheckMarkDTO(Float avgBaseLineMark, Float slopeT1cMark, Float slopeT2cMark, Float db_t1t2c_cv) {
            this.avgBaseLineMark = avgBaseLineMark;
            this.slopeT1cMark = slopeT1cMark;
            this.slopeT2cMark = slopeT2cMark;
            this.db_t1t2c_cv = db_t1t2c_cv;
        }
    }
}
