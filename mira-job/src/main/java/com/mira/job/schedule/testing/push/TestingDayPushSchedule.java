package com.mira.job.schedule.testing.push;

import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.JobNotificationDTO;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.ReminderEnum;
import com.mira.job.dal.dao.master.SysNotificationTestingHistoryDAO;
import com.mira.job.dal.dao.master.SysNotificationTestingStatisticsDAO;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.dal.entity.master.SysNotificationTestingHistoryEntity;
import com.mira.job.dal.entity.master.SysNotificationTestingStatisticsEntity;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.redis.cache.RedisComponent;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Test env：0 5,15,25,35,45,55 * * * ?
 * Product env：0 6,16,26,36,46,56 * * * ?
 * <p>
 * 推送测试日
 * <p>
 * 发送firebase，并记录sys_notification_record和sys_notification_testing_history
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2021-08-04
 **/
@Slf4j
@Component
public class TestingDayPushSchedule extends AbstractPushTesting {
    @Resource
    private SysNotificationTestingHistoryDAO sysNotificationTestingHistoryDAO;
    @Resource
    private SysNotificationTestingStatisticsDAO sysNotificationTestingStatisticsDAO;

    @Resource
    private JobManager jobManager;
    @Resource
    private CommonManager commonManager;
    @Resource
    private RedisComponent redisComponent;

    private static final Set<String> MAX_WAND_TYPES = Set.of("3", "9", "12");
    private static final String OVUM_WAND_TYPE = "16";

    @XxlJob("testingDayPushHandler")
    public void testingDayPushHandler() {
        log.info("testingDayPushHandler: start execute");
        run();
        log.info("testingDayPushHandler: end execute");
        log.info("------------------------------------");
    }

    public void run() {
        long pushCurrentTime = System.currentTimeMillis();
        // 10分钟前
        Long pushBeforeTime = pushCurrentTime - 10 * 60 * 1000;
        // 10分钟后
        Long pushAfterTime = pushCurrentTime + 10 * 60 * 1000;
        // 在pushStartTime之前的，需要从first_reminder_time中移除
        removeBeforePushStartTime(pushBeforeTime);
        // 在pushStartTime~pushEndTime时间区间内没有推送过的，需要发推送，并记录
        List<JobNotificationDTO> jobNotificationDTOS = buildJobNotificationDTOS(pushBeforeTime, pushAfterTime);
        push(jobNotificationDTOS, jobManager.getAllUserInfo());
    }

    /**
     * 从first_reminder_time中移除
     */
    private void removeBeforePushStartTime(Long pushStartTime) {
        List<SysNotificationTestingStatisticsEntity> removeTestingDayList = sysNotificationTestingStatisticsDAO.listBeforePushStartTime(pushStartTime);
        log.info("remove testingDayList size:{}", removeTestingDayList.size());
        for (SysNotificationTestingStatisticsEntity statisticsEntity : removeTestingDayList) {
            // reminder time long
            Long e3gFirstReminderTime = statisticsEntity.getE3gFirstReminderTime();
            Long hcgFirstReminderTime = statisticsEntity.getHcgFirstReminderTime();
            Long pdgFirstReminderTime = statisticsEntity.getPdgFirstReminderTime();
            Long product12FirstReminderTime = statisticsEntity.getProduct12FirstReminderTime();
            Long product14FirstReminderTime = statisticsEntity.getProduct14FirstReminderTime();
            Long product16FirstReminderTime = statisticsEntity.getProduct16FirstReminderTime();

            // reminder time string
            String e3gReminderTimes = statisticsEntity.getE3gReminderTimes();
            String hcgReminderTimes = statisticsEntity.getHcgReminderTimes();
            String pdgReminderTimes = statisticsEntity.getPdgReminderTimes();
            String product12ReminderTimes = statisticsEntity.getProduct12ReminderTimes();
            String product14ReminderTimes = statisticsEntity.getProduct14ReminderTimes();
            String product16ReminderTimes = statisticsEntity.getProduct16ReminderTimes();

            // handler wand type
            if (e3gFirstReminderTime != null && e3gFirstReminderTime < pushStartTime) {
                removeFirstReminderTime(statisticsEntity, e3gReminderTimes, WandTypeEnum.E3G_LH);
            }
            if (hcgFirstReminderTime != null && hcgFirstReminderTime < pushStartTime) {
                removeFirstReminderTime(statisticsEntity, hcgReminderTimes, WandTypeEnum.HCG);
            }
            if (pdgFirstReminderTime != null && pdgFirstReminderTime < pushStartTime) {
                removeFirstReminderTime(statisticsEntity, pdgReminderTimes, WandTypeEnum.PDG);
            }
            if (product12FirstReminderTime != null && product12FirstReminderTime < pushStartTime) {
                removeFirstReminderTime(statisticsEntity, product12ReminderTimes, WandTypeEnum.LH_E3G_PDG);
            }
            if (product14FirstReminderTime != null && product14FirstReminderTime < pushStartTime) {
                removeFirstReminderTime(statisticsEntity, product14ReminderTimes, WandTypeEnum.HCG_QUALITATIVE);
            }
            if (product16FirstReminderTime != null && product16FirstReminderTime < pushStartTime) {
                removeFirstReminderTime(statisticsEntity, product16ReminderTimes, WandTypeEnum.FSH);
            }

            // update
            UpdateEntityTimeUtil.updateBaseEntityTime(statisticsEntity.getTimeZone(), statisticsEntity);
        }
        sysNotificationTestingStatisticsDAO.updateBatchById(removeTestingDayList);
    }

    /**
     * 在pushStartTime~pushEndTime时间区间内没有推送过的，需要发推送，并记录
     * <p>
     * 如果间隔是半小时，则推送最多延迟半小时
     */
    private List<JobNotificationDTO> buildJobNotificationDTOS(Long pushBeforeTime, Long pushAfterTime) {
        List<SysNotificationTestingStatisticsEntity> testingDayList = sysNotificationTestingStatisticsDAO.listBetweenPushTime(pushBeforeTime, pushAfterTime);
        log.info("push testingDayList size:{}", testingDayList.size());

        return testingDayList.stream()
                .map(statistics -> buildNotification(statistics, pushBeforeTime, pushAfterTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private JobNotificationDTO buildNotification(SysNotificationTestingStatisticsEntity statistics,
                                                 Long pushBeforeTime, Long pushAfterTime) {
        Long userId = statistics.getUserId();
        String timeZone = statistics.getTimeZone();

        /*
         Testing day: Mira Max Wand，03/09/12 测了其中一个及以上，不推送
         Testing day: Mira Max Wand Mira 和 Ovum Wand，03/09/12/16 测了其中一个及以上，不推送
         */

        // 当日已测试试剂集合
        String currentDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        String key = RedisCacheKeyConst.USER_NEW_HORMONE_WANDTYPE_DAY + currentDate + ":" + userId;
        Set<String> alreadyTestWandTypeSet = redisComponent.get(key, Set.class);

        // 已测试的试剂类型
        boolean hasTestedMax = CollectionUtils.isNotEmpty(alreadyTestWandTypeSet) &&
                alreadyTestWandTypeSet.stream().anyMatch(MAX_WAND_TYPES::contains);
        boolean hasTestedOvum = CollectionUtils.isNotEmpty(alreadyTestWandTypeSet) &&
                alreadyTestWandTypeSet.contains(OVUM_WAND_TYPE);

        // 构建 JobNotificationDTO
        JobNotificationDTO maxNotification = !hasTestedMax ? buildMaxNotification(statistics, pushBeforeTime, pushAfterTime) : null;
        JobNotificationDTO ovumNotification = !hasTestedOvum ? buildOvumNotification(statistics, pushBeforeTime, pushAfterTime) : null;

        // 决定返回哪种通知类型
        return determineNotification(maxNotification, ovumNotification, userId, hasTestedMax, hasTestedOvum);
    }

    private JobNotificationDTO buildMaxNotification(SysNotificationTestingStatisticsEntity statistics,
                                                    Long pushBeforeTime, Long pushAfterTime) {
        Long reminderTime = Stream.of(
                        statistics.getProduct12FirstReminderTime(),
                        statistics.getE3gFirstReminderTime(),
                        statistics.getPdgFirstReminderTime())
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);

        return getPushNotificationDTO(pushBeforeTime, pushAfterTime, reminderTime,
                NotificationDefineEnum.TESTING_DAY_REMINDER_MAX.getDefineId(),
                statistics.getUserId(), statistics.getTimeZone());
    }

    private JobNotificationDTO buildOvumNotification(SysNotificationTestingStatisticsEntity statistics,
                                                     Long pushBeforeTime, Long pushAfterTime) {
        return getPushNotificationDTO(pushBeforeTime, pushAfterTime,
                statistics.getProduct16FirstReminderTime(),
                NotificationDefineEnum.TESTING_DAY_REMINDER_FSH.getDefineId(),
                statistics.getUserId(), statistics.getTimeZone());
    }

    private JobNotificationDTO determineNotification(JobNotificationDTO maxNotification,
                                                     JobNotificationDTO ovumNotification, Long userId, boolean hasTestedMax, boolean hasTestedOvum) {
        // Both Max and Ovum
        if (Objects.nonNull(maxNotification) && Objects.nonNull(ovumNotification)) {
            if (hasTestedMax || hasTestedOvum) {
                return null;
            }
            if (sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX_OVUM.getDefineId(),
                    ovumNotification.getReminderTime())) {
                return null;
            }
            maxNotification.setDefineId(NotificationDefineEnum.TESTING_DAY_REMINDER_MAX_OVUM.getDefineId());
            return maxNotification;
        }

        // Only Max
        if (Objects.nonNull(maxNotification)) {
            if (hasTestedMax || sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX.getDefineId(),
                    maxNotification.getReminderTime())) {
                return null;
            }
            return maxNotification;
        }

        // Only Ovum
        if (Objects.nonNull(ovumNotification)) {
            if (hasTestedOvum || sysNotificationTestingHistoryDAO.checkExist(userId,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_FSH.getDefineId(),
                    ovumNotification.getReminderTime())) {
                return null;
            }
            return ovumNotification;
        }

        return null;
    }

    private JobNotificationDTO getPushNotificationDTO(Long pushBeforeTime, Long pushAfterTime,
                                                      Long firstReminderTime, Long defineId, Long userId, String timeZone) {
        return Optional.ofNullable(firstReminderTime)
                .filter(time -> time > pushBeforeTime && time < pushAfterTime)
                .map(time -> new JobNotificationDTO(userId, timeZone, defineId, time))
                .orElse(null);
    }

    private void push(List<JobNotificationDTO> jobNotificationDTOS, Map<Long, AppUserInfoEntity> allUserInfoMap) {
        List<SysNotificationTestingHistoryEntity> testingHistoryEntityList = new ArrayList<>();
        List<SysNotificationTestingStatisticsEntity> testingStatisticsEntityList = new ArrayList<>();
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();
        Map<FirebasePushDTO, List<Long>> saveRecordNoFirebasePushMap = new HashMap<>();

        for (JobNotificationDTO jobNotificationDTO : jobNotificationDTOS) {
            // app user info
            Long userId = jobNotificationDTO.getUserId();
            AppUserInfoEntity appUserInfo = allUserInfoMap.get(userId);
            if (appUserInfo == null) {
                continue;
            }
            // notification define
            SysNotificationDefineEntity notificationDefine = commonManager.getNotificationDefine(jobNotificationDTO.getDefineId());
            if (notificationDefine == null) {
                continue;
            }
            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // firebase push check
            boolean schedulePushFlag = 1 == userReminderInfo.getTestingScheduleFlag();
            if (!schedulePushFlag) {
                continue;
            }
            // firbase push dto
            FirebasePushDTO firebasePushDTO = FirebaseBuildUtil
                    .buildPushNotification(notificationDefine, jobManager.userRemindOpen(userId, ReminderEnum.HIDE_CONTENT_SWITCH));
            // build push map
            boolean firebasePushFlag = 1 == userReminderInfo.getRemindFlag();
            if (firebasePushFlag) {
                firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO).add(userId);
            } else {
                saveRecordNoFirebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
                saveRecordNoFirebasePushMap.get(firebasePushDTO).add(userId);
            }
            // sys_notification_testing_history
            Integer pushType = commonManager.getPushType(appUserInfo.getPlatform());
            testingHistoryEntityList.add(buildTestingHistory(jobNotificationDTO, pushType));
            // 发送后从first_reminder_time中移除
            SysNotificationTestingStatisticsEntity statisticsEntity = sysNotificationTestingStatisticsDAO.getByUserId(jobNotificationDTO.getUserId());
            testingStatisticsEntityList.add(updateTestingStatistics(jobNotificationDTO, statisticsEntity));
        }

        // save testing push history
        if (CollectionUtils.isNotEmpty(testingHistoryEntityList)) {
            sysNotificationTestingHistoryDAO.saveBatch(testingHistoryEntityList);
        }
        // update testing statistics
        if (CollectionUtils.isNotEmpty(testingStatisticsEntityList)) {
            sysNotificationTestingStatisticsDAO.updateBatchById(testingStatisticsEntityList);
        }

        // push
        log.info("-----------------------------------------");
        long[] pushCount = {0L};
        long[] noFirebasePushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        saveRecordNoFirebasePushMap.values().forEach(list -> noFirebasePushCount[0] += list.size());
        log.info("TestingDayPushSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        log.info("TestingDayPushSchedule -> Start firebase push, type size:{}, noFirebasePush count:{}", saveRecordNoFirebasePushMap.size(), pushCount[0]);

        Map<Long, PushUserInfoDTO> pushUserInfoMap = buildPushUserInfoDTO(allUserInfoMap);
        commonManager.firebasePush(firebasePushMap, pushUserInfoMap, null);
        commonManager.saveRecordNoFirebasePush(saveRecordNoFirebasePushMap, pushUserInfoMap, null);
    }

    private Map<Long, PushUserInfoDTO> buildPushUserInfoDTO(Map<Long, AppUserInfoEntity> allUserInfoMap) {
        Map<Long, PushUserInfoDTO> pushUserInfoDTOMap = new HashMap<>();
        for (Map.Entry<Long, AppUserInfoEntity> entry : allUserInfoMap.entrySet()) {
            Long userId = entry.getKey();
            AppUserInfoEntity value = entry.getValue();

            PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
            pushUserInfoDTO.setUserId(userId);
            pushUserInfoDTO.setPushToken(value.getPushToken());
            pushUserInfoDTO.setPlatform(value.getPlatform());
            pushUserInfoDTO.setTimeZone(value.getTimeZone());
            pushUserInfoDTOMap.put(userId, pushUserInfoDTO);
        }
        return pushUserInfoDTOMap;
    }
}
