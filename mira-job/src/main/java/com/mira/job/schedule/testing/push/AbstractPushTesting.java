package com.mira.job.schedule.testing.push;

import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.JobNotificationDTO;
import com.mira.job.dal.entity.master.SysNotificationTestingHistoryEntity;
import com.mira.job.dal.entity.master.SysNotificationTestingStatisticsEntity;
import com.mira.mybatis.util.UpdateEntityTimeUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * push testing common
 *
 * <AUTHOR>
 */
public abstract class AbstractPushTesting {
    protected SysNotificationTestingHistoryEntity buildTestingHistory(JobNotificationDTO jobNotificationDTO, Integer pushType) {
        SysNotificationTestingHistoryEntity testingHistoryEntity = new SysNotificationTestingHistoryEntity();
        testingHistoryEntity.setUserId(jobNotificationDTO.getUserId());
        testingHistoryEntity.setNotificationDefineId(jobNotificationDTO.getDefineId());
        testingHistoryEntity.setPushType(pushType);
        testingHistoryEntity.setReminderTime(jobNotificationDTO.getReminderTime());
        testingHistoryEntity.setReminderTimeStr(ZoneDateUtil.format(
                jobNotificationDTO.getTimeZone(), jobNotificationDTO.getReminderTime(), DatePatternConst.DATE_TIME_PATTERN));
        testingHistoryEntity.setTimeZone(jobNotificationDTO.getTimeZone());
        UpdateEntityTimeUtil.setBaseEntityTime(jobNotificationDTO.getTimeZone(), testingHistoryEntity);
        return testingHistoryEntity;
    }

    protected SysNotificationTestingStatisticsEntity updateTestingStatistics(JobNotificationDTO jobNotificationDTO,
                                                                          SysNotificationTestingStatisticsEntity statisticsEntity) {
        Long notificationDefineId = jobNotificationDTO.getDefineId();
        String e3gReminderTimes = statisticsEntity.getE3gReminderTimes();
        String hcgReminderTimes = statisticsEntity.getHcgReminderTimes();
        String pdgReminderTimes = statisticsEntity.getPdgReminderTimes();
        String product12ReminderTimes = statisticsEntity.getProduct12ReminderTimes();
        String product14ReminderTimes = statisticsEntity.getProduct14ReminderTimes();
        String product16ReminderTimes = statisticsEntity.getProduct16ReminderTimes();
        if (NotificationDefineEnum.TESTING_DAY_REMINDER_E3G.getDefineId().equals(notificationDefineId)) {
            removeFirstReminderTime(statisticsEntity, e3gReminderTimes, WandTypeEnum.E3G_LH);
        } else if (NotificationDefineEnum.TESTING_DAY_REMINDER_PDG.getDefineId().equals(notificationDefineId)) {
            removeFirstReminderTime(statisticsEntity, pdgReminderTimes, WandTypeEnum.PDG);
        } else if (NotificationDefineEnum.TESTING_DAY_REMINDER_HCG.getDefineId().equals(notificationDefineId)) {
            removeFirstReminderTime(statisticsEntity, hcgReminderTimes, WandTypeEnum.HCG);
        } else if (NotificationDefineEnum.TESTING_DAY_REMINDER_FSH.getDefineId().equals(notificationDefineId)) {
            removeFirstReminderTime(statisticsEntity, product16ReminderTimes, WandTypeEnum.FSH);
        } else if (NotificationDefineEnum.TESTING_DAY_REMINDER_HCG_QUALITATIVE.getDefineId().equals(notificationDefineId)) {
            removeFirstReminderTime(statisticsEntity, product14ReminderTimes, WandTypeEnum.HCG_QUALITATIVE);
        } else if (NotificationDefineEnum.TESTING_DAY_REMINDER_MAX.getDefineId().equals(notificationDefineId)) {
            removeFirstReminderTime(statisticsEntity, product12ReminderTimes, WandTypeEnum.LH_E3G_PDG);
        }
        UpdateEntityTimeUtil.updateBaseEntityTime(statisticsEntity.getTimeZone(), statisticsEntity);
        return statisticsEntity;
    }

    protected void removeFirstReminderTime(SysNotificationTestingStatisticsEntity statisticsEntity,
                                        String reminderTimes, WandTypeEnum wandTypeEnum) {
        List<String> oldReminderTimeList = StringListUtil.strToList(reminderTimes, ",");
        List<String> reminderTimeList = new ArrayList<>(oldReminderTimeList);
        if (!reminderTimeList.isEmpty()) {
            reminderTimeList.remove(reminderTimeList.get(0));
        }
        switch (wandTypeEnum) {
            case E3G_LH:
                statisticsEntity.setE3gReminderTimes(StringListUtil.listToString(reminderTimeList, ","));
                if (reminderTimeList.isEmpty()) {
                    statisticsEntity.setE3gFirstReminderTime(null);
                } else {
                    statisticsEntity.setE3gFirstReminderTime(
                            ZoneDateUtil.timestamp(statisticsEntity.getTimeZone(), reminderTimeList.get(0), DatePatternConst.DATE_TIME_PATTERN));
                }
                break;
            case PDG:
                statisticsEntity.setPdgReminderTimes(StringListUtil.listToString(reminderTimeList, ","));
                if (reminderTimeList.isEmpty()) {
                    statisticsEntity.setPdgReminderTimes(null);
                } else {
                    statisticsEntity.setPdgFirstReminderTime(
                            ZoneDateUtil.timestamp(statisticsEntity.getTimeZone(), reminderTimeList.get(0), DatePatternConst.DATE_TIME_PATTERN));
                }
                break;
            case HCG:
                statisticsEntity.setHcgReminderTimes(StringListUtil.listToString(reminderTimeList, ","));
                if (reminderTimeList.isEmpty()) {
                    statisticsEntity.setHcgReminderTimes(null);
                } else {
                    statisticsEntity.setHcgFirstReminderTime(
                            ZoneDateUtil.timestamp(statisticsEntity.getTimeZone(), reminderTimeList.get(0), DatePatternConst.DATE_TIME_PATTERN));
                }
                break;
            case FSH:
                statisticsEntity.setProduct16ReminderTimes(StringListUtil.listToString(reminderTimeList, ","));
                if (reminderTimeList.isEmpty()) {
                    statisticsEntity.setProduct16ReminderTimes(null);
                } else {
                    statisticsEntity.setProduct16FirstReminderTime(
                            ZoneDateUtil.timestamp(statisticsEntity.getTimeZone(), reminderTimeList.get(0), DatePatternConst.DATE_TIME_PATTERN));
                }
                break;
            case HCG_QUALITATIVE:
                statisticsEntity.setProduct14ReminderTimes(StringListUtil.listToString(reminderTimeList, ","));
                if (reminderTimeList.isEmpty()) {
                    statisticsEntity.setProduct14ReminderTimes(null);
                } else {
                    statisticsEntity.setProduct14FirstReminderTime(
                            ZoneDateUtil.timestamp(statisticsEntity.getTimeZone(), reminderTimeList.get(0), DatePatternConst.DATE_TIME_PATTERN));
                }
                break;
            case LH_E3G_PDG:
                statisticsEntity.setProduct12ReminderTimes(StringListUtil.listToString(reminderTimeList, ","));
                if (reminderTimeList.isEmpty()) {
                    statisticsEntity.setProduct12ReminderTimes(null);
                } else {
                    statisticsEntity.setProduct12FirstReminderTime(
                            ZoneDateUtil.timestamp(statisticsEntity.getTimeZone(), reminderTimeList.get(0), DatePatternConst.DATE_TIME_PATTERN));
                }
                break;
            default:
                break;
        }
    }
}
