package com.mira.job.schedule.subscribe;

import com.google.common.collect.Lists;
import com.mira.job.dal.dao.master.AdminNotificationSubscriptionDAO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * subscribe schedule
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SubscribeSchedule {
    @Resource
    private AdminNotificationSubscriptionDAO subscriptionDAO;

    private static final String FILE_PATH = "/root/micro_service/mira-job/ACTIVE.csv";

    @XxlJob("subscribeHandler")
    public void subscribeHandler() throws Exception {
        log.info("subscribeHandler: start execute");
        run();
        log.info("subscribeHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() throws Exception {
        int emailIndex = -1;
        int statusIndex = -1;

        Set<String> emailSet = new HashSet<>();
        List<String> lineList = Files.readAllLines(Path.of(FILE_PATH));
        String[] titleArr = lineList.get(0).split(",");
        int titleLen = titleArr.length;
        for (int i = 0; i < titleLen; i++) {
            if (titleArr[i].equalsIgnoreCase("CUSTOMER_EMAIL")) {
                emailIndex = i;
            }
            if (titleArr[i].equalsIgnoreCase("STATUS")) {
                statusIndex = i;
            }
        }

        int totalSize = lineList.size();
        for (int i = 1; i < totalSize; i++) {
            String[] lineArr = lineList.get(i).split(",");
            if ("ACTIVE".equalsIgnoreCase(lineArr[statusIndex])) {
                emailSet.add(lineArr[emailIndex]);
            }
        }
        List<String> emailList = new ArrayList<>(emailSet);

        try {
            // 创建临时表
            subscriptionDAO.createTempTable();
            // 插入数据到临时表
            List<List<String>> emailPartition = Lists.partition(emailList, 1000);
            for (List<String> list : emailPartition) {
                subscriptionDAO.batchInsertTempTable(list);
            }
            // 获取用户id列表
            List<Long> userIds = subscriptionDAO.getUserIdsByTempTable();
            // 清空订阅表
            subscriptionDAO.truncate();
            // 写入订阅表
            List<List<Long>> userIdPartition = Lists.partition(userIds, 1000);
            for (List<Long> list : userIdPartition) {
                subscriptionDAO.batchInsert(list);
            }
        } catch (Exception e) {
            log.error("插入临时表发生错误", e);
        } finally {
            // 删除临时表
            subscriptionDAO.dropTempTable();
        }
    }
}
