package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppUserDiarySymptomsEntity;
import com.mira.job.dal.mapper.master.AppUserDiarySymptomsMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user_diary_symptoms DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserDiarySymptomsDAO extends ServiceImpl<AppUserDiarySymptomsMapper, AppUserDiarySymptomsEntity> {
    @DS(DataSourceName.SLAVE)
    public List<AppUserDiarySymptomsEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppUserDiarySymptomsEntity>lambdaQuery()
                .eq(AppUserDiarySymptomsEntity::getUserId, userId));
    }

    @DS(DataSourceName.SLAVE)
    public List<AppUserDiarySymptomsEntity> listByUserIds(List<Long> userIds) {
        return list(Wrappers.<AppUserDiarySymptomsEntity>lambdaQuery()
                            .in(AppUserDiarySymptomsEntity::getUserId, userIds));
    }

    @DS(DataSourceName.SLAVE)
    public AppUserDiarySymptomsEntity getByUserIdAndDayStr(Long userId, String dayStr) {
        return getOne(Wrappers.<AppUserDiarySymptomsEntity>lambdaQuery()
                .eq(AppUserDiarySymptomsEntity::getUserId, userId)
                .eq(AppUserDiarySymptomsEntity::getDiaryDayStr, dayStr)
                .last("limit 1"));
    }

    @DS(DataSourceName.SLAVE)
    public long getCount() {
        return count();
    }
}
