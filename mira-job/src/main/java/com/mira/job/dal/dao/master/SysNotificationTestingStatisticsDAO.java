package com.mira.job.dal.dao.master;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.entity.master.SysNotificationTestingStatisticsEntity;
import com.mira.job.dal.mapper.master.SysNotificationTestingStatisticsMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class SysNotificationTestingStatisticsDAO extends ServiceImpl<SysNotificationTestingStatisticsMapper, SysNotificationTestingStatisticsEntity> {
    public SysNotificationTestingStatisticsEntity getByUserId(Long userId) {
        return getOne(Wrappers.<SysNotificationTestingStatisticsEntity>lambdaQuery()
                .eq(SysNotificationTestingStatisticsEntity::getUserId, userId));
    }

    public List<SysNotificationTestingStatisticsEntity> listBeforePushStartTime(Long pushStartTime) {
        return list(Wrappers.<SysNotificationTestingStatisticsEntity>lambdaQuery()
                .lt(SysNotificationTestingStatisticsEntity::getE3gFirstReminderTime, pushStartTime)
                .or().lt(SysNotificationTestingStatisticsEntity::getHcgFirstReminderTime, pushStartTime)
                .or().lt(SysNotificationTestingStatisticsEntity::getPdgFirstReminderTime, pushStartTime)
                .or().lt(SysNotificationTestingStatisticsEntity::getProduct12FirstReminderTime, pushStartTime)
                .or().lt(SysNotificationTestingStatisticsEntity::getProduct14FirstReminderTime, pushStartTime)
                .or().lt(SysNotificationTestingStatisticsEntity::getProduct16FirstReminderTime, pushStartTime));
    }

    public List<SysNotificationTestingStatisticsEntity> listBetweenPushTime(Long pushBeforeTime, Long pushAfterTime) {
        return list(Wrappers.<SysNotificationTestingStatisticsEntity>lambdaQuery()
                .gt(SysNotificationTestingStatisticsEntity::getE3gFirstReminderTime, pushBeforeTime).lt(SysNotificationTestingStatisticsEntity::getE3gFirstReminderTime, pushAfterTime)
                .or().gt(SysNotificationTestingStatisticsEntity::getHcgFirstReminderTime, pushBeforeTime).lt(SysNotificationTestingStatisticsEntity::getHcgFirstReminderTime, pushAfterTime)
                .or().gt(SysNotificationTestingStatisticsEntity::getPdgFirstReminderTime, pushBeforeTime).lt(SysNotificationTestingStatisticsEntity::getPdgFirstReminderTime, pushAfterTime)
                .or().gt(SysNotificationTestingStatisticsEntity::getProduct12FirstReminderTime, pushBeforeTime).lt(SysNotificationTestingStatisticsEntity::getProduct12FirstReminderTime, pushAfterTime)
                .or().gt(SysNotificationTestingStatisticsEntity::getProduct14FirstReminderTime, pushBeforeTime).lt(SysNotificationTestingStatisticsEntity::getProduct14FirstReminderTime, pushAfterTime)
                .or().gt(SysNotificationTestingStatisticsEntity::getProduct16FirstReminderTime, pushBeforeTime).lt(SysNotificationTestingStatisticsEntity::getProduct16FirstReminderTime, pushAfterTime));
    }
}
