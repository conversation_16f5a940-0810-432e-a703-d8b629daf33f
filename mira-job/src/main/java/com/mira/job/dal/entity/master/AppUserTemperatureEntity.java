package com.mira.job.dal.entity.master;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 用户体温温度表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_temperature")
public class AppUserTemperatureEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 时间戳（年月日时分秒）
     */
    private Long tempTimestamp;

    /**
     * 温度测试时间(天)
     */
    private String tempDay;

    /**
     * 温度测试时间
     */
    private String tempTime;

    /**
     * 温度
     */
    private BigDecimal tempC;

    /**
     * 温度
     */
    private BigDecimal tempF;

    /**
     * 数据类型:0:auto;1manual(default1)
     */
    private Integer autoFlag;

    /**
     * 默认0，1表示GENIAL厂商的温度计
     */
    private Integer type;

    /**
     * 默认0表述数据正常；如果值为非0则结合type使用，
     * 例如type=1，则modeError 01,02,03,04
     * 分别表示量测温度过高, >= 42.00°C；量测温度过低, <32.00°C；电池低电压；ERR错误
     */
    private String modeError;

    /**
     * 系统操作备注
     */
    private String sysNote;
}
