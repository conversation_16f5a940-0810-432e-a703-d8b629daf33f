package com.mira.job.dal.entity.master;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 好用户（算法提供数据）
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("report_good_users")
public class ReportGoodUsersEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 是否亚马逊用户 0不是，1是
     */
    private Integer isAmazon;
}
