package com.mira.job.dal.entity.hub;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户每日用药记录
 * <p>
 * from app_user_diary_medications
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("hub_user_daily_medication")
public class HubUserDailyMedicationEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String hubId;


    /**
     * 日记时间（年月日）
     */
    private String dateStr;

    /**
     * 用药记录jsonArray
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String medications;

    /**
     * 修改时间
     */
    private Long modifyTime;


    /**
     * 同步时间
     */
    private Long syncTime;

}
