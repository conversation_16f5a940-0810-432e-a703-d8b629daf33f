package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.ReportGoodUsersEntity;
import com.mira.job.dal.mapper.master.ReportGoodUsersMapper;
import org.springframework.stereotype.Repository;

/**
 * report_good_users DAO
 *
 * <AUTHOR>
 */
@DS(DataSourceName.SLAVE)
@Repository
public class ReportGoodUsersDAO extends ServiceImpl<ReportGoodUsersMapper, ReportGoodUsersEntity> {
    public long getCount() {
        return count(Wrappers.emptyWrapper());
    }
}
