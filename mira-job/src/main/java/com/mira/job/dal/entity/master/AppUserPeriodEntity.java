package com.mira.job.dal.entity.master;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户经期表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_period")
public class AppUserPeriodEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户输入的经期的所有日期 json 数据
     */
    private String periods;

    /**
     * 计算得到的经期信息
     */
    private String periodData;

    /**
     * 平均经期长度
     */
    private Integer avgLenPeriod;

    /**
     * 平均周期长度
     */
    private Integer avgLenCycle;

    /**
     * 经期标识：0：正常；-1：I don't know
     */
    private Integer periodFlag;

    /**
     * 周期标识：0：正常；-1：I don't know;-2 varies often ；-3  I don't know + varies often
     */
    private Integer cycleFlag;

    /**
     * cycleFlag 和 periodFlag 的开关。默认0关闭；1打开
     */
    private Integer switchFlag;

    /**
     * 是否新用户：0新用户，1老用户
     */
    private Integer fresh;

    /**
     * 经期强制截断点（遇到该点时立即结束当前cycle的经期计算，并将该天的下一天作为经期结束日）
     */
    private String cutPoints;
}
