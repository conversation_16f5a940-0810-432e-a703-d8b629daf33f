package com.mira.job.dal.entity.master;

import com.baomidou.mybatisplus.annotation.*;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户当日测试提醒统计表
 */
@Getter
@Setter
@TableName("sys_notification_testing_statistics")
public class SysNotificationTestingStatisticsEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long e3gFirstReminderTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String e3gReminderTimes;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long pdgFirstReminderTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String pdgReminderTimes;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long hcgFirstReminderTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String hcgReminderTimes;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long product12FirstReminderTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String product12ReminderTimes;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long product14FirstReminderTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String product14ReminderTimes;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long product16FirstReminderTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String product16ReminderTimes;

    private String extraInfo;
}
